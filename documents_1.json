[{"tips": ["Highlight relevant internships and projects in your application", "Demonstrate your problem-solving skills during interviews"], "salary": "$99,500 - $209,000", "company": "Amazon.com Services LLC", "location": "USA, NJ, New Jersey; USA, CA, Santa Clara; USA, WA, Bellevue; USA, WA, Seattle; USA, VA, Arlington; USA, NY, New York; USA, CA, East Palo Alto", "position": "Front-End Engineer, 2025 SPC", "companyValues": ["Diversity and inclusion", "Equal opportunity employer"], "applicationUrl": "https://www.amazon.jobs/applicant/jobs/2874384/apply?cmpid=SPLICX0248M&ss=paid&utm_campaign=cxro&utm_content=job_posting&utm_medium=social_media&utm_source=linkedin.com", "requiredSkills": ["Basic proficiency in JavaScript, HTML, and CSS", "Proficiency in at least one object-oriented programming language (e.g., JavaScript, Python, C#)", "Understanding of computer science fundamentals (e.g., data structures & algorithms, OO design)"], "keyQualifications": ["Customer obsession", "Strong problem-solving skills"], "requiredExperience": "Previous technical internships in a related field (e.g., Front-End, Full Stack, Web Development)", "jobResponsibilities": ["Deliver software components and improvements to software features", "Work with stakeholders and product partners to deliver high-quality UX", "Take feature specifications and deliver working code that is maintainable and extendable", "Help provide operational support for your team", "Participate in code reviews and daily stand-ups", "Collaborate with your team to positively impact software quality"], "potentialChallenges": ["Identifying and resolving root causes of issues", "Maintaining high-quality UX under tight deadlines"], "estimatedSalaryRange": "$99,500 - $209,000"}]