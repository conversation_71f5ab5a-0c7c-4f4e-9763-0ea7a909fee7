# JobCraft - Craft Your Career with AI

JobCraft is a comprehensive full-stack Next.js application that empowers job seekers to manage their job applications and create personalized, AI-powered outreach content. It provides intelligent tools for resume management, application tracking, and content generation to craft the perfect job application materials.

## 🚀 Features

### **Authentication & User Management**

-   🔐 Secure authentication with Clerk
-   👤 Comprehensive user profile management
-   🛡️ Protected routes for authenticated users
-   🔄 Seamless sign-in/sign-up flow

### **Resume & Profile Management**

-   📄 Upload and manage multiple resume versions (PDF, DOC, DOCX)
-   🤖 AI-powered resume parsing to extract key information
-   📋 Set default resume for applications
-   🗑️ Robust resume deletion with UploadThing storage cleanup
-   📊 Resume analytics and insights

### **AI-Powered Content Generation**

-   📝 **Cover Letters**: Personalized, role-specific cover letters
-   💼 **LinkedIn Messages**: Professional connection requests and outreach
-   📧 **Cold Emails**: Compelling outreach emails to recruiters
-   ❓ **Application Questions**: Smart responses to common application questions
-   🎯 Context-aware content using job descriptions and resume data

### **Application Tracking System**

-   📊 Comprehensive dashboard with application statistics
-   🔍 Advanced filtering and sorting capabilities
-   📈 Application status tracking and analytics
-   📅 Timeline view of application history
-   🔗 Application linking and management

### **Advanced Features**

-   🌐 Web scraping for job posting analysis
-   📱 Responsive design with mobile-first approach
-   🎨 Modern UI with dark/light theme support
-   ⚡ Real-time loading states and animations
-   📈 Performance optimized with React Query
-   🔍 Comprehensive error handling and logging

## 🛠️ Tech Stack

### **Frontend**

-   **Next.js 15** with App Router
-   **React 18** with modern hooks
-   **TypeScript** for type safety
-   **Tailwind CSS** for styling
-   **Shadcn UI** for components
-   **Framer Motion** for animations
-   **React Query** for data fetching
-   **React Hook Form** for form management

### **Backend**

-   **Next.js API Routes**
-   **Neon Database** (PostgreSQL)
-   **Drizzle ORM** for database interactions
-   **UploadThing** for file storage
-   **Comprehensive API logging** for monitoring

### **AI & External Services**

-   **Google Gemini AI** for content generation
-   **OpenAI GPT** for advanced text processing
-   **Firecrawl** for web scraping
-   **PDF parsing** for resume analysis

### **Authentication & Infrastructure**

-   **Clerk** for user authentication and management
-   **Vercel** ready deployment
-   **ESLint & Prettier** for code quality

## 🏗️ Getting Started

### Prerequisites

-   Node.js 18+ and npm
-   Neon Database account
-   Clerk account
-   Google AI Studio API key (Gemini)
-   OpenAI API key (optional)
-   UploadThing account

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
# Database
DATABASE_URL="your-neon-postgres-connection-string"
DIRECT_URL="your-neon-direct-connection-string"

# Authentication (Clerk)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="your-clerk-publishable-key"
CLERK_SECRET_KEY="your-clerk-secret-key"

# AI Services
GOOGLE_AI_API_KEY="your-gemini-api-key"
OPENAI_API_KEY="your-openai-api-key"

# File Storage (UploadThing)
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"

# Web Scraping (Firecrawl)
FIRECRAWL_API_KEY="your-firecrawl-api-key"

# Analytics (Optional)
NEXT_PUBLIC_GA_ID="your-google-analytics-id"

# Logging Level (Optional)
LOG_LEVEL="INFO"
```

### Installation

1. **Clone the repository:**

    ```bash
    git clone https://github.com/BeLazy167/JobCraft.git
    cd JobCraft
    ```

2. **Install dependencies:**

    ```bash
    npm install
    ```

3. **Set up the database schema:**

    ```bash
    npm run db:generate
    npm run db:push
    ```

4. **Run the development server:**

    ```bash
    npm run dev
    ```

5. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📊 Database Schema

### Core Tables

-   **Users**: Linked to Clerk user IDs with profile information
-   **Resumes**: Multiple resume versions per user with UploadThing file storage
-   **Job Applications**: Comprehensive application tracking with status management
-   **Generated Content**: AI-generated cover letters, emails, and messages

### Features

-   🔄 Proper foreign key relationships
-   🗑️ Cascade deletion for data integrity
-   📝 Comprehensive logging for all operations
-   ⚡ Optimized indexes for performance

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Prepare your repository:**

    ```bash
    git push origin main
    ```

2. **Deploy to Vercel:**

    - Import your GitHub repository in Vercel
    - Configure all environment variables
    - Deploy with one click

3. **Post-deployment:**
    - Set up your custom domain
    - Configure analytics
    - Monitor application performance

### Manual Deployment

The application supports deployment on any Node.js hosting platform that supports Next.js applications.

## 📚 Documentation

-   [API Documentation](./docs/api/README.md)
-   [Architecture Overview](./docs/architecture/README.md)
-   [Features Documentation](./docs/features/README.md)
-   [Setup Guide](./docs/setup/environment.md)

## 🔧 Development

### Available Scripts

-   `npm run dev` - Start development server
-   `npm run build` - Build for production
-   `npm run start` - Start production server
-   `npm run lint` - Run ESLint
-   `npm run db:generate` - Generate database migrations
-   `npm run db:push` - Push schema to database
-   `npm run db:studio` - Open Drizzle Studio

### Key Features for Developers

-   🔍 Comprehensive error handling and logging
-   📝 TypeScript for type safety
-   🧪 Ready for testing framework integration
-   📦 Modular component architecture
-   🎯 Clean API design

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

-   **Clerk** for authentication services
-   **Neon** for serverless PostgreSQL
-   **UploadThing** for file storage
-   **Google** for Gemini AI
-   **Shadcn** for beautiful UI components

---

**JobCraft** - Empowering your career journey with AI-powered job application tools. 🚀
