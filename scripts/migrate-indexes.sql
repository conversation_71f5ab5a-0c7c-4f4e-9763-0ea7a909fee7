-- JobCraft Database Index Migration
-- This script adds performance indexes to all tables

BEGIN;

-- Resumes table indexes
CREATE INDEX IF NOT EXISTS resumes_user_id_idx ON resumes(user_id);
CREATE INDEX IF NOT EXISTS resumes_is_default_idx ON resumes(is_default);
CREATE INDEX IF NOT EXISTS resumes_created_at_idx ON resumes(created_at);

-- Job Applications table indexes
CREATE INDEX IF NOT EXISTS applications_user_id_idx ON job_applications(user_id);
CREATE INDEX IF NOT EXISTS applications_status_idx ON job_applications(status);
CREATE INDEX IF NOT EXISTS applications_created_at_idx ON job_applications(created_at);
CREATE INDEX IF NOT EXISTS applications_company_position_idx ON job_applications(company, position);
CREATE INDEX IF NOT EXISTS applications_user_status_idx ON job_applications(user_id, status);
CREATE INDEX IF NOT EXISTS applications_applied_date_idx ON job_applications(applied_date);

-- Generated Documents table indexes
CREATE INDEX IF NOT EXISTS generated_docs_user_id_idx ON generated_documents(user_id);
CREATE INDEX IF NOT EXISTS generated_docs_application_id_idx ON generated_documents(application_id);
CREATE INDEX IF NOT EXISTS generated_docs_created_at_idx ON generated_documents(created_at);

-- Shortened URLs table indexes
CREATE INDEX IF NOT EXISTS shortened_urls_short_code_idx ON shortened_urls(short_code);
CREATE INDEX IF NOT EXISTS shortened_urls_user_id_idx ON shortened_urls(user_id);
CREATE INDEX IF NOT EXISTS shortened_urls_created_at_idx ON shortened_urls(created_at);
CREATE INDEX IF NOT EXISTS shortened_urls_expires_at_idx ON shortened_urls(expires_at);

-- Analyze tables for query optimization
ANALYZE resumes;
ANALYZE job_applications;
ANALYZE generated_documents;
ANALYZE shortened_urls;

COMMIT;

-- Verify indexes were created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('resumes', 'job_applications', 'generated_documents', 'shortened_urls')
ORDER BY tablename, indexname; 