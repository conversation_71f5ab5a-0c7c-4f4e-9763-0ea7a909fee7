# JobCraft Documentation

Welcome to the JobCraft documentation. This comprehensive documentation provides detailed information about the JobCraft application, its features, architecture, setup procedures, and usage guidelines.

## 📚 Table of Contents

### **API Documentation** 📡

-   [API Overview](./api/README.md)
-   [Authentication Middleware](./api/authentication-middleware.md)
-   [Gemini AI Integration](./api/gemini-ai-integration.md)
-   [PDF.co Integration](./api/pdf-co-integration.md)

### **Features Documentation** ⚡

-   [Features Overview](./features/README.md)
-   [Resume Parsing](./features/resume-parsing.md)
-   [Application Tracking](./features/application-tracking.md)
-   [Application Question Assistant](./features/application-question-assistant.md)
-   [React Query Implementation](./features/react-query-implementation.md)
-   [Mobile UI Enhancements](./features/mobile-ui-enhancements.md)
-   [Loading Animations](./features/loading-animations.md)
-   [Defensive Programming](./features/defensive-programming.md)

### **Architecture Documentation** 🏗️

-   [Architecture Overview](./architecture/README.md)
-   [System Overview](./architecture/system-overview.md)
-   [Component Structure](./architecture/component-structure.md)
-   [Database Schema](./architecture/database-schema.md)
-   [Design System](./architecture/design-system.md)
-   [Theming System](./architecture/theming.md)
-   [UI Components](./architecture/ui-components.md)

### **Setup Guide** 🛠️

-   [Environment Setup](./setup/environment.md)

## 🚀 Quick Start

1. **Read the Setup Guide**: Start with the [Environment Setup](./setup/environment.md) to configure your development environment.

2. **Understand the Architecture**: Review the [Architecture Overview](./architecture/README.md) to understand the system design.

3. **Explore Features**: Check out the [Features Documentation](./features/README.md) to learn about available functionality.

4. **API Integration**: Refer to the [API Documentation](./api/README.md) for backend integration details.

## 🔧 Key Technologies Covered

-   **Next.js 15** with App Router
-   **Neon Database** with Drizzle ORM
-   **UploadThing** for file storage
-   **Clerk** for authentication
-   **Google Gemini AI** for content generation
-   **React Query** for data management
-   **Tailwind CSS** with Shadcn UI

## 📝 Documentation Standards

This documentation follows these principles:

-   **Comprehensive**: Covers all major features and components
-   **Up-to-date**: Regularly updated to reflect current codebase
-   **Practical**: Includes code examples and real-world usage
-   **Accessible**: Clear explanations for developers of all levels

## 🤝 Contributing to Documentation

We welcome contributions to improve this documentation:

1. **Identify gaps**: Look for missing or outdated information
2. **Follow format**: Use consistent markdown formatting and structure
3. **Add examples**: Include practical code examples where helpful
4. **Test accuracy**: Ensure all instructions and code work correctly

## 📞 Need Help?

-   Check the relevant documentation section first
-   Review the main [README](../README.md) for basic setup
-   Look at the [Architecture documentation](./architecture/README.md) for system understanding

---

**Last Updated**: January 2025  
**Version**: 1.0.0  
**Maintained by**: JobCraft Development Team
