# UI Components and Design System

This document outlines HireRizz's UI component library and design system, providing guidelines for consistent interface design.

## Component Library

HireRizz uses a combination of custom components and the [shadcn/ui](https://ui.shadcn.com/) component library, which provides accessible, customizable components built on Radix UI.

### Key Components

#### Button

```tsx
<Button variant="default" size="default">
    Button Text
</Button>
```

**Variants:**

-   `default`: Primary action button
-   `destructive`: For delete/cancel actions
-   `outline`: Secondary action button
-   `ghost`: Minimal visual styling
-   `link`: Button that appears as a link

**Sizes:**

-   `default`: Standard size
-   `sm`: Small button
-   `lg`: Large button
-   `icon`: Square button for icons

#### CyberpunkText

```tsx
<CyberpunkText
    text="HireRizz"
    gradient="primary"
    className="text-xl font-bold"
/>
```

**Gradient Options:**

-   `primary`: Royal purple to magenta
-   `secondary`: <PERSON><PERSON> to light blue
-   `accent`: Magenta to purple
-   `coral-amber`: Coral red to amber

#### CyberpunkBackground

```tsx
<CyberpunkBackground className="p-6 rounded-xl">
    <h2>Content with cyberpunk styling</h2>
</CyberpunkBackground>
```

Features dynamic adjustments based on theme with glowing elements.

#### Card

```tsx
<Card>
    <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card description here</CardDescription>
    </CardHeader>
    <CardContent>
        <p>Card content</p>
    </CardContent>
    <CardFooter>
        <Button>Action</Button>
    </CardFooter>
</Card>
```

#### Dialog

```tsx
<Dialog>
    <DialogTrigger>Open Dialog</DialogTrigger>
    <DialogContent>
        <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog description</DialogDescription>
        </DialogHeader>
        <DialogFooter>
            <Button>Confirm</Button>
        </DialogFooter>
    </DialogContent>
</Dialog>
```

#### Form Controls

```tsx
<Form {...form}>
    <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
            <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormDescription>Enter your email address.</FormDescription>
                <FormMessage />
            </FormItem>
        )}
    />
</Form>
```

## Design Patterns

### Layout Structure

HireRizz uses a consistent layout structure:

```tsx
<div className="min-h-screen bg-background">
    {/* Header component */}
    <Header />

    {/* Main content */}
    <main className="container mx-auto py-6 space-y-8">
        {/* Page content */}
    </main>

    {/* Footer component */}
    <Footer />
</div>
```

### Component Spacing

Consistent spacing using Tailwind's spacing scale:

-   `space-y-2`: Extra small spacing (0.5rem)
-   `space-y-4`: Small spacing (1rem)
-   `space-y-6`: Medium spacing (1.5rem)
-   `space-y-8`: Large spacing (2rem)
-   `space-y-12`: Extra large spacing (3rem)

### Grid and Responsive Layouts

Use Tailwind's grid system for complex layouts:

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {/* Grid items */}
</div>
```

## Typography

### Font Family

-   **Main Font**: Inter (sans-serif)
-   **Special Displays**: Space Grotesk (for cyberpunk elements)

### Font Sizes

-   `text-xs`: Extra small text (0.75rem)
-   `text-sm`: Small text (0.875rem)
-   `text-base`: Base text size (1rem)
-   `text-lg`: Large text (1.125rem)
-   `text-xl`: Extra large text (1.25rem)
-   `text-2xl`: Heading level (1.5rem)
-   `text-3xl`: Large heading (1.875rem)
-   `text-4xl`: Extra large heading (2.25rem)

### Typography Combinations

```tsx
<h1 className="text-3xl font-bold tracking-tight">Page Title</h1>
<h2 className="text-2xl font-semibold">Section Heading</h2>
<h3 className="text-xl font-medium">Subsection Heading</h3>
<p className="text-base text-muted-foreground">Regular paragraph text</p>
```

## Icons

HireRizz uses [Lucide React](https://lucide.dev/) for icons:

```tsx
import { Home, Search, Bell, Settings } from "lucide-react";

// Usage
<Button size="icon">
    <Settings className="h-4 w-4" />
</Button>;
```

## Animations and Transitions

### Page Transitions

```tsx
<motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.3 }}
>
    {/* Page content */}
</motion.div>
```

### Component Transitions

```tsx
<div className="transition-all duration-300 hover:scale-105">
    {/* Component with hover effect */}
</div>
```

## Accessibility Guidelines

1. **Semantic HTML**: Use appropriate HTML elements (`button`, `nav`, `main`, etc.)
2. **ARIA attributes**: Add when necessary, but prefer semantic HTML
3. **Keyboard navigation**: Ensure all interactive elements are keyboard accessible
4. **Focus styles**: Maintain visible focus indicators
5. **Color contrast**: Minimum 4.5:1 ratio for normal text
6. **Screen reader text**: Include `sr-only` text for icons

```tsx
<button aria-label="Settings">
    <Settings className="h-4 w-4" />
    <span className="sr-only">Settings</span>
</button>
```

## Form Design Patterns

### Input Validation

```tsx
<FormField
    control={form.control}
    name="email"
    rules={{ required: "Email is required" }}
    render={({ field, fieldState }) => (
        <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
                <Input
                    {...field}
                    className={fieldState.error ? "border-destructive" : ""}
                />
            </FormControl>
            <FormMessage />
        </FormItem>
    )}
/>
```

### Loading States

```tsx
<Button disabled={isLoading}>
    {isLoading ? (
        <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading...
        </>
    ) : (
        "Submit"
    )}
</Button>
```

## Design Resources

-   **Design System**: Figma design system (link to internal resource)
-   **Icon Library**: Lucide React (https://lucide.dev/)
-   **Color Reference**: See the [Theming documentation](./theming.md)

## Testing UI Components

When implementing new UI components:

1. Test across all breakpoints (mobile, tablet, desktop)
2. Verify both light and dark mode appearance
3. Test keyboard navigation and screen reader compatibility
4. Check for layout shifts or overflow issues
5. Validate against design specifications

## Adding New Components

When adding new components to the design system:

1. Maintain consistency with existing components
2. Follow naming conventions (`ButtonVariant`, `CardSize`, etc.)
3. Ensure component is accessible
4. Document props and usage examples
5. Add to this documentation

## UI Components

### New Components

#### Text Generate Effect

-   Location: `src/components/ui/text-generate-effect.tsx`
-   A new text animation component that provides a generative text effect
-   Used for enhancing user experience with animated text transitions

#### Preloader Particles

-   Location: `src/components/preloader-particles.tsx`
-   Enhanced loading animation with particle effects
-   TypeScript definitions added in `preloader-particles.d.ts`
-   Provides an engaging loading state visualization

### Updated Components

#### Dashboard Header

-   Location: `src/components/dashboard-header.tsx`
-   Improved header component with enhanced functionality
-   Updated styling and layout improvements

#### Loading Client

-   Location: `src/components/loading-client.tsx`
-   Optimized loading states
-   Improved performance and visual feedback

### Removed Components

-   SEO Component (`seo.tsx`) - Functionality integrated directly into page metadata
-   Structured Data Component - Moved to a more modular approach

### Styling Updates

-   New Tailwind configuration added (`tailwind.config.js`)
-   Enhanced theming system
-   Additional utility classes for improved component styling

### Best Practices

-   Components follow TypeScript strict typing
-   Modular design for better reusability
-   Performance optimized loading states
-   Mobile-first responsive design
