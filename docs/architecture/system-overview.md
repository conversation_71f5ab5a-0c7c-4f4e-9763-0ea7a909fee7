# System Architecture Overview

This document provides a high-level overview of the HireRizz system architecture, including its components, data flow, and integration points.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client (Next.js)                         │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │  React      │  │  Framer     │  │  UI Components          │  │
│  │  Query      │  │  Motion     │  │  (shadcn/ui, Aceternity)│  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API Routes (Next.js)                     │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│  Resumes    │Applications │    AI       │ UploadThing │  Auth   │
└──────┬──────┴──────┬──────┴──────┬──────┴──────┬──────┴────┬────┘
       │             │             │             │           │
       ▼             ▼             ▼             ▼           ▼
┌──────────┐  ┌────────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐
│ Database │  │ PDF.co API │  │Gemini AI│  │UploadThing│ │  Clerk  │
└──────────┘  └────────────┘  └─────────┘  └─────────┘  └─────────┘
```

## Core Components

### Frontend

-   **Next.js Application**: Server-side rendered React application
-   **React Components**: UI components for different features
-   **TailwindCSS**: Utility-first CSS framework for styling
-   **shadcn/ui**: Component library built on Radix UI
-   **React Query**: Data fetching and state management library
-   **Framer Motion**: Animation library for enhanced UI
-   **Aceternity UI**: Inspiration for modern UI components

### Backend

-   **Next.js API Routes**: Serverless API endpoints
-   **Drizzle ORM**: Database ORM for PostgreSQL
-   **Clerk**: Authentication and user management
-   **UploadThing**: File upload and storage

### External Services

-   **PDF.co API**: PDF text extraction
-   **Google Gemini AI**: AI-powered text processing and generation
-   **PostgreSQL**: Database for storing application data

## Data Flow

### Resume Upload and Parsing

1. User uploads a resume file via the UI
2. File is processed by UploadThing and stored
3. API route triggers resume parsing
4. Text is extracted from the file (PDF.co for PDFs, Mammoth for DOCX)
5. Extracted text is sent to Gemini AI for parsing
6. Structured data is stored in the database
7. UI displays the parsed information

```sequence
User->UI: Upload resume
UI->UploadThing: Send file
UploadThing->UI: Return file URL
UI->API: Trigger parsing
API->UploadThing: Fetch file
UploadThing->API: Return file
API->PDF.co/Mammoth: Extract text
PDF.co/Mammoth->API: Return text
API->Gemini AI: Parse text
Gemini AI->API: Return structured data
API->Database: Store parsed data
API->UI: Return success
UI->User: Display parsed resume
```

### Data Fetching with React Query

1. Component mounts and triggers a React Query hook
2. If data is in cache and not stale, it's returned immediately
3. In parallel, a background fetch is triggered to refresh data
4. API route handles the request and returns data
5. React Query updates the cache and component state
6. UI reflects the latest data

```sequence
Component->React Query: Request data
React Query->Component: Return cached data (if available)
React Query->API: Fetch fresh data
API->Database: Query data
Database->API: Return data
API->React Query: Return response
React Query->Cache: Update cache
React Query->Component: Update with fresh data
Component->UI: Render updated data
```

### Job Application Creation

1. User creates a new job application
2. Application details are stored in the database
3. User can generate content based on resume and job details
4. Generated content is stored with the application

### Content Generation

1. User requests content generation (cover letter, etc.)
2. System retrieves resume data and job details
3. Data is sent to Gemini AI for content generation
4. Generated content is returned to the user and stored

### Application Question Assistant

1. User navigates to the application question tab
2. User asks a question or selects from suggested questions
3. System sends the question along with job details and resume data to Gemini AI
4. AI generates a personalized answer based on the context
5. Answer is displayed to the user and stored in the chat history

## Database Schema

The database uses PostgreSQL with Drizzle ORM. Key tables include:

-   **users**: User information (managed by Clerk)
-   **resumes**: Resume metadata and parsed content
-   **applications**: Job application details
-   **generatedDocuments**: Generated content for applications, including cover letters, LinkedIn messages, cold emails, and chat history

## Integration Points

### Clerk Authentication

-   User authentication and management
-   Protected API routes and pages
-   User profile information

### UploadThing

-   File upload handling
-   File storage and retrieval
-   File type validation

### PDF.co API

-   PDF text extraction
-   Maintains text formatting for better parsing

### Google Gemini AI

-   Resume parsing
-   Content generation
-   Job analysis

### React Query

-   Data fetching and caching
-   Server state management
-   Optimistic updates
-   Background refetching

## UI Architecture

HireRizz uses a component-based UI architecture with several key features:

### Responsive Design

-   Mobile-first approach with responsive breakpoints
-   Fluid layouts that adapt to different screen sizes
-   Optimized touch targets for mobile devices

### Animation and Interaction

-   Framer Motion for smooth animations
-   Hover effects and transitions
-   Loading states and skeletons

### Component Hierarchy

-   Core UI components (buttons, inputs, etc.)
-   Layout components (containers, grids)
-   Feature-specific components (resume cards, application forms)
-   Page components

### Server/Client Component Strategy

-   Server components for static content and data fetching
-   Client components for interactive elements
-   Careful boundary management to avoid hydration issues

## Deployment Architecture

HireRizz is designed to be deployed on Vercel with the following configuration:

-   **Frontend**: Vercel Edge Network
-   **API Routes**: Vercel Serverless Functions
-   **Database**: Vercel Postgres or external PostgreSQL provider
-   **File Storage**: UploadThing Cloud Storage

## Security Considerations

-   **Authentication**: All routes are protected by Clerk authentication
-   **Authentication Middleware**: Standardized middleware functions (`withAuth` and `withServerAuth`) ensure consistent authentication checks across API routes
-   **API Keys**: All external API keys are stored as environment variables
-   **File Access**: Files are only accessible to their owners
-   **Data Validation**: All user inputs are validated before processing
-   **Error Handling**: Consistent error handling for authentication failures

## Performance Considerations

-   **Edge Caching**: Static assets are cached at the edge
-   **API Response Caching**: Common API responses are cached with React Query
-   **Optimistic UI Updates**: UI updates optimistically before API responses
-   **Lazy Loading**: Components and data are loaded only when needed
-   **Code Splitting**: Only necessary code is loaded for each page

## Scalability

The architecture is designed to scale horizontally:

-   **Stateless API Routes**: Can be scaled independently
-   **Database Connection Pooling**: Efficient database connections
-   **External Service Redundancy**: Multiple fallback options for critical services
-   **Efficient Data Fetching**: React Query minimizes unnecessary API calls

## Future Architecture Considerations

-   **Microservices**: Split API routes into dedicated microservices
-   **Message Queue**: Add queue for processing long-running tasks
-   **Caching Layer**: Implement Redis for caching
-   **Search Index**: Add Elasticsearch for resume and job searching
