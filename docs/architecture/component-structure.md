# Component Structure

This document outlines the component structure of the HireRizz application, including the data fetching strategy and UI component organization.

## Data Fetching with React Query

HireRizz uses React Query for efficient data fetching, caching, and state management. This provides several benefits:

-   **Automatic caching**: Data is cached and reused when possible
-   **Background refetching**: Data is refreshed in the background
-   **Loading and error states**: Built-in states for UI feedback
-   **Optimistic updates**: UI updates before server confirmation
-   **Prefetching**: Data can be prefetched for faster navigation

### React Query Setup

The React Query client is set up in a client component to avoid server/client boundary issues:

```tsx
// src/components/providers.tsx
"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "sonner";

// Create a client
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            staleTime: 60 * 1000, // 1 minute
            retry: 1,
        },
    },
});

export function Providers({ children }: { children: React.ReactNode }) {
    return (
        <QueryClientProvider client={queryClient}>
            <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem
                disableTransitionOnChange
            >
                <Toaster position="top-right" />
                {children}
            </ThemeProvider>
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}
```

### API Hooks

Custom hooks are created to encapsulate API calls and React Query logic:

```tsx
// src/lib/hooks/api-hooks.ts
import { useQuery, useMutation } from "@tanstack/react-query";

// Resume hooks
export function useResumes() {
    return useQuery({
        queryKey: ["resumes"],
        queryFn: async () => {
            const response = await fetch("/api/resumes");
            if (!response.ok) {
                throw new Error("Failed to fetch resumes");
            }
            return response.json();
        },
    });
}

export function useResume(id: string) {
    return useQuery({
        queryKey: ["resumes", id],
        queryFn: async () => {
            const response = await fetch(`/api/resumes/${id}`);
            if (!response.ok) {
                throw new Error("Failed to fetch resume");
            }
            return response.json();
        },
        enabled: !!id,
    });
}

// Application hooks
export function useApplications() {
    return useQuery({
        queryKey: ["applications"],
        queryFn: async () => {
            const response = await fetch("/api/applications");
            if (!response.ok) {
                throw new Error("Failed to fetch applications");
            }
            return response.json();
        },
    });
}

// Content generation hooks
export function useGeneratedContent(id: string) {
    return useQuery({
        queryKey: ["content", id],
        queryFn: async () => {
            const response = await fetch(`/api/content/${id}`);
            if (!response.ok) {
                throw new Error("Failed to fetch content");
            }
            return response.json();
        },
        enabled: !!id,
    });
}
```

## UI Component Structure

HireRizz uses a modular component structure with several layers:

### Core UI Components

Built on shadcn/ui and extended with custom components:

-   **Button, Input, Card, etc.**: Base UI components from shadcn/ui
-   **Layout components**: Dashboard layout, page containers
-   **Navigation**: Main navigation, mobile navigation
-   **Forms**: Form components with validation

### Feature Components

Components specific to application features:

-   **Resume components**: Resume cards, resume detail view
-   **Application components**: Application list, application detail
-   **Content generation**: Content editor, generation forms

### Enhanced Mobile UI

Mobile-optimized components with Aceternity UI inspiration:

#### Animated Card Components

```tsx
// Card hover effect component
const CardHoverEffect = ({ children, className }: CardHoverEffectProps) => {
    return (
        <div
            className={cn(
                "group relative h-full rounded-xl border bg-card p-6 shadow-sm transition-all hover:shadow-md",
                className
            )}
        >
            <div className="relative z-10 h-full">
                <div className="h-full">{children}</div>
            </div>
            <motion.div
                className="absolute inset-0 z-0 rounded-xl bg-primary/5 opacity-0 transition-opacity group-hover:opacity-100"
                initial={{ opacity: 0 }}
                whileHover={{ opacity: 1 }}
            />
        </div>
    );
};
```

#### Animated Section Titles

```tsx
// Animated section title component
const SectionTitle = ({ children }: { children: React.ReactNode }) => (
    <motion.h3
        className="text-lg font-semibold mb-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
    >
        {children}
    </motion.h3>
);
```

### Responsive Design Approach

HireRizz uses a mobile-first approach with responsive breakpoints:

-   **Base styles**: Optimized for mobile
-   **md: breakpoint**: Tablet and small desktop
-   **lg: breakpoint**: Large desktop

Example of responsive design pattern:

```tsx
<div className="container py-4 md:py-8">
    <div className="mb-6 md:mb-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
            Resumes
        </h1>
        <Button size="lg" className="w-full md:w-auto h-11">
            <PlusCircle className="mr-2 h-5 w-5" />
            Upload Resume
        </Button>
    </div>
</div>
```

## Server/Client Component Strategy

HireRizz carefully separates server and client components:

### Server Components

-   **Page components**: Main page components
-   **Data fetching wrappers**: Components that fetch data
-   **Static UI elements**: UI that doesn't need interactivity

### Client Components

-   **Interactive UI**: Components that need user interaction
-   **State management**: Components that manage local state
-   **Animation components**: Components with animations

### Provider Structure

To avoid server/client boundary issues, providers are separated:

```tsx
// Root layout
export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <ClerkProvider>
            <html lang="en" suppressHydrationWarning>
                <body>
                    <UploadThingProvider>
                        <Providers>{children}</Providers>
                    </UploadThingProvider>
                </body>
            </html>
        </ClerkProvider>
    );
}
```

## Performance Optimizations

Several performance optimizations are implemented:

-   **Code splitting**: Components are loaded only when needed
-   **Image optimization**: Next.js Image component for optimized images
-   **Lazy loading**: Components are lazy-loaded when appropriate
-   **Memoization**: React.memo and useMemo for expensive computations
-   **Virtualization**: For long lists of items
