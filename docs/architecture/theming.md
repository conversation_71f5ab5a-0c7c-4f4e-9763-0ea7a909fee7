# Theming System

This document outlines HireRizz's theming system, including the color palette, dark/light mode, CSS variables, and implementation details.

## Overview

HireRizz features a fully responsive theming system that supports both light and dark modes. The theme is built on CSS variables and Tailwind CSS, providing consistent styling across all components and pages.

## Light and Dark Mode

The application supports dynamic switching between light and dark modes based on:

-   User preference (via the theme toggle button)
-   System preference
-   Default theme (currently set to "system")

### Implementation Details

The theming system is implemented using:

-   `next-themes` for theme management
-   CSS variables for color definitions
-   Tailwind CSS for applying theme colors

```tsx
// src/components/providers.tsx
<ThemeProvider
    attribute="class"
    defaultTheme="system"
    enableSystem
    disableTransitionOnChange
>
    {children}
</ThemeProvider>
```

### Theme Toggle Component

Users can manually switch between themes using the `ThemeToggle` component, which is included in both the main navigation and dashboard header:

```tsx
// src/components/theme-toggle.tsx
export function ThemeToggle() {
    const { setTheme } = useTheme();

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                    <Sun className="... dark:scale-0" />
                    <Moon className="... dark:scale-100" />
                    <span className="sr-only">Toggle theme</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setTheme("light")}>
                    Light
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("dark")}>
                    Dark
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("system")}>
                    System
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
```

## Color Palette

HireRizz uses a minimalist black and white color palette that maintains consistent visual identity across both light and dark modes.

### Primary Colors

| Color Name | Hex Code  | HSL           | Purpose                                    |
| ---------- | --------- | ------------- | ------------------------------------------ |
| Black      | `#000000` | `0, 0%, 0%`   | Primary color in light mode, text, accents |
| White      | `#ffffff` | `0, 0%, 100%` | Background in light mode, text in dark     |
| Gray       | `#737373` | `0, 0%, 45%`  | Secondary color, borders, less emphasis    |
| Light Gray | `#bfbfbf` | `0, 0%, 75%`  | Muted elements in dark mode                |
| Dark Gray  | `#262626` | `0, 0%, 15%`  | Muted backgrounds in dark mode             |

### Light Mode Variables

```css
/* Light mode */
--background: 0 0% 100%; /* White: #ffffff */
--foreground: 0 0% 0%; /* Black: #000000 */
--card: 0 0% 100%; /* White: #ffffff */
--muted: 0 0% 96%; /* Light Gray: #f5f5f5 */
--border: 0 0% 89.8%; /* Light Gray: #e5e5e5 */
```

### Dark Mode Variables

```css
/* Dark mode */
--background: 0 0% 0%; /* Black: #000000 */
--foreground: 0 0% 100%; /* White: #ffffff */
--card: 0 0% 5%; /* Very Dark Gray: #0d0d0d */
--muted: 0 0% 15%; /* Dark Gray: #262626 */
--border: 0 0% 20%; /* Dark Gray: #333333 */
```

## CSS Variable Structure

The theming system uses HSL color values to define colors, making it easy to adjust lightness and saturation:

```css
:root {
    /* Base colors */
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 0%; /* Black */

    /* Component colors */
    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 0%; /* Black */

    /* Semantic colors */
    --primary: 0 0% 0%; /* Black */
    --secondary: 0 0% 45%; /* Gray */
    --accent: 0 0% 0%; /* Black */
    --destructive: 0 0% 0%; /* Black */

    /* UI colors */
    --muted: 0 0% 96%; /* Light Gray */
    --border: 0 0% 89.8%; /* Light Gray */
    --input: 0 0% 89.8%; /* Light Gray */
    --ring: 0 0% 0%; /* Black */
}

.dark {
    /* Dark mode overrides */
    --background: 0 0% 0%; /* Black */
    --foreground: 0 0% 100%; /* White */
    --primary: 0 0% 100%; /* White */
    --secondary: 0 0% 75%; /* Light Gray */
    /* ...other dark mode variables... */
}
```

## Usage Guidelines

### Using Theme Variables

**✅ DO:**

-   Use Tailwind CSS classes with theme variables:
    ```tsx
    <div className="bg-background text-foreground border-border" />
    ```
-   Use semantic color variables for appropriate elements:
    ```tsx
    <Button className="bg-primary hover:bg-primary/90 text-primary-foreground" />
    ```

**❌ DON'T:**

-   Hardcode colors:
    ```tsx
    <div className="bg-[#000000] text-white" /> <!-- Avoid this -->
    ```
-   Mix semantic color usage:
    ```tsx
    <Button className="bg-destructive hover:bg-primary/90" /> <!-- Inconsistent -->
    ```

### Gradient Usage

The application includes predefined gradients for consistent branding:

```tsx
<span className="text-gradient-pink-cyan">Gradient Text</span> <!-- Black to Gray -->
<span className="text-gradient-cyan-purple">Another Gradient</span> <!-- Gray to Black -->
<span className="text-gradient-pink-purple">Third Option</span> <!-- Black to Dark Gray -->
<span className="text-gradient-coral-amber">Fourth Option</span> <!-- Black to White -->
```

### Component-Specific Theming

Some components have specialized theme integration:

**Clerk Authentication:**

```tsx
<ClerkProvider
    appearance={{
        elements: {
            card: "bg-card border border-border rounded-xl shadow-xl",
            formButtonPrimary: "bg-primary hover:bg-primary/90 text-sm normal-case",
            footerActionLink: "text-secondary hover:text-secondary/90",
        },
    }}
>
```

**CyberpunkBackground Component:**
The background adjusts its styling based on the current theme, controlling glow opacity and color.

## Testing Theme Changes

When making theme changes:

1. Test in both light and dark modes
2. Verify contrast meets accessibility standards
3. Check transition animations between themes
4. Ensure all UI components correctly handle theme switching

## Extending the Theme

To extend the theme with new colors:

1. Add CSS variables to both `:root` and `.dark` in `globals.css`
2. Create helper classes in `globals.css` if needed
3. Update the design documentation

## Theme Best Practices

1. **Consistency**: Use the same color variables for similar UI elements
2. **Contrast**: Ensure text has sufficient contrast against backgrounds
3. **Semantics**: Use semantic color variables (primary, secondary, etc.) over direct color references
4. **Gradients**: Use predefined gradients for branded elements
5. **Transitions**: Apply smooth transitions for theme changes with `transition-colors`
