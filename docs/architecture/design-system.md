# Design System

This document outlines the design system of the HireRizz application, including the color palette, typography, and UI components.

## Color Palette

HireRizz uses a modern, cyberpunk-inspired color palette that creates a cohesive visual identity across the application. The primary colors are:

| Color Name   | Hex Code  | Description                                          |
| ------------ | --------- | ---------------------------------------------------- |
| Royal Purple | `#5e4b8b` | Primary brand color, used for logo, buttons, accents |
| Coral Red    | `#ff6e61` | Secondary color, used for highlighting and accents   |
| Light Blue   | `#6d9dc5` | Used for links, secondary elements, and accents      |
| Vivid Pink   | `#da1b61` | Used for attention-grabbing elements and gradients   |
| Amber        | `#ffb84d` | Used for warnings, notifications, and some gradients |
| Dark Slate   | `#1c1b29` | Main background color for dark mode                  |

### Usage Guidelines

-   **Primary Actions**: Use Royal Purple (`#5e4b8b`) for primary buttons, focus states, and key interactive elements.
-   **Secondary Actions**: Use Light Blue (`#6d9dc5`) for secondary buttons, links, and less important interactive elements.
-   **Destructive Actions**: Use Vivid Pink (`#da1b61`) for destructive actions like delete buttons.
-   **Branding**: The HireRizz logo uses Royal Purple (`#5e4b8b`) for "Hire" and Coral Red (`#ff6e61`) for "Rizz".
-   **Backgrounds**: Dark Slate (`#1c1b29`) is used for the main background in dark mode.
-   **Gradients**: Various combinations of the palette colors are used for gradients throughout the application.

## Gradient Text

The application uses several gradient text classes that combine colors from the palette:

| Gradient Name | Colors                                 | CSS Class                   |
| ------------- | -------------------------------------- | --------------------------- |
| Accent        | `#da1b61` (Pink) to `#6d9dc5` (Blue)   | `text-gradient-pink-cyan`   |
| Primary       | `#da1b61` (Pink) to `#5e4b8b` (Purple) | `text-gradient-pink-purple` |
| Secondary     | `#6d9dc5` (Blue) to `#5e4b8b` (Purple) | `text-gradient-cyan-purple` |
| Coral-Amber   | `#ff6e61` (Coral) to `#ffb84d` (Amber) | `text-gradient-coral-amber` |

## UI Components

### CyberpunkText

The `CyberpunkText` component is used for styled text with gradient effects. It accepts the following props:

```tsx
interface CyberpunkTextProps {
    children: React.ReactNode;
    className?: string;
    variant?: "default" | "gradient" | "glitch" | "animated";
    gradient?:
        | "primary"
        | "secondary"
        | "accent"
        | "coral-amber"
        | "pink-purple";
    as?: React.ElementType;
}
```

Example usage:

```tsx
<CyberpunkText gradient="accent" className="text-3xl">
    <span className="text-[#5e4b8b]">Hire</span>
    <span className="text-[#ff6e61]">Rizz</span>
</CyberpunkText>
```

### CyberpunkBackground

The `CyberpunkBackground` component creates a cyberpunk-themed background with a grid and glowing accents. It accepts the following props:

```tsx
interface CyberpunkBackgroundProps {
    className?: string;
    showGrid?: boolean;
    showGlow?: boolean;
    variant?: "default" | "subtle";
}
```

Example usage:

```tsx
<div className="relative">
    <CyberpunkBackground />
    <div className="relative z-10">Content goes here</div>
</div>
```

### CyberpunkButton

The `CyberpunkButton` component provides styled buttons with various variants:

```tsx
// Usage
<CyberpunkButton variant="default">Primary</CyberpunkButton>
<CyberpunkButton variant="secondary">Secondary</CyberpunkButton>
<CyberpunkButton variant="accent">Accent</CyberpunkButton>
<CyberpunkButton variant="outline">Outline</CyberpunkButton>
<CyberpunkButton variant="ghost">Ghost</CyberpunkButton>
<CyberpunkButton variant="destructive">Destructive</CyberpunkButton>
```

## Page Transitions

The application uses a custom `PageTransition` component that provides animated transitions between pages:

```tsx
// Layout usage
export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en" suppressHydrationWarning>
            <body className={inter.className}>
                <Providers>
                    <PageTransition>{children}</PageTransition>
                </Providers>
            </body>
        </html>
    );
}
```

The transition includes a loading screen with animated elements using the application's color palette.

## Responsive Design

The application follows a mobile-first approach with responsive breakpoints:

-   **Base styles**: Optimized for mobile
-   **md: breakpoint**: Tablet and small desktop (768px)
-   **lg: breakpoint**: Large desktop (1024px)
-   **xl: breakpoint**: Extra large desktop (1280px)

All components are designed to be fully responsive across these breakpoints.
