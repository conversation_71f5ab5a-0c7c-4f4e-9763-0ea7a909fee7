# Database Schema

This document provides a detailed overview of the database schema used in the HireRizz application.

## Overview

HireRizz uses PostgreSQL as its primary database, with the schema defined using Drizzle ORM. The database consists of several interconnected tables that store user data, resumes, job applications, and AI-generated content.

## Tables

### Users

The `users` table stores basic user information and is linked to <PERSON>'s authentication system.

```typescript
export const users = pgTable("users", {
    id: text("id").primaryKey(), // Clerk user ID
    email: text("email").notNull().unique(),
    firstName: text("first_name"),
    lastName: text("last_name"),
    profileImage: text("profile_image"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

### Resumes

The `resumes` table stores information about user-uploaded resumes, including file metadata and parsed content.

```typescript
export const resumes = pgTable("resumes", {
    id: serial("id").primaryKey(),
    userId: text("user_id")
        .notNull()
        .references(() => users.id, { onDelete: "cascade" }),
    name: varchar("name", { length: 255 }).notNull(),
    fileUrl: text("file_url").notNull(),
    filePath: text("file_path"), // Store the file path/key for deletion
    fileType: varchar("file_type", { length: 10 }).notNull(), // PDF, DOCX
    isDefault: boolean("is_default").default(false),
    parsedContent: json("parsed_content"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

### Job Applications

The `jobApplications` table tracks job applications created by users.

```typescript
export const applicationStatusEnum = pgEnum("application_status", [
    "not_applied",
    "applied",
    "interviewing",
    "offer",
    "rejected",
    "accepted",
]);

export const jobApplications = pgTable("job_applications", {
    id: serial("id").primaryKey(),
    userId: text("user_id")
        .notNull()
        .references(() => users.id, { onDelete: "cascade" }),
    resumeId: integer("resume_id").references(() => resumes.id),
    company: varchar("company", { length: 255 }).notNull(),
    position: varchar("position", { length: 255 }).notNull(),
    jobDescription: text("job_description"),
    parsedJobDescription: json("parsed_job_description"), // Store the parsed JD analysis results
    location: varchar("location", { length: 255 }),
    salary: varchar("salary", { length: 100 }),
    applicationUrl: text("application_url"),
    contactName: varchar("contact_name", { length: 255 }),
    contactEmail: varchar("contact_email", { length: 255 }),
    contactPhone: varchar("contact_phone", { length: 50 }),
    status: applicationStatusEnum("status").default("not_applied"),
    notes: text("notes"),
    appliedDate: timestamp("applied_date"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
    jobLink: text("job_link"),
});
```

### Generated Documents

The `generatedDocuments` table stores AI-generated content for job applications, including cover letters, LinkedIn messages, cold emails, and chat history for application questions.

```typescript
export const ChatMessageSchema = z.object({
    speaker: z.enum(["user", "bot"]),
    time: z.coerce.date(),
    message: z.string(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type ChatHistory = ChatMessage[];

export const generatedDocuments = pgTable("generated_documents", {
    id: serial("id").primaryKey(),
    userId: text("user_id")
        .notNull()
        .references(() => users.id, { onDelete: "cascade" }),
    applicationId: integer("application_id").references(
        () => jobApplications.id,
        { onDelete: "cascade" }
    ),
    coverLetter: text("cover_letter"),
    linkedinMessage: text("linkedin_message"),
    coldEmail: text("cold_email"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
    chat: jsonb("chat").$type<ChatHistory>().default([]),
});
```

### Shortened URLs

The `shortenedUrls` table stores shortened URLs for tracking job applications.

```typescript
export const shortenedUrls = pgTable("shortened_urls", {
    id: serial("id").primaryKey(),
    shortCode: varchar("short_code", { length: 10 }).notNull().unique(),
    originalUrl: text("original_url").notNull(),
    userId: text("user_id").references(() => users.id),
    applicationId: integer("application_id").references(
        () => jobApplications.id
    ),
    clickCount: integer("click_count").default(0),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    expiresAt: timestamp("expires_at"),
});
```

## Relationships

-   **Users to Resumes**: One-to-many relationship. A user can have multiple resumes.
-   **Users to Job Applications**: One-to-many relationship. A user can have multiple job applications.
-   **Resumes to Job Applications**: One-to-many relationship. A resume can be used for multiple job applications.
-   **Job Applications to Generated Documents**: One-to-one relationship. Each job application can have one set of generated documents.
-   **Users to Generated Documents**: One-to-many relationship. A user can have multiple generated documents.
-   **Job Applications to Shortened URLs**: One-to-many relationship. A job application can have multiple shortened URLs.

## Chat Message Format

The chat history for application questions is stored in the `chat` field of the `generatedDocuments` table as a JSONB array. Each message in the array has the following structure:

```typescript
{
    speaker: "user" | "bot",
    time: Date,
    message: string
}
```

This format allows for efficient storage and retrieval of conversation history between the user and the AI assistant.

## Data Migration and Schema Updates

When making changes to the database schema:

1. Update the schema definitions in `src/lib/db/schema.ts`
2. Generate the migration using Drizzle Kit: `npm run db:generate`
3. Apply the migration: `npm run db:push`

## Indexes and Performance

The schema includes primary keys and foreign key relationships to ensure data integrity and optimize query performance. Additional indexes may be added based on query patterns and performance analysis.
