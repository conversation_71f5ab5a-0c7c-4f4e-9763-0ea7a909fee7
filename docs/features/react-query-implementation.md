# React Query Implementation

This document outlines the implementation of React Query in HireRizz for efficient data fetching and state management.

## Overview

HireRizz uses [React Query](https://tanstack.com/query/latest) (TanStack Query) to manage server state, handle data fetching, and provide a better user experience through automatic caching, background refetching, and optimistic updates.

## Benefits

The implementation of React Query provides several key benefits:

-   **Automatic caching**: Data is cached and reused when possible, reducing unnecessary network requests
-   **Background refetching**: Data is refreshed in the background without disrupting the user experience
-   **Loading and error states**: Built-in states for providing feedback to users
-   **Optimistic updates**: UI can be updated before server confirmation for a snappier feel
-   **Prefetching**: Data can be prefetched for faster navigation
-   **Devtools**: Integrated developer tools for debugging and monitoring

## Setup

### Query Client Singleton

We use a singleton pattern for the React Query client to ensure consistent caching and state management throughout the application:

```tsx
// src/lib/query-client.ts
import { QueryClient } from "@tanstack/react-query";

const queryClientConfig = {
    defaultOptions: {
        queries: {
            staleTime: 60 * 1000, // 1 minute
            retry: 1,
            refetchOnWindowFocus: false,
        },
        mutations: {
            retry: 1,
        },
    },
};

export const queryClient = new QueryClient(queryClientConfig);
```

### Provider Configuration

React Query is set up using a dedicated provider component:

```tsx
// src/providers/query-provider.tsx
"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { queryClient } from "@/lib/query-client";

export function QueryProvider({ children }: { children: React.ReactNode }) {
    return (
        <QueryClientProvider client={queryClient}>
            {children}
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}
```

### Provider Integration

The provider is integrated in the application layout to ensure all components have access to React Query.

## Custom Hooks

### API Hooks

We've developed a comprehensive set of custom hooks to encapsulate API calls and React Query logic:

```tsx
// src/lib/hooks/api-hooks.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { queryClient } from "@/lib/query-client";

// Application Queries
export type ApplicationStatus =
    | "not_applied"
    | "applied"
    | "interviewing"
    | "offer"
    | "rejected"
    | "accepted";

// Valid application statuses
export const VALID_STATUSES: ApplicationStatus[] = [
    "not_applied",
    "applied",
    "interviewing",
    "offer",
    "rejected",
    "accepted",
];

// Get single application
export function useApplication(id: string) {
    return useQuery({
        queryKey: ["application", id],
        queryFn: async () => {
            const response = await fetch(`/api/applications/${id}`);
            if (!response.ok) throw new Error("Failed to fetch application");
            return response.json();
        },
        retry: 1,
    });
}

// Get filtered applications
export function useFilteredApplications(filters?: {
    status?: ApplicationStatus;
}) {
    return useQuery({
        queryKey: ["applications", filters?.status],
        queryFn: async () => {
            // Build URL with query parameters
            let url = "/api/applications";

            if (filters?.status) {
                url += `?status=${filters.status}`;
            }

            const response = await fetch(url);
            if (!response.ok) throw new Error("Failed to fetch applications");

            return response.json();
        },
        staleTime: 30 * 1000, // 30 seconds
    });
}

// Update application status
export function useUpdateApplicationStatus() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ id, status, application }) => {
            const response = await fetch(`/api/applications/${id}`, {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ ...application, status }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || "Failed to update status");
            }

            return response.json();
        },
        onSuccess: (data, { id }) => {
            queryClient.setQueryData(["application", id], data);
            queryClient.invalidateQueries({ queryKey: ["applications"] });
            toast.success(`Application status updated successfully`);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
}

// Resume Queries
export function useResume(resumeId: string) {
    return useQuery({
        queryKey: ["resume", resumeId],
        queryFn: async () => {
            const response = await fetch(`/api/resumes/${resumeId}`);
            if (!response.ok) throw new Error("Failed to fetch resume");
            return response.json();
        },
        enabled: !!resumeId,
    });
}

export function useResumes() {
    return useQuery({
        queryKey: ["resumes"],
        queryFn: async () => {
            const response = await fetch("/api/resumes");
            if (!response.ok) throw new Error("Failed to fetch resumes");
            return response.json();
        },
    });
}
```

## Error Handling

We've implemented robust error handling throughout our React Query implementation to ensure a good user experience:

```tsx
// Example of error handling in a component
try {
    // API call or state update
} catch (error) {
    console.error("Error:", error);
    toast.error(error.message || "An unexpected error occurred");
}
```

In our hooks, we handle errors gracefully:

```tsx
// Error handling in hooks
return useMutation({
    // ...mutation logic
    onError: (error) => {
        toast.error(error.message || "Failed to perform operation");
    },
});
```

## Status Filtering Implementation

We've implemented a filtering mechanism for applications based on their status:

```tsx
// src/app/dashboard/applications/applications-client.tsx
export function ApplicationsClient() {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Safely get status from URL
    const getStatusFromUrl = (): ApplicationStatus | "all" => {
        try {
            const statusParam = searchParams?.get("status");
            if (!statusParam) return "all";

            // Validate that it's a valid status
            if (
                statusParam === "all" ||
                VALID_STATUSES.includes(statusParam as ApplicationStatus)
            ) {
                return statusParam as ApplicationStatus | "all";
            }
            return "all";
        } catch (e) {
            console.error("Error parsing status from URL", e);
            return "all";
        }
    };

    // Get status filter from URL with safe fallback
    const [status, setStatus] = useState<ApplicationStatus | "all">(
        getStatusFromUrl()
    );

    // Fetch filtered applications using React Query
    const {
        data: applications,
        isLoading,
        error,
    } = useFilteredApplications({
        status: status !== "all" ? status : undefined,
    });

    // Rest of the component...
}
```

The filtering UI utilizes badge components:

```tsx
// src/app/dashboard/applications/search-filters.tsx
export function SearchFilters() {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Get current status filter from URL
    const currentStatus = searchParams ? searchParams.get("status") : null;

    // Handle status filter click
    const handleStatusClick = (status: string) => {
        try {
            router.push(
                `${pathname}?${createQueryString("status", status || null)}`
            );
        } catch (e) {
            console.error("Error updating status", e);
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div className="flex flex-wrap gap-2">
                    <Badge
                        variant={!currentStatus ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleStatusClick("")}
                    >
                        All
                    </Badge>

                    {VALID_STATUSES.map((status) => (
                        <Badge
                            key={status}
                            variant={
                                currentStatus === status ? "default" : "outline"
                            }
                            className="cursor-pointer"
                            onClick={() => handleStatusClick(status)}
                        >
                            {statusLabels[status] || status}
                        </Badge>
                    ))}
                </div>
            </div>
        </div>
    );
}
```

## Defensive Programming

Our implementation includes defensive programming practices to ensure robustness:

1. **Safe Parameter Handling**: All URL parameter access is wrapped in try/catch blocks
2. **Default Values**: Every function that might return null/undefined has appropriate fallbacks
3. **Error Boundaries**: Error handling at multiple levels ensures graceful degradation
4. **Validation**: Input validation prevents invalid data from being processed

## Conclusion

React Query provides HireRizz with a powerful, consistent approach to data fetching and state management. The implementation allows for a better user experience with optimized network requests, proper loading states, and a responsive UI.

For more information on React Query, visit the [official documentation](https://tanstack.com/query/latest/docs/react/overview).
