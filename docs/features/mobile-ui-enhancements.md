# Mobile UI Enhancements

This document outlines the mobile UI enhancements implemented in HireRizz, inspired by Aceternity UI components and design patterns.

## Overview

HireRizz has been enhanced with a mobile-first approach to ensure a seamless experience across all devices. The UI improvements focus on:

-   **Responsive design**: Adapting layouts for different screen sizes
-   **Touch-friendly interfaces**: Optimized for mobile interaction
-   **Smooth animations**: Subtle animations for better user experience
-   **Consistent styling**: Unified design language across the application

## Aceternity UI Inspiration

The UI enhancements draw inspiration from [Aceternity UI](https://ui.aceternity.com/), a modern component library known for its sleek animations and contemporary design. Key elements adopted include:

-   **Card hover effects**: Subtle animations on hover/touch
-   **Animated transitions**: Smooth transitions between states
-   **Modern aesthetics**: Clean, minimal design with appropriate spacing
-   **Motion-based feedback**: Visual feedback for user actions

## Key Components

### CardHoverEffect

A reusable component that adds subtle hover animations to card elements:

```tsx
interface CardHoverEffectProps {
    children: React.ReactNode;
    className?: string;
}

const CardHoverEffect = ({ children, className }: CardHoverEffectProps) => {
    return (
        <div
            className={cn(
                "group relative h-full rounded-xl border bg-card p-6 shadow-sm transition-all hover:shadow-md",
                className
            )}
        >
            <div className="relative z-10 h-full">
                <div className="h-full">{children}</div>
            </div>
            <motion.div
                className="absolute inset-0 z-0 rounded-xl bg-primary/5 opacity-0 transition-opacity group-hover:opacity-100"
                initial={{ opacity: 0 }}
                whileHover={{ opacity: 1 }}
            />
        </div>
    );
};
```

### SectionTitle

An animated section title component for visual hierarchy:

```tsx
const SectionTitle = ({ children }: { children: React.ReactNode }) => (
    <motion.h3
        className="text-lg font-semibold mb-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
    >
        {children}
    </motion.h3>
);
```

### CardItem

A motion-enhanced card item for lists and grids:

```tsx
const CardItem = ({
    children,
    className,
}: {
    children: React.ReactNode;
    className?: string;
}) => (
    <motion.div
        className={cn(
            "rounded-lg border bg-card shadow-sm overflow-hidden",
            className
        )}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -2, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
    >
        <div className="p-4">{children}</div>
    </motion.div>
);
```

## Responsive Design Patterns

### Mobile-First Approach

All components are designed with a mobile-first approach, using Tailwind's responsive modifiers:

```tsx
<div className="container py-4 md:py-8">
    <div className="mb-6 md:mb-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
            Resumes
        </h1>
        <Button size="lg" className="w-full md:w-auto h-11">
            <PlusCircle className="mr-2 h-5 w-5" />
            Upload Resume
        </Button>
    </div>
</div>
```

### Responsive Typography

Text sizes adjust based on screen size:

```tsx
<h3 className="font-medium text-base md:text-lg line-clamp-1">
    {resume.name}
</h3>
<p className="text-xs md:text-sm text-muted-foreground">
    {resume.fileType.toUpperCase()} • {formatDate(new Date(resume.createdAt))}
</p>
```

### Adaptive Layouts

Layouts change from stacked on mobile to side-by-side on larger screens:

```tsx
<div className="flex flex-col md:flex-row md:items-center md:justify-between mt-3 gap-4">
    <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
        {resume.name}
    </h1>
    <div className="flex items-center gap-2">
        <Button
            variant="outline"
            size="sm"
            asChild
            className="text-xs md:text-sm"
        >
            <a href={resume.fileUrl} target="_blank" rel="noopener noreferrer">
                <Download className="mr-1 md:mr-2 h-3 w-3 md:h-4 md:w-4" />
                Download
            </a>
        </Button>
    </div>
</div>
```

### Grid Adaptations

Grid layouts adjust columns based on screen size:

```tsx
<div className="grid gap-4 md:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
    {resumes.map((resume) => (
        <CardHoverEffect key={resume.id}>{/* Card content */}</CardHoverEffect>
    ))}
</div>
```

## Enhanced Pages

### Resume List Page

The resume list page has been enhanced with:

-   Responsive grid layout (1 column on mobile, 2-3 columns on larger screens)
-   Animated card hover effects
-   Optimized button and text sizes for mobile
-   Full-width buttons on mobile for better touch targets
-   Proper spacing and hierarchy

### Resume Detail Page

The resume detail page features:

-   Stacked header on mobile, side-by-side on desktop
-   Animated section titles
-   Responsive tabs (full-width on mobile)
-   Optimized preview height for mobile screens
-   Touch-friendly buttons and controls

### Resume Card Component

The resume card component has been enhanced with improved responsive design:

```tsx
<CardHoverEffect className="group relative h-full rounded-xl border bg-card p-4 sm:p-6">
    {/* Card Header */}
    <div className="mb-3 sm:mb-4 flex items-start justify-between">
        <div className="flex items-start gap-2 sm:gap-3">
            <div className="rounded-lg bg-primary/10 p-2">
                {getFileIcon(resume.fileType)}
            </div>
            <div>
                <h3 className="font-medium text-sm sm:text-base line-clamp-1">
                    {resume.name}
                </h3>
                <p className="text-xs text-muted-foreground mt-0.5">
                    {resume.fileType.toUpperCase()} •{" "}
                    {formatDate(new Date(resume.createdAt))}
                </p>
            </div>
        </div>
        {resume.isDefault && (
            <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                Default
            </span>
        )}
    </div>

    {/* Card Actions */}
    <div className="flex gap-2 mt-3 sm:mt-4 relative z-20">
        <Button variant="outline" className="w-full h-8 sm:h-9 text-xs">
            <Download className="mr-1.5 h-3 w-3 sm:h-4 sm:w-4" />
            Download
        </Button>
        <Button variant="outline" className="w-full h-8 sm:h-9 text-xs">
            <Trash2 className="mr-1.5 h-3 w-3 sm:h-4 sm:w-4" />
            Delete
        </Button>
    </div>
</CardHoverEffect>
```

Key improvements in the resume card design:

-   **Consistent Spacing**: Using smaller padding on mobile (`p-4`) and larger on desktop (`sm:p-6`)
-   **Optimized Typography**: Text sizes scale appropriately (`text-sm sm:text-base` for titles)
-   **Button Sizing**: Consistent button heights across devices (`h-8 sm:h-9`)
-   **Icon Scaling**: Icons scale with screen size (`h-3 w-3 sm:h-4 sm:w-4`)
-   **Touch Targets**: Appropriately sized for mobile interaction
-   **Visual Hierarchy**: Clear distinction between primary and secondary information
-   **Responsive Grid**: Updated grid system using `sm` breakpoint for earlier adaptation

## Implementation Details

### Required Dependencies

-   **Framer Motion**: For animations and transitions
-   **TailwindCSS**: For responsive styling
-   **clsx/tailwind-merge**: For conditional class composition

### Browser Compatibility

The enhanced UI is compatible with:

-   Chrome/Edge (latest 2 versions)
-   Firefox (latest 2 versions)
-   Safari (latest 2 versions)
-   Mobile Safari and Chrome for iOS/Android

### Accessibility Considerations

The UI enhancements maintain accessibility through:

-   Proper contrast ratios
-   Keyboard navigation support
-   Screen reader compatibility
-   Appropriate ARIA attributes
-   Reduced motion preferences support

## Future Enhancements

Planned future enhancements include:

-   **Gesture-based interactions**: Swipe and pinch gestures for mobile
-   **Scroll-based animations**: Animations triggered by scroll position
-   **Skeleton loading states**: Enhanced loading states with animations
-   **Micro-interactions**: Subtle animations for user feedback
-   **Dark mode optimizations**: Enhanced dark mode experience
