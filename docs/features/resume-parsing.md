# Resume Parsing

The Resume Parsing feature in HireRizz allows users to upload resumes in various formats (PDF, DOCX, DOC, TXT) and automatically extract structured information from them. This feature uses a combination of PDF.co API for text extraction and Google's Gemini AI for intelligent parsing.

## Overview

Resume parsing is a critical feature that enables users to:

-   Upload resumes in multiple formats
-   Extract key information automatically
-   Store structured data for later use
-   Analyze resume content for job matching

## How It Works

The resume parsing process follows these steps:

1. **File Upload**: User uploads a resume file via the custom file upload interface
2. **Text Extraction**: The system extracts raw text from the document
    - For PDF files: Uses PDF.co API
    - For DOCX/DOC files: Uses Mammoth.js
    - For TXT files: Reads directly
3. **AI Parsing**: The extracted text is sent to Google's Gemini AI with a specialized prompt
4. **Data Normalization**: The AI response is normalized to ensure consistent data structure
5. **Storage**: The parsed data is stored in the database for future use

## File Upload Process

The file upload process has been enhanced to provide a better user experience:

1. **File Selection**: Users can select files through:

    - Drag and drop interface
    - File browser dialog
    - Click on the upload area

2. **Validation**: Files are validated for:

    - File type (PDF, DOCX, DOC)
    - File size (maximum 10MB)

3. **Upload Flow**:

    - User selects a file
    - File preview is displayed with name and size
    - User clicks "Upload File" button
    - Progress bar shows upload status
    - Success confirmation appears when complete

4. **User Experience Enhancements**:

    - Visual feedback during drag operations
    - File type icons based on extension
    - Real-time progress tracking
    - Clear error messages for invalid files
    - Ability to remove selected files

5. **Integration with UploadThing**:
    - Secure file uploads
    - Server-side validation
    - Automatic file storage

## Parsing Triggers

Resume parsing can be triggered in three ways:

1. **Upload Time**: Automatically when a resume is uploaded
2. **Manual Parsing**: User-initiated from the resume detail page
3. **On-Demand**: When needed for content generation or job analysis

## Implementation Details

### File Processing

The system supports multiple file formats:

-   **PDF**: Processed using PDF.co's text extraction API
-   **DOCX/DOC**: Converted to text using Mammoth.js
-   **TXT**: Read directly as UTF-8 text

### AI Parsing

The system uses Google's Gemini AI to extract structured information:

````typescript
async function parseWithAI(fileContent: string): Promise<ResumeData | null> {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) return null;

    try {
        const genAI = new GoogleGenerativeAI(apiKey);
        const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
        const truncatedContent = fileContent.slice(0, MAX_CONTENT_CHARS);
        const prompt = generatePrompt(truncatedContent);

        const result = await model.generateContent(prompt);
        const responseText = result.response.text();

        // Extract JSON from response
        const jsonMatch =
            responseText.match(/```json\s*([\s\S]*?)\s*```/) || [];
        const jsonText = jsonMatch[1]?.trim() || responseText.trim();

        return JSON.parse(jsonText) as ResumeData;
    } catch (error) {
        logger.error("Failed to parse with AI", { error });
        return null;
    }
}
````

### Data Structure

The parsed resume data follows this structure:

```typescript
interface ResumeData {
    name: string;
    email: string;
    phone: string;
    summary: string;
    skills: string[];
    experience: Array<{
        title: string;
        company: string;
        dates: string;
        description: string;
    }>;
    education: Array<{
        institution: string;
        degree: string;
        field: string;
        dates: string;
    }>;
    projects: Array<{
        name: string;
        description: string;
        dates?: string;
    }>;
}
```

### Fallback Mechanism

If AI parsing fails, the system falls back to a basic parser that extracts:

-   Email addresses using regex
-   Phone numbers using regex
-   Basic structure from section headers

## API Endpoints

### POST /api/resumes/parse

Parses a resume by ID.

**Request Body:**

```json
{
    "resumeId": "123"
}
```

**Response:**

```json
{
    "parsedContent": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "************",
        "summary": "Experienced software developer...",
        "skills": ["JavaScript", "React", "Node.js"],
        "experience": [...],
        "education": [...],
        "projects": [...]
    }
}
```

### GET /api/resumes/parse?resumeId=123

Retrieves previously parsed content for a resume.

**Response:**

```json
{
    "parsedContent": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "************",
        "summary": "Experienced software developer...",
        "skills": ["JavaScript", "React", "Node.js"],
        "experience": [...],
        "education": [...],
        "projects": [...]
    }
}
```

## Configuration

The resume parsing feature requires the following environment variables:

-   `PDF_CO_API_KEY`: API key for PDF.co text extraction
-   `GEMINI_API_KEY`: API key for Google's Gemini AI

## Limitations

-   Maximum file size: 10MB
-   Maximum content length for AI parsing: 60,000 characters
-   Supported file types: PDF, DOCX, DOC

## Future Improvements

-   Add support for more file formats (RTF, HTML)
-   Improve parsing accuracy with custom-trained models
-   Add ability to manually correct parsed information
-   Implement batch processing for multiple resumes
-   Add preview functionality for uploaded documents
