# Application Question Assistant

The Application Question Assistant is an AI-powered feature that helps job applicants craft professional answers to common application and interview questions. This feature provides personalized, context-aware responses based on job details and optionally the applicant's resume.

## Overview

The Application Question Assistant provides:

1. **Interactive Chat Interface**: A conversational UI where users can ask questions about their job application
2. **Personalized Answers**: AI-generated responses tailored to the specific job and company
3. **Suggested Questions**: Common application questions to help users get started
4. **Persistent Chat History**: Conversation history saved for future reference

## Implementation

### Integration Points

The feature consists of several components:

1. **UI Component**: `ApplicationQuestionChat` component in the content page
2. **API Route**: `/api/ai/application-questions` for handling questions and generating answers
3. **Database Storage**: Chat history stored in `generatedDocuments` table
4. **React Query Hooks**: Custom hooks for managing chat state and API requests

### UI Implementation

The Application Question Assistant is integrated into the application content page as a tab:

```tsx
<TabsContent value="appQuestions" className="mt-4">
    <div className="rounded-lg border bg-card overflow-hidden">
        <div className="p-4 bg-muted/20 border-b">
            <div className="flex items-center">
                <MessageCircle className="h-5 w-5 text-primary mr-2" />
                <h3 className="font-medium">Application Question Assistant</h3>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
                Get help answering common application questions for{" "}
                {application.company}.
            </p>
        </div>
        <ApplicationQuestionChat application={application} resume={resume} />
    </div>
</TabsContent>
```

### Chat Component

The `ApplicationQuestionChat` component provides the interactive chat interface:

```tsx
export function ApplicationQuestionChat({
    application,
    resume,
}: ApplicationQuestionChatProps) {
    // Fetch existing chat history
    const { data: chatData, isLoading: isLoadingChat } = useApplicationChat(
        application.id
    );

    const [localMessages, setLocalMessages] = useState<Message[]>([
        {
            role: "assistant",
            content:
                "Hi! I'm your AI assistant for job applications. Ask me any question about this application, and I'll help craft a professional answer.",
            timestamp: new Date(),
        },
    ]);

    const [input, setInput] = useState("");
    const { mutate: answerQuestion, isPending: isGenerating } =
        useAnswerApplicationQuestion();

    // Handle sending messages and other UI interactions
    // ...

    return (
        <div className="flex flex-col h-[600px] max-h-[80vh]">
            {/* Chat messages display */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-secondary/5">
                {messages.map((message, index) => (
                    <div
                        key={index}
                        className={`flex ${
                            message.role === "user"
                                ? "justify-end"
                                : "justify-start"
                        }`}
                    >
                        {/* Message display */}
                    </div>
                ))}
            </div>

            {/* Suggested questions */}
            {messages.length === 1 && (
                <div className="px-4 pb-4 pt-2 bg-muted/20">
                    <p className="text-sm text-muted-foreground mb-2">
                        Suggested questions:
                    </p>
                    <div className="flex flex-wrap gap-2">
                        {SUGGESTED_QUESTIONS.map((question, index) => (
                            <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                    handleSuggestedQuestion(question)
                                }
                            >
                                {question}
                            </Button>
                        ))}
                    </div>
                </div>
            )}

            {/* Input field */}
            <div className="p-4 border-t">
                <div className="flex gap-2">
                    <Textarea
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        placeholder="Type a question..."
                        className="flex-1"
                    />
                    <Button
                        onClick={handleSendMessage}
                        disabled={!input.trim() || isGenerating}
                    >
                        <SendHorizontal className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
}
```

### API Implementation

The `/api/ai/application-questions` route handles fetching chat history and generating answers:

```typescript
// GET - Retrieve chat history
export async function GET(request: Request) {
    try {
        const { userId } = await auth();

        const url = new URL(request.url);
        const applicationId = url.searchParams.get("applicationId");

        // Check if application exists and belongs to user
        // ...

        // Fetch chat history from database
        const document = await db.query.generatedDocuments.findFirst({
            where: eq(
                generatedDocuments.applicationId,
                parseInt(applicationId)
            ),
        });

        return NextResponse.json({ chat: document?.chat || [] });
    } catch (error) {
        console.error("Error fetching chat history:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

// POST - Generate new answer
export async function POST(request: Request) {
    try {
        const { userId } = await auth();

        // Parse request and get application data
        const { question, applicationId } = await request.json();

        // Get application data from database
        const application = applicationId
            ? await db.query.jobApplications.findFirst({
                  where: eq(jobApplications.id, parseInt(applicationId)),
              })
            : null;

        const jobDescription = application?.jobDescription;
        const resumeId = application?.resumeId;

        // Get resume content if available
        let resume;
        if (resumeId) {
            const resumeData = await db.query.resumes.findFirst({
                where: eq(resumes.id, resumeId),
            });
            resume = resumeData?.parsedContent;
        }

        // Create context for the AI
        let context = "";
        if (jobDescription) context += `Job Description: ${jobDescription}\n`;
        if (resume) context += `Parsed Resume Summary: ${resume}\n`;

        // Generate prompt for the AI
        const prompt = `
    You are an AI assistant helping a job applicant prepare answers for interview or application questions.

    CONTEXT ABOUT THE JOB:
    ${context}

    USER QUESTION: ${question}

    Please provide a well-structured, professional response that:
    1. Is personalized based on the job and company
    2. Highlights relevant qualifications and experience
    3. Shows enthusiasm and cultural fit
    4. Is concise yet comprehensive (max 3-4 paragraphs)
    5. Uses professional, confident language
    6. Is formatted for the application
    7. Answer in 1-2 sentences and make it sound like a real person
    8. Make sure to use the right tone for the question
    Your response should be ready to use or easily adaptable for the application.
    `;

        // Generate answer using Gemini singleton
        const answer = await gemini.generateContent(prompt);

        // Store chat history in database
        // ...

        return NextResponse.json({ answer }, { status: 200 });
    } catch (error) {
        console.error("Error generating answer:", error);
        return NextResponse.json(
            { error: "Failed to generate answer" },
            { status: 500 }
        );
    }
}
```

### React Query Hooks

The feature uses two custom hooks to interact with the API:

```typescript
// Fetch chat history
export function useApplicationChat(applicationId: string) {
    return useQuery({
        queryKey: ["application-chat", applicationId],
        queryFn: async () => {
            const response = await fetch(
                `/api/ai/application-questions?applicationId=${applicationId}`,
                {
                    credentials: "include",
                }
            );
            if (!response.ok) {
                throw new Error("Failed to fetch chat history");
            }
            return response.json();
        },
        enabled: !!applicationId,
    });
}

// Generate new answers
export function useAnswerApplicationQuestion() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            question,
            applicationId,
        }: {
            question: string;
            applicationId: string;
        }) => {
            const response = await fetch("/api/ai/application-questions", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ question, applicationId }),
                credentials: "include",
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Failed to generate answer");
            }

            return response.json();
        },
        onSuccess: (_, variables) => {
            // Update chat history after successfully generating an answer
            queryClient.invalidateQueries({
                queryKey: ["application-chat", variables.applicationId],
            });
            toast.success("Answer generated successfully");
        },
        onError: (error) =>
            toast.error(
                error instanceof Error
                    ? error.message
                    : "Failed to generate answer"
            ),
    });
}
```

## Database Schema

Chat history is stored in the `generatedDocuments` table using a JSONB column:

```typescript
export const ChatMessageSchema = z.object({
    speaker: z.enum(["user", "bot"]),
    time: z.coerce.date(),
    message: z.string(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type ChatHistory = ChatMessage[];

export const generatedDocuments = pgTable("generated_documents", {
    // ... other fields
    chat: jsonb("chat").$type<ChatHistory>().default([]),
    // ... other fields
});
```

## User Flow

1. User navigates to the application content page and clicks on the "Questions" tab
2. The chat interface loads with a welcome message and suggested questions
3. User selects a suggested question or types their own
4. Question is sent to the API which generates a personalized answer
5. Answer is displayed in the chat interface and saved to the database
6. Chat history persists between sessions, allowing users to reference past answers

## Error Handling

The feature includes robust error handling:

1. **Authentication Verification**: All API requests verify user authentication
2. **Application Ownership Validation**: Ensures users only access their own applications
3. **Input Validation**: Validates all user inputs before processing
4. **Graceful Error Recovery**: Displays user-friendly error messages
5. **Loading States**: Provides visual feedback during API requests
6. **Optimistic Updates**: Updates UI immediately while waiting for API responses

## AI Prompt Engineering

The prompts for the Application Question Assistant are designed to:

1. **Provide Context**: Include relevant job and company details
2. **Generate Professional Responses**: Create polished, professional answers
3. **Maintain Natural Tone**: Ensure responses sound like a real person
4. **Optimize Length**: Keep responses concise but comprehensive
5. **Highlight Relevant Experience**: Focus on qualifications relevant to the job
6. **Adapt Tone Appropriately**: Adjust the tone based on the specific question type

## Future Enhancements

Planned improvements for the Application Question Assistant include:

1. **Answer Customization**: Allow users to edit and save AI-generated answers
2. **Export Functionality**: Export answers for use in applications
3. **More Suggested Questions**: Expand the library of suggested questions
4. **Categorized Questions**: Organize questions by category (skills, experience, etc.)
5. **Job-Specific Questions**: Generate questions based on job description
6. **Resume Integration**: Better integration with resume data for more personalized answers
7. **Speech-to-Text**: Allow users to speak their questions
8. **Multi-Turn Conversations**: Support follow-up questions based on context
