# Application Tracking

HireRizz provides a comprehensive application tracking system to help users manage their job applications efficiently.

## Overview

The application tracking system allows users to:

-   Track all job applications in one place
-   Filter applications by status
-   View detailed information about each application
-   Update application status as they progress through the hiring process
-   Store relevant information like job descriptions, contacts, and notes

## Application Statuses

Applications can have the following statuses:

| Status       | Description                                         | Visual Indicator              |
| ------------ | --------------------------------------------------- | ----------------------------- |
| Not Applied  | Job opportunities identified but not yet applied to | Blue badge with calendar icon |
| Applied      | Applications that have been submitted               | Yellow badge with clock icon  |
| Interviewing | Applications in the interview stage                 | Purple badge with phone icon  |
| Offer        | Jobs where an offer has been received               | Orange badge with award icon  |
| Accepted     | Jobs where the offer has been accepted              | Green badge with check icon   |
| Rejected     | Applications that were rejected                     | Red badge with X icon         |

## Filtering System

The application tracking system includes a robust filtering system that allows users to view applications by status.

### Status Filtering

Users can filter applications by status using the filter badges at the top of the applications page:

```tsx
// src/app/dashboard/applications/search-filters.tsx
export function SearchFilters() {
    // ...

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div className="flex flex-wrap gap-2">
                    <Badge
                        variant={!currentStatus ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleStatusClick("")}
                    >
                        All
                    </Badge>

                    {VALID_STATUSES.map((status) => (
                        <Badge
                            key={status}
                            variant={
                                currentStatus === status ? "default" : "outline"
                            }
                            className="cursor-pointer"
                            onClick={() => handleStatusClick(status)}
                        >
                            {statusLabels[status] || status}
                        </Badge>
                    ))}
                </div>
            </div>
        </div>
    );
}
```

### API Integration

The filtering system is integrated with the API to efficiently fetch only the necessary data:

```tsx
// src/lib/hooks/api-hooks.ts
export function useFilteredApplications(filters?: {
    status?: ApplicationStatus;
}) {
    return useQuery({
        queryKey: ["applications", filters?.status],
        queryFn: async () => {
            let url = "/api/applications";

            if (filters?.status) {
                url += `?status=${filters.status}`;
            }

            const response = await fetch(url);
            if (!response.ok) throw new Error("Failed to fetch applications");

            return response.json();
        },
        staleTime: 30 * 1000, // 30 seconds
    });
}
```

## Application Card UI

Each application is displayed in a card format with the following information:

-   Company name
-   Position title
-   Location (if available)
-   Creation date
-   Status badge

```tsx
<Card className="cursor-pointer hover:bg-accent/5 transition-colors">
    <CardContent className="p-4 sm:p-6 flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                <h3 className="font-medium">{application.company}</h3>
                <div className="flex items-center space-x-2">
                    <div
                        className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${statusConfig[statusKey]?.color}`}
                    >
                        <StatusIcon className="h-3 w-3" />
                        <span>
                            {statusConfig[statusKey]?.label || statusKey}
                        </span>
                    </div>
                </div>
            </div>
            <p className="text-sm text-muted-foreground mb-1">
                {application.position}
                {application.location && ` • ${application.location}`}
            </p>
            <p className="text-xs text-muted-foreground">
                Created {format(new Date(application.createdAt), "MMM d, yyyy")}
            </p>
        </div>
    </CardContent>
</Card>
```

## Status Updates

Users can update the status of an application from the application detail page:

1. The current status is displayed at the top of the page
2. Status update buttons allow changing to any available status
3. Status updates are processed through the API and update the database
4. The UI is updated immediately through React Query's optimistic updates

```tsx
// Status update mutation
export function useUpdateApplicationStatus() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ id, status, application }) => {
            const response = await fetch(`/api/applications/${id}`, {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ ...application, status }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || "Failed to update status");
            }

            return response.json();
        },
        onSuccess: (data, { id }) => {
            // Update the cache with the new data
            queryClient.setQueryData(["application", id], data);
            // Invalidate the applications list to reflect the status change
            queryClient.invalidateQueries({ queryKey: ["applications"] });
            // Show success message
            toast.success(`Application status updated successfully`);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
}
```

## Error Handling

The application tracking system includes robust error handling to ensure a smooth user experience:

1. API errors are caught and displayed to the user
2. Network issues are handled with appropriate fallbacks
3. Loading states are shown during data fetching
4. Empty states are displayed when no applications are found

## Responsive Design

The application tracking UI is fully responsive and works well on mobile, tablet, and desktop devices:

-   On mobile, the cards stack vertically and the content is reorganized
-   On larger screens, the layout uses more horizontal space
-   The filters and status badges adapt to the screen size

## Data Persistence

All application data is stored in the database and linked to the user's account, ensuring that:

1. Data is available across devices
2. Changes are persisted between sessions
3. Applications can be accessed and updated at any time

## Conclusion

The HireRizz application tracking system provides a comprehensive solution for managing job applications throughout the entire process, from identification to acceptance or rejection.
