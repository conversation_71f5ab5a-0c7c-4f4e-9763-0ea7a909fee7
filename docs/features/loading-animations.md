# Loading Animations

This document provides an overview of the loading animation system implemented across the application to provide consistent and informative loading states for users.

## Overview

The loading animation system provides a unified approach to loading states throughout the application. It features:

-   Multiple animation variants for different contexts
-   Customizable sizes
-   Support for descriptive text and subtext
-   Color customization options
-   Seamless integration with Next.js App Router loading states

## Implementation

### Core Component

The core of the system is the `LoadingAnimation` component located at `src/components/ui/loading-animation.tsx`. This component uses Framer Motion to create smooth, engaging animations.

```tsx
// Basic usage
<LoadingAnimation
    variant="pulse"
    size="md"
    text="Loading data..."
    subText="Please wait while we fetch your information"
/>
```

### Animation Variants

The system supports four distinct animation variants:

1. **Pulse** (`variant="pulse"`) - Default animation with pulsing circles, ideal for general loading states
2. **Orbit** (`variant="orbit"`) - Orbiting elements around a center point, suitable for processing operations
3. **Bloom** (`variant="bloom"`) - Expanding and contracting elements, good for transitions
4. **Ripple** (`variant="ripple"`) - Ripple effect, perfect for button loading states

### Sizes

Three sizes are available:

-   **Small** (`size="sm"`) - Compact loading indicator for inline or button contexts
-   **Medium** (`size="md"`) - Default size for component-level loading
-   **Large** (`size="lg"`) - Full-size loading for page-level feedback

### Color Customization

The component supports custom colors through the `color` prop:

```tsx
<LoadingAnimation
    variant="pulse"
    size="md"
    color="primary" // "primary", "secondary", "accent", or "neutral"
/>
```

## Integration with Next.js App Router

The loading system integrates with Next.js 13+ App Router through `loading.tsx` files:

```tsx
// src/app/some-route/loading.tsx
"use client";

import { LoadingAnimation } from "@/components/ui/loading-animation";

export default function PageLoading() {
    return (
        <div className="container py-8">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
                <LoadingAnimation
                    variant="bloom"
                    size="lg"
                    text="Loading Content"
                    subText="Preparing your data..."
                />
            </div>
        </div>
    );
}
```

## Loading State During Navigation

For route transitions, the system provides feedback by changing button states:

```tsx
const [isNavigating, setIsNavigating] = useState(false);

const handleClick = () => {
    setIsNavigating(true);
    router.push("/destination-route");
};

<Button onClick={handleClick} disabled={isNavigating}>
    {isNavigating ? (
        <div className="flex items-center gap-2">
            <LoadingAnimation variant="ripple" size="sm" />
            <span>Loading...</span>
        </div>
    ) : (
        "Click Me"
    )}
</Button>;
```

## Demo Page

A comprehensive demo of all loading variants and sizes is available at `/loadingdemo` in the application. This provides a quick reference for all available loading animations and their options.

## Best Practices

1. **Use appropriate variants for context**:

    - `pulse` for general loading
    - `orbit` for processing/analysis operations
    - `bloom` for page transitions
    - `ripple` for button/action feedback

2. **Provide informative text when possible**:

    - Use the `text` prop for the main loading message
    - Use `subText` for additional context about what's happening

3. **Error handling**:

    - Always wrap loading components with error boundaries for graceful fallbacks

4. **Progress indication**:
    - For long operations, consider pairing the loading animation with progress indicators when possible

## Implementation Examples

### Page Loading State

```tsx
// src/app/dashboard/resumes/upload/loading.tsx
export default function UploadResumeLoading() {
    return (
        <div className="container py-8">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
                <LoadingAnimation
                    variant="orbit"
                    size="lg"
                    text="Opening Resume Upload"
                    subText="Preparing upload interface..."
                />
            </div>
        </div>
    );
}
```

### Button Loading State

```tsx
<Button onClick={handleUploadClick} disabled={isUploading}>
    {isUploading ? (
        <>
            <LoadingAnimation
                variant="ripple"
                size="sm"
                className="mr-2 inline-block"
            />
            Uploading...
        </>
    ) : (
        <>
            <Upload className="mr-2 h-4 w-4" />
            Upload File
        </>
    )}
</Button>
```

### Component Loading State

```tsx
{loading ? (
  <div className="flex justify-center py-12">
    <LoadingAnimation
      variant="pulse"
      size="md"
      text="Loading applications..."
    />
  </div>
) : (
  // Component content
)}
```
