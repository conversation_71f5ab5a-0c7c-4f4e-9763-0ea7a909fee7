# JobCraft Environment Setup Guide

This comprehensive guide provides step-by-step instructions for setting up the development environment for JobCraft, including all required environment variables, API keys, and external services.

## 🔧 Prerequisites

Before setting up JobCraft, ensure you have the following installed:

-   **Node.js** (v18 or later) - [Download](https://nodejs.org/)
-   **npm** (comes with Node.js) or **yarn**
-   **Git** - [Download](https://git-scm.com/)
-   **Code Editor** - VS Code recommended

## 📥 Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/BeLazy167/JobCraft.git
cd JobCraft
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Create Environment File

```bash
cp .env.local.example .env.local
# or manually create
touch .env.local
```

## 🔐 Environment Variables

Create a `.env.local` file in the root directory with the following configuration:

### **Core Configuration**

```env
# App Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database (Neon)
DATABASE_URL="********************************************************************"
DIRECT_URL="*****************************************************************************************************"
```

### **Authentication (Clerk)**

```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_..."
CLERK_SECRET_KEY="sk_test_..."
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"
```

### **File Storage (UploadThing)**

```env
# UploadThing Configuration
UPLOADTHING_SECRET="sk_live_..."
UPLOADTHING_APP_ID="your-app-id"
```

### **AI Services**

```env
# Google Gemini AI (Primary)
GOOGLE_AI_API_KEY="AIza..."

# OpenAI (Secondary/Fallback)
OPENAI_API_KEY="sk-..."

# Web Scraping
FIRECRAWL_API_KEY="fc-..."
```

### **Analytics & Monitoring (Optional)**

```env
# Google Analytics
NEXT_PUBLIC_GA_ID="G-..."

# Logging Level
LOG_LEVEL="INFO"
```

## 🔑 API Keys Setup Guide

### 1. **Neon Database** 🗄️

1. Go to [Neon Console](https://console.neon.tech/)
2. Create a new project
3. Choose your region (preferably close to your users)
4. Copy the connection string from the dashboard
5. Update both `DATABASE_URL` and `DIRECT_URL` in your `.env.local`

**Note**: Neon provides both pooled and direct connection strings. Use the direct URL for migrations.

### 2. **Clerk Authentication** 🔐

1. Visit [Clerk Dashboard](https://dashboard.clerk.dev/)
2. Create a new application
3. Choose your authentication providers
4. Navigate to **API Keys** section
5. Copy the **Publishable Key** and **Secret Key**
6. Configure redirect URLs:
    - Sign-in URL: `http://localhost:3000/sign-in`
    - Sign-up URL: `http://localhost:3000/sign-up`
    - After sign-in: `http://localhost:3000/dashboard`

### 3. **UploadThing File Storage** 📁

1. Go to [UploadThing Dashboard](https://uploadthing.com/dashboard)
2. Create a new application
3. Configure file upload settings:
    - Max file size: 10MB
    - Allowed types: PDF, DOC, DOCX
4. Copy the **Secret Key** and **App ID**
5. Add them to your environment variables

### 4. **Google Gemini AI** 🤖

1. Visit [Google AI Studio](https://ai.google.dev/)
2. Create a new API key
3. Enable the Gemini Pro model
4. Copy the API key (starts with `AIza`)
5. Add to `GOOGLE_AI_API_KEY`

**Free Tier**: 15 requests per minute, 1,500 requests per day

### 5. **OpenAI (Optional)** 🧠

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an API key
3. Add billing information (pay-per-use)
4. Copy the API key (starts with `sk-`)
5. Add to `OPENAI_API_KEY`

### 6. **Firecrawl Web Scraping** 🌐

1. Visit [Firecrawl](https://firecrawl.dev/)
2. Sign up for an account
3. Get your API key from the dashboard
4. Add to `FIRECRAWL_API_KEY`

**Free Tier**: 500 pages per month

## 🗄️ Database Setup

### **Neon Database Configuration**

1. **Create Database Schema**:

    ```bash
    npm run db:generate
    npm run db:push
    ```

2. **Verify Connection**:

    ```bash
    npm run db:studio
    ```

    This opens Drizzle Studio to view your database.

3. **Seed Data (Optional)**:
    ```bash
    # If you have seed scripts
    npm run db:seed
    ```

### **Database Features**

-   ✅ **Serverless**: Auto-scaling PostgreSQL
-   ✅ **Branching**: Database branches for different environments
-   ✅ **Connection Pooling**: Built-in connection management
-   ✅ **SSL**: Secure connections by default

## 🚀 Running the Application

### **Development Mode**

```bash
npm run dev
# or
yarn dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

### **Production Build**

```bash
npm run build
npm start
```

### **Database Management**

```bash
# Generate migrations
npm run db:generate

# Push schema changes
npm run db:push

# Open database studio
npm run db:studio
```

## ✅ Verification Checklist

To verify your setup is working correctly:

### **Authentication Test**

1. ✅ Navigate to [http://localhost:3000](http://localhost:3000)
2. ✅ Click "Sign Up" - should redirect to Clerk
3. ✅ Create an account
4. ✅ Verify redirect to dashboard

### **Database Test**

1. ✅ Access dashboard after login
2. ✅ Check that user data is created
3. ✅ Verify database connection in logs

### **File Upload Test**

1. ✅ Navigate to resume upload page
2. ✅ Upload a PDF/DOC file
3. ✅ Verify file appears in UploadThing dashboard
4. ✅ Check file URL is accessible

### **AI Integration Test**

1. ✅ Create a job application
2. ✅ Generate content (cover letter/LinkedIn message)
3. ✅ Verify AI-generated content appears
4. ✅ Check API usage in respective dashboards

## 🔧 Development Tools

### **Recommended VS Code Extensions**

-   **TypeScript Hero** - Enhanced TypeScript support
-   **Tailwind CSS IntelliSense** - CSS class autocomplete
-   **ESLint** - Code linting
-   **Prettier** - Code formatting
-   **Drizzle Kit** - Database schema management

### **Useful Scripts**

```bash
# Development
npm run dev              # Start dev server
npm run build           # Build for production
npm run start           # Start production server
npm run lint            # Run ESLint

# Database
npm run db:generate     # Generate migrations
npm run db:push         # Push schema to database
npm run db:studio       # Open Drizzle Studio

# Type Checking
npm run type-check      # Run TypeScript compiler
```

## 🐛 Troubleshooting

### **Common Issues & Solutions**

#### **Database Connection Errors**

```bash
Error: connection refused / timeout
```

**Solutions**:

-   ✅ Verify Neon database is running
-   ✅ Check `DATABASE_URL` format
-   ✅ Ensure SSL is enabled
-   ✅ Try using `DIRECT_URL` for migrations

#### **Authentication Errors**

```bash
Error: Invalid publishable key
```

**Solutions**:

-   ✅ Verify Clerk API keys are correct
-   ✅ Check environment variable names
-   ✅ Ensure keys are for correct environment
-   ✅ Verify redirect URLs in Clerk dashboard

#### **File Upload Errors**

```bash
Error: UploadThing configuration invalid
```

**Solutions**:

-   ✅ Verify UploadThing API keys
-   ✅ Check file size limits (10MB max)
-   ✅ Ensure file types are allowed (PDF, DOC, DOCX)
-   ✅ Verify CORS settings

#### **AI Integration Errors**

```bash
Error: API key invalid / quota exceeded
```

**Solutions**:

-   ✅ Verify API keys are active
-   ✅ Check quota limits in respective dashboards
-   ✅ Ensure billing is set up (OpenAI)
-   ✅ Try switching between Gemini/OpenAI

#### **Build Errors**

```bash
Error: TypeScript compilation failed
```

**Solutions**:

-   ✅ Run `npm run type-check`
-   ✅ Fix TypeScript errors
-   ✅ Ensure all dependencies are installed
-   ✅ Clear `.next` folder and rebuild

## 📱 Environment-Specific Configuration

### **Development**

```env
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
LOG_LEVEL=DEBUG
```

### **Staging**

```env
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://staging-jobcraft.vercel.app
LOG_LEVEL=INFO
```

### **Production**

```env
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://jobcraft.app
LOG_LEVEL=WARN
```

## 🔒 Security Best Practices

### **Environment Variables**

-   ✅ Never commit `.env.local` to version control
-   ✅ Use different API keys for different environments
-   ✅ Rotate API keys regularly
-   ✅ Use least privilege access for all services

### **Database Security**

-   ✅ Use SSL connections (enabled by default in Neon)
-   ✅ Regular backups (automated in Neon)
-   ✅ Monitor access logs
-   ✅ Use read-only connections where possible

## 📞 Getting Help

### **Resources**

-   📖 [JobCraft Documentation](../README.md)
-   🐛 [GitHub Issues](https://github.com/BeLazy167/JobCraft/issues)
-   💬 [Discussions](https://github.com/BeLazy167/JobCraft/discussions)

### **External Documentation**

-   [Next.js Documentation](https://nextjs.org/docs)
-   [Neon Documentation](https://neon.tech/docs)
-   [Clerk Documentation](https://clerk.dev/docs)
-   [UploadThing Documentation](https://docs.uploadthing.com/)
-   [Drizzle ORM Documentation](https://orm.drizzle.team/)

---

**Setup Guide Version**: 2.0  
**Last Updated**: January 2025  
**Compatible with**: JobCraft v1.0+
