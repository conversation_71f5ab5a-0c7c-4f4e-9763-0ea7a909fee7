# JobCraft API Documentation

This comprehensive documentation covers all JobCraft API endpoints, integrations, and backend services.

## 🚀 Overview

JobCraft provides a robust REST API built on Next.js API routes with comprehensive authentication, logging, and error handling. All endpoints are designed for scalability and reliability.

## 🔐 Authentication

All API endpoints are protected by **Clerk authentication**. Every request requires a valid authentication token.

### Authentication Headers

```http
Authorization: Bearer <clerk-session-token>
Cookie: __session=<clerk-session-cookie>
```

### Authentication Middleware

-   **Location**: `src/lib/auth.ts`
-   **Functions**: `getServerAuth()`, `getOrCreateUser()`
-   **Standardized**: Consistent auth handling across all routes

## 📡 API Endpoints

### **Resume Management** 📄

#### Resume Upload & Management

-   **POST** `/api/resumes` - Create new resume record
-   **GET** `/api/resumes` - List user's resumes
-   **GET** `/api/resumes/[id]` - Get specific resume
-   **DELETE** `/api/resumes/[id]/delete` - Delete resume with comprehensive cleanup

**Resume Deletion Features**:

-   ✅ Database cascade deletion (updates job applications)
-   ✅ UploadThing file storage cleanup
-   ✅ Comprehensive error handling and logging
-   ✅ Dual deletion strategy (filePath + URL fallback)

#### Resume Processing

-   **POST** `/api/resumes/parse` - Parse uploaded resume files
-   **Support**: PDF, DOC, DOCX formats
-   **AI Integration**: Content extraction and analysis

### **File Storage** 📁

#### UploadThing Integration

-   **DELETE** `/api/uploadthing/delete` - Delete files from UploadThing storage
-   **Core Endpoint**: `/api/uploadthing` - File upload handling
-   **Features**:
    -   Secure file uploads
    -   Automatic cleanup on removal
    -   Progress tracking
    -   File validation

### **Application Management** 📋

#### Application CRUD Operations

-   **GET** `/api/applications` - List user's applications
-   **POST** `/api/applications` - Create new application
-   **GET** `/api/applications/[id]` - Get application details
-   **PUT** `/api/applications/[id]` - Update application
-   **DELETE** `/api/applications/[id]` - Delete application

#### Application Content Management

-   **GET** `/api/applications/[id]/content` - Get generated content
-   **POST** `/api/applications/[id]/content` - Generate new content
-   **PUT** `/api/applications/[id]/field` - Update specific fields
-   **GET** `/api/applications/[id]/resumes` - Get linked resumes

#### Application Chat & AI

-   **POST** `/api/applications/[id]/chat` - Application-specific chat
-   **POST** `/api/applications/[id]/ai-reply` - AI-powered responses

### **AI Services** 🤖

#### Content Generation

-   **POST** `/api/ai/generate-content` - Generate personalized content
    -   Cover letters
    -   LinkedIn messages
    -   Cold emails
    -   Application answers

#### Job Analysis

-   **POST** `/api/ai/analyze-job` - Analyze job descriptions
-   **POST** `/api/ai/analyze-job-link` - Analyze job from URL

#### Application Questions

-   **GET** `/api/ai/application-questions` - Get chat history
-   **POST** `/api/ai/application-questions` - Generate question responses

#### Interview Preparation

-   **POST** `/api/ai/interview-chat` - Interview practice sessions

### **Utility Endpoints** 🔧

#### URL Management

-   **POST** `/api/urls/link-application` - Link applications to job URLs
-   **GET** `/api/urls/stats` - URL analytics and statistics

#### Export Functionality

-   **GET** `/api/applications/export` - Export applications data

## 🧠 AI Integration

### **Google Gemini AI**

-   **Primary**: Content generation and analysis
-   **Model**: Gemini Pro for text generation
-   **Features**: Context-aware, personalized responses

### **OpenAI Integration**

-   **Secondary**: Advanced text processing
-   **Model**: GPT for specialized tasks
-   **Fallback**: When Gemini is unavailable

### **Firecrawl Integration**

-   **Purpose**: Web scraping for job analysis
-   **Features**: Extract job details from URLs
-   **Rate Limited**: Intelligent usage patterns

## 📊 Database Integration

### **Neon Database**

-   **Type**: Serverless PostgreSQL
-   **ORM**: Drizzle ORM with TypeScript
-   **Features**: Connection pooling, SSL support

### **Core Tables**

```typescript
// Users - Clerk integration
users: {
  id: string (Clerk ID)
  email: string
  firstName: string
  lastName: string
  createdAt: timestamp
}

// Resumes - File management
resumes: {
  id: serial
  userId: string (FK)
  name: string
  fileUrl: string
  filePath: string (UploadThing key)
  fileType: string
  isDefault: boolean
  parsedContent: json
}

// Applications - Job tracking
jobApplications: {
  id: serial
  userId: string (FK)
  resumeId: integer (FK)
  company: string
  position: string
  status: enum
  coverLetter: text
  linkedinMessage: text
  coldEmail: text
  appQuestions: text
}
```

## 🔍 Comprehensive Logging

### **Logging Features**

-   **Request IDs**: Unique tracking per request
-   **Performance Metrics**: Timing for all operations
-   **Error Tracking**: Full stack traces and context
-   **Operation Logging**: Database and storage operations

### **Log Levels**

```env
LOG_LEVEL=DEBUG|INFO|WARN|ERROR
```

### **Log Format**

```
[OPERATION:requestId] === Action ===
[OPERATION:requestId] Step completed in XXXms
[OPERATION:requestId] === Operation completed/failed ===
```

## ⚡ Performance & Reliability

### **Caching Strategy**

-   **React Query**: Client-side data caching
-   **Database**: Optimized queries with indexes
-   **File Storage**: CDN-optimized delivery

### **Error Handling**

-   **Standardized**: Consistent error responses
-   **Graceful Degradation**: Fallback mechanisms
-   **User-Friendly**: Clear error messages

### **Rate Limiting**

-   **AI Endpoints**: 5 requests/minute per user
-   **File Operations**: 10 requests/minute per user
-   **Standard Endpoints**: 60 requests/minute per user

## 🔒 Security Features

### **Data Protection**

-   **Authentication**: Clerk-based security
-   **Authorization**: User-scoped data access
-   **Input Validation**: Zod schema validation
-   **SQL Injection**: Parameterized queries with Drizzle

### **File Security**

-   **Upload Validation**: File type and size checks
-   **Storage Security**: UploadThing secure uploads
-   **Access Control**: User-restricted file access

## 📋 API Response Format

### **Success Response**

```json
{
  "success": true,
  "data": { ... },
  "metadata": {
    "requestId": "req_123456789",
    "processingTimeMs": 150
  }
}
```

### **Error Response**

```json
{
    "error": "Error type",
    "message": "Human-readable message",
    "metadata": {
        "requestId": "req_123456789",
        "processingTimeMs": 85
    },
    "details": "Development details (dev only)"
}
```

## 🛠️ Development Tools

### **API Testing**

```bash
# Example: Get user applications
curl -X GET "http://localhost:3000/api/applications" \
  -H "Authorization: Bearer your-clerk-token"

# Example: Delete resume
curl -X DELETE "http://localhost:3000/api/resumes/123/delete" \
  -H "Authorization: Bearer your-clerk-token"
```

### **Database Tools**

```bash
npm run db:studio    # Open Drizzle Studio
npm run db:generate  # Generate migrations
npm run db:push      # Push schema changes
```

## 📈 Monitoring & Analytics

### **Request Tracking**

-   Unique request IDs for all operations
-   Performance timing for optimization
-   Error rates and patterns
-   User activity patterns

### **File Storage Metrics**

-   Upload success rates
-   Storage cleanup efficiency
-   File type distribution
-   Usage patterns

---

**API Version**: 1.0  
**Last Updated**: January 2025  
**Documentation Maintained by**: JobCraft Development Team
