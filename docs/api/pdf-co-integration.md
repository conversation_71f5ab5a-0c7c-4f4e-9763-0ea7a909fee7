# PDF.co API Integration

HireRizz integrates with PDF.co's API for efficient text extraction from PDF documents. This integration enables the application to convert PDF resumes into plain text for further processing by the AI parsing system.

## Overview

PDF.co provides a robust API for PDF processing, including text extraction, conversion, and more. HireRizz specifically uses the `/pdf/convert/to/text-simple` endpoint to extract text from PDF files.

## Integration Points

The PDF.co integration is primarily used in the resume parsing feature for:

-   Converting PDF resumes to plain text
-   Maintaining text formatting for better parsing results
-   Handling complex PDF documents that may be difficult to parse with other libraries

## Implementation

### API Endpoint Used

HireRizz uses the following PDF.co endpoint:

```
https://api.pdf.co/v1/pdf/convert/to/text-simple
```

### Authentication

Authentication is done via an API key passed in the `x-api-key` header:

```typescript
const apiKey = process.env.PDF_CO_API_KEY;
if (!apiKey) throw new Error("PDF.co API key not configured");

const convertResponse = await fetch(
    "https://api.pdf.co/v1/pdf/convert/to/text-simple",
    {
        method: "POST",
        headers: {
            "x-api-key": apiKey,
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            url: fileUrl,
            inline: true,
            async: false,
        }),
    }
);
```

### Request Parameters

The API request includes the following parameters:

| Parameter | Description                                         |
| --------- | --------------------------------------------------- |
| `url`     | URL to the source PDF file (from UploadThing)       |
| `inline`  | Set to `true` to return results inside the response |
| `async`   | Set to `false` for synchronous processing           |

### Response Handling

The API response is processed to extract the text content:

```typescript
if (!convertResponse.ok) throw new Error("PDF.co text conversion failed");
const { body: extractedText } = await convertResponse.json();
return extractedText;
```

## Error Handling

The integration includes error handling for various scenarios:

1. **Missing API Key**: Checks if the API key is configured
2. **Missing File URL**: Ensures a valid file URL is provided
3. **API Request Failure**: Handles non-200 responses from the API
4. **Parsing Errors**: Catches and logs any errors during the text extraction process

## Configuration

To configure the PDF.co integration, add the following to your environment variables:

```
PDF_CO_API_KEY=your_api_key_here
```

You can obtain an API key by signing up at [PDF.co](https://pdf.co/).

## Usage Example

Here's a complete example of how the PDF.co integration is used in the resume parsing process:

```typescript
async function parseFileContent(
    fileBuffer: Buffer,
    fileType: string,
    fileUrl?: string
): Promise<string> {
    if (
        !fileType ||
        !SUPPORTED_FILE_TYPES.some((type) => fileType.includes(type))
    ) {
        throw new Error(`Unsupported file type: ${fileType}`);
    }

    if (fileType.includes("pdf")) {
        console.log("Processing PDF content with PDF.co API");
        const apiKey = process.env.PDF_CO_API_KEY;
        if (!apiKey) throw new Error("PDF.co API key not configured");
        if (!fileUrl) throw new Error("File URL required for PDF.co");

        // Use the existing file URL directly with PDF.co
        const convertResponse = await fetch(
            "https://api.pdf.co/v1/pdf/convert/to/text-simple",
            {
                method: "POST",
                headers: {
                    "x-api-key": apiKey,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    url: fileUrl,
                    inline: true,
                    async: false,
                }),
            }
        );

        if (!convertResponse.ok)
            throw new Error("PDF.co text conversion failed");
        const { body: extractedText } = await convertResponse.json();
        return extractedText;
    } else if (fileType.includes("docx") || fileType.includes("doc")) {
        // Handle Word documents with Mammoth.js
        const result = await mammoth.extractRawText({ buffer: fileBuffer });
        return result.value;
    } else {
        // Handle plain text files
        return fileBuffer.toString("utf-8");
    }
}
```

## Limitations

-   The PDF.co API has rate limits based on your subscription plan
-   Very large PDF files may require asynchronous processing
-   Some heavily formatted or scanned PDFs may not extract perfectly

## Alternatives Considered

Before choosing PDF.co, we evaluated several alternatives:

1. **pdf.js**: Browser-based PDF parsing, but less reliable for complex documents
2. **pdf-parse**: Simple Node.js library, but limited formatting preservation
3. **Adobe PDF Services API**: More features but higher complexity and cost

PDF.co was selected for its balance of simplicity, reliability, and cost-effectiveness.

## Future Improvements

-   Implement caching to reduce API calls for previously processed files
-   Add support for OCR processing of scanned documents
-   Explore batch processing for multiple files
