# Authentication Middleware

This document describes the authentication middleware used in the HireRizz API routes.

## Overview

HireRizz uses <PERSON> for authentication and implements custom middleware functions to standardize authentication checks across API routes. These middleware functions simplify route handlers by abstracting away common authentication logic and error handling.

## Authentication Middleware Functions

The application provides two main authentication middleware functions:

1. **`withAuth`**: For client-side authentication (with request object)
2. **`withServerAuth`**: For server-side authentication

Both functions are defined in `src/lib/auth.ts` and provide consistent authentication checks and error handling.

## Implementation

```typescript
/**
 * Authentication middleware for API routes
 * @param req The Next.js request object
 * @param handler The handler function to execute if authentication is successful
 * @returns A Promise that resolves to a NextResponse
 */
export async function withAuth<T>(
    req: NextRequest,
    handler: (userId: string) => Promise<T>
) {
    const { userId } = getAuth(req);
    
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    try {
        return await handler(userId);
    } catch (error) {
        console.error("Error in authenticated route:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

/**
 * Authentication middleware for API routes using server-side auth
 * @param handler The handler function to execute if authentication is successful
 * @returns A Promise that resolves to a NextResponse
 */
export async function withServerAuth<T>(
    handler: (userId: string) => Promise<T>
) {
    const { userId } = auth();
    
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    try {
        return await handler(userId);
    } catch (error) {
        console.error("Error in authenticated route:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
```

## Usage Examples

### Client-Side Authentication (with request object)

```typescript
import { NextRequest, NextResponse } from "next/server";
import { withAuth } from "@/lib/auth";

export async function GET(request: NextRequest) {
    return withAuth(request, async (userId) => {
        // Your authenticated code here
        // userId is guaranteed to be valid
        
        return NextResponse.json({ success: true, data: "Your data" });
    });
}
```

### Server-Side Authentication

```typescript
import { NextResponse } from "next/server";
import { withServerAuth } from "@/lib/auth";

export async function GET() {
    return withServerAuth(async (userId) => {
        // Your authenticated code here
        // userId is guaranteed to be valid
        
        return NextResponse.json({ success: true, data: "Your data" });
    });
}
```

## Benefits

Using these middleware functions provides several benefits:

1. **Code Reduction**: Eliminates repetitive authentication code in each route
2. **Consistency**: Ensures all routes handle authentication in the same way
3. **Maintainability**: Makes it easier to update authentication logic in one place
4. **Error Handling**: Provides consistent error responses and logging
5. **Type Safety**: Uses TypeScript generics to ensure type safety

## Error Handling

The middleware functions handle common errors:

- **Authentication Errors**: Returns a 401 Unauthorized response if no user ID is found
- **Unexpected Errors**: Catches and logs any errors in the handler function, returning a 500 Internal Server Error response

## Best Practices

When implementing API routes:

1. **Always Use Middleware**: Use the appropriate middleware function for all authenticated routes
2. **Choose the Right Middleware**: Use `withAuth` when you need the request object, and `withServerAuth` when you don't
3. **Keep Handlers Focused**: The handler function should focus on business logic, not authentication
4. **Return Proper Responses**: Always return a NextResponse object from your handler function
5. **Handle Specific Errors**: For specific error cases, return appropriate status codes and error messages

## Related Documentation

- [Clerk Authentication](./clerk-integration.md)
- [API Overview](./README.md)
- [System Architecture](../architecture/system-overview.md)
