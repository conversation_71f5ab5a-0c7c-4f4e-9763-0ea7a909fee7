# Gemini AI Integration

HireRizz leverages Google's Gemini AI for intelligent resume parsing and content generation. This integration enables the application to extract structured information from resume text and generate personalized content for job applications.

## Overview

Gemini AI is Google's multimodal large language model that can understand and generate text, code, and more. HireRizz uses Gemini AI for:

1. **Resume Parsing**: Extracting structured information from resume text
2. **Content Generation**: Creating personalized cover letters and application materials
3. **Job Analysis**: Analyzing job descriptions to identify key requirements
4. **Application Question Assistant**: Providing AI-powered answers to common application questions

## Integration Points

The Gemini AI integration is used in several features:

-   **Resume Parsing**: Converting unstructured resume text to structured data
-   **Cover Letter Generation**: Creating personalized cover letters based on resume and job details
-   **Interview Preparation**: Generating potential interview questions based on job requirements
-   **Application Question Chat**: Interactive chat interface for answering job application questions

## Implementation

### Gemini Singleton

HireRizz uses a singleton pattern to manage Gemini AI instances, ensuring efficient resource usage and consistent configuration:

```typescript
// src/lib/ai/gemini.ts
import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";

class GeminiSingleton {
    private static instance: GeminiSingleton;
    private genAI: GoogleGenerativeAI;
    private models: Map<string, GenerativeModel> = new Map();
    private apiKey: string;

    private constructor(apiKey: string) {
        this.apiKey = apiKey;
        this.genAI = new GoogleGenerativeAI(apiKey);
    }

    public static getInstance(): GeminiSingleton {
        if (!GeminiSingleton.instance) {
            const apiKey = process.env.GEMINI_API_KEY;
            if (!apiKey) {
                throw new Error(
                    "GEMINI_API_KEY is not defined in environment variables"
                );
            }
            GeminiSingleton.instance = new GeminiSingleton(apiKey);
        }
        return GeminiSingleton.instance;
    }

    public async generateContent(
        prompt: string,
        options = {
            model: "gemini-2.0-flash",
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 8192,
        }
    ): Promise<string> {
        try {
            // Try with primary model first
            const model = this.getModel(options.model);
            const result = await model.generateContent(prompt);
            return result.response.text();
        } catch (error) {
            console.error(
                `Error with ${options.model}, falling back to gemini-pro:`,
                error
            );

            // Fall back to gemini-pro if primary model fails
            try {
                const fallbackModel = this.getModel("gemini-pro");
                const result = await fallbackModel.generateContent(prompt);
                return result.response.text();
            } catch (fallbackError) {
                console.error("Error with fallback model:", fallbackError);
                throw fallbackError;
            }
        }
    }

    private getModel(modelName: string): GenerativeModel {
        if (!this.models.has(modelName)) {
            this.models.set(
                modelName,
                this.genAI.getGenerativeModel({
                    model: modelName,
                })
            );
        }
        return this.models.get(modelName)!;
    }
}

// Export a single instance
export const gemini = GeminiSingleton.getInstance();
```

### Usage

The Gemini singleton is used across all API routes for AI-powered features:

```typescript
import { gemini } from "@/lib/ai/gemini";

// Generate content using the singleton
const generatedContent = await gemini.generateContent(prompt);
```

### Resume Parsing Implementation

For resume parsing, we use a specialized prompt to extract structured information:

```typescript
async function parseWithAI(fileContent: string): Promise<ResumeData | null> {
    console.log("Starting AI parsing");

    const truncatedContent = fileContent.slice(0, MAX_CONTENT_CHARS);
    console.log(`Truncated content length: ${truncatedContent.length} chars`);
    const prompt = generatePrompt(truncatedContent);

    console.log("Sending prompt to Gemini API");
    try {
        const responseText = await gemini.generateContent(prompt);
        console.log(
            `Received response from Gemini, length: ${responseText.length} chars`
        );

        try {
            const parsedData = JSON.parse(responseText);

            // Basic validation of the parsed data
            if (!parsedData || typeof parsedData !== "object") {
                console.log("Gemini returned invalid data format");
                return null;
            }

            // Normalize the data to ensure it conforms to our schema
            return normalizeResumeData(parsedData);
        } catch (parseError) {
            console.error("Error parsing Gemini response as JSON:", parseError);
            console.log("Raw response:", responseText);
            return null;
        }
    } catch (error) {
        console.error("Error generating content with Gemini:", error);
        return null;
    }
}
```

### Application Question Assistant

The application implements an interactive chat interface for answering job application questions:

```typescript
// API endpoint for application questions
export async function POST(request: Request) {
    try {
        // Verify authentication
        const { userId } = await auth();

        // Parse request body
        const { question, applicationId } = await request.json();

        // Get application data from database
        const application = applicationId
            ? await db.query.jobApplications.findFirst({
                  where: eq(jobApplications.id, parseInt(applicationId)),
              })
            : null;

        // Generate prompt for the AI
        const prompt = `
    You are an AI assistant helping a job applicant prepare answers for interview or application questions.

    CONTEXT ABOUT THE JOB:
    ${context}

    USER QUESTION: ${question}

    Please provide a well-structured, professional response that:
    1. Is personalized based on the job and company
    2. Highlights relevant qualifications and experience
    3. Shows enthusiasm and cultural fit
    4. Is concise yet comprehensive (max 3-4 paragraphs)
    5. Uses professional, confident language
    6. Is formatted for the application
    7. Answer in 1-2 sentences and make it sound like a real person
    8. Make sure to use the right tone for the question
    Your response should be ready to use or easily adaptable for the application.
    `;

        // Generate content using the Gemini singleton
        const answer = await gemini.generateContent(prompt);

        // Store chat history in the database
        // ...

        return NextResponse.json({ answer }, { status: 200 });
    } catch (error) {
        console.error("Error generating answer:", error);
        return NextResponse.json(
            { error: "Failed to generate answer" },
            { status: 500 }
        );
    }
}
```

## Error Handling

The integration includes robust error handling:

1. **API Key Validation**: Checks if the Gemini API key is configured
2. **Model Fallback**: Automatically falls back to `gemini-pro` if `gemini-2.0-flash` fails
3. **Content Truncation**: Limits content to 60,000 characters to prevent token limit issues
4. **Response Validation**: Handles various response formats (markdown, plain JSON)
5. **Exception Handling**: Catches and logs any errors during the AI processing
6. **Fallback Mechanism**: Falls back to basic parsing if AI parsing fails

## Configuration

To configure the Gemini AI integration, add the following to your environment variables:

```
GEMINI_API_KEY=your_api_key_here
```

You can obtain an API key from the [Google AI Studio](https://ai.google.dev/).

## Benefits of Singleton Implementation

The singleton pattern for Gemini AI integration provides several advantages:

1. **Efficient Resource Usage**: Prevents creating multiple instances of the AI client
2. **Consistent Configuration**: Ensures all API routes use the same model configuration
3. **Centralized Error Handling**: Provides a single place to manage and log errors
4. **Model Caching**: Reuses model instances to improve performance
5. **Automatic Fallback**: Gracefully handles model failures by falling back to stable models
6. **Simplified API**: Provides a clean, simple interface for generating content

## Usage Examples

### Using the Gemini Singleton

```typescript
import { gemini } from "@/lib/ai/gemini";

// Basic usage with default settings
const content = await gemini.generateContent(
    "Write a cover letter for a software engineer position"
);

// Advanced usage with custom settings
const contentWithOptions = await gemini.generateContent(
    "Write a professional email",
    {
        model: "gemini-pro",
        temperature: 0.5,
        maxOutputTokens: 4000,
    }
);
```

### Content Generation (Cover Letter)

```typescript
async function generateCoverLetter(
    applicationId: string,
    company: string,
    position: string,
    jobDescription: string
): Promise<string> {
    const prompt = `
        Write a professional cover letter for a ${position} position at ${company}.
        
        ${
            jobDescription
                ? `The job description is as follows: ${jobDescription}`
                : ""
        }
        
        Please write a cover letter that:
        1. Has a professional greeting and introduction
        2. Explains why I'm interested in this specific role
        3. Highlights my relevant skills and experience
        4. Shows enthusiasm for the company and its mission
        5. Includes a call to action and professional closing
    `;

    return await gemini.generateContent(prompt);
}
```

## Limitations

-   **Token Limits**: Gemini has token limits that restrict the size of input and output
-   **Rate Limits**: API calls are subject to rate limits based on your Google Cloud quota
-   **Parsing Accuracy**: While highly accurate, AI parsing may occasionally miss or misinterpret information
-   **Cost**: API usage incurs costs based on the number of tokens processed

## Future Improvements

-   **Fine-tuning**: Train a custom model specifically for resume parsing
-   **Streaming Responses**: Implement streaming for faster response times
-   **Multi-modal Parsing**: Use Gemini's multi-modal capabilities to parse resume images directly
-   **Contextual Understanding**: Improve parsing by providing more context about job requirements
-   **Feedback Loop**: Implement a feedback mechanism to improve parsing accuracy over time
-   **Response Caching**: Cache common responses to reduce API calls and improve performance
