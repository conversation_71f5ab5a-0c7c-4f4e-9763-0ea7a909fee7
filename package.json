{"name": "jobcraft", "version": "1.0.0", "description": "Craft your career with AI-powered job applications", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:pg", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio"}, "repository": {"type": "git", "url": "git+https://github.com/BeLazy167/JobCraft.git"}, "keywords": ["job", "application", "outreach", "resume", "ai"], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/BeLazy167/JobCraft/issues"}, "homepage": "https://github.com/BeLazy167/JobCraft#readme", "dependencies": {"@clerk/nextjs": "^6.12.2", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^4.1.3", "@mendable/firecrawl-js": "^1.21.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.31.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.13", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.67.3", "@tanstack/react-query-devtools": "^5.67.3", "@types/dompurify": "^3.0.5", "@types/lodash": "^4.17.16", "@types/node": "^22.13.9", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@uploadthing/react": "^7.3.0", "@xyflow/react": "^12.5.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "dotenv": "^16.4.7", "drizzle-orm": "^0.40.0", "framer-motion": "^12.5.0", "html-to-image": "^1.11.13", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "mammoth": "^1.9.0", "nanoid": "^5.1.5", "next": "^15.2.3", "next-themes": "^0.4.4", "openai": "^4.86.2", "pdf-parse": "^1.1.1", "pdfjs-dist": "^4.10.38", "pg": "^8.13.3", "postcss": "^8.5.3", "postgres": "^3.4.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-pdf": "^9.2.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.13", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.2", "uploadthing": "^7.5.2", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/pdf-parse": "^1.1.4", "@types/pg": "^8.11.11", "drizzle-kit": "^0.30.5", "tsx": "^4.19.3"}}