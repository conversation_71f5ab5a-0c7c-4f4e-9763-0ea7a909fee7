import * as XLSX from "xlsx";

// Define the type for job applications
export interface JobApplicationExport {
    id?: number;
    company?: string;
    position?: string;
    location?: string;
    salary?: string;
    applicationUrl?: string;
    contactName?: string;
    contactEmail?: string;
    contactPhone?: string;
    status?: string;
    notes?: string;
    appliedDate?: string | Date;
    createdAt?: string | Date;
    updatedAt?: string | Date;
}

/**
 * Converts job applications data to an XLSX file with improved formatting and triggers download
 * @param applications Array of job application objects
 * @param fileName Name of the file to be downloaded (without extension)
 */
export function exportApplicationsToXLSX(
    applications: JobApplicationExport[],
    fileName: string = "job-applications"
) {
    // Helper function to convert date strings or Date objects to Date, or null if invalid/undefined
    const toDate = (date: string | Date | undefined): Date | null => {
        if (!date) return null;
        const d = new Date(date);
        return isNaN(d.getTime()) ? null : d;
    };

    // Map applications to worksheet data with proper date handling
    const worksheetData = applications.map((app) => ({
        Company: app.company || "",
        Position: app.position || "",
        Status: app.status || "",
        Location: app.location || "",
        Salary: app.salary || "",
        "Applied Date": toDate(app.appliedDate),
        "Created Date": toDate(app.createdAt),
        "Updated Date": toDate(app.updatedAt),
        "Contact Name": app.contactName || "",
        "Contact Email": app.contactEmail || "",
        "Contact Phone": app.contactPhone || "",
        "Application URL": app.applicationUrl || "",

        Notes: app.notes || "",
    }));

    // Create a new workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);

    // Set column widths (in characters)
    worksheet["!cols"] = [
        { wch: 20 }, // Company
        { wch: 25 }, // Position
        { wch: 15 }, // Status
        { wch: 20 }, // Location
        { wch: 15 }, // Salary
        { wch: 12 }, // Applied Date
        { wch: 12 }, // Created Date
        { wch: 12 }, // Updated Date
        { wch: 20 }, // Contact Name
        { wch: 25 }, // Contact Email
        { wch: 15 }, // Contact Phone
        { wch: 30 }, // Application URL
        { wch: 50 }, // Notes
    ];

    // Style the header row (A1 to N1) with bold text
    const headerCells = [
        "A1",
        "B1",
        "C1",
        "D1",
        "E1",
        "F1",
        "G1",
        "H1",
        "I1",
        "J1",
        "K1",
        "L1",
        "N1",
    ];
    headerCells.forEach((cell) => {
        worksheet[cell].s = { font: { bold: true } };
    });

    // Add hyperlinks to Application URL (L) column
    const urlColumns = ["L"];
    const rowCount = applications.length + 1; // Header row + data rows
    for (let row = 2; row <= rowCount; row++) {
        urlColumns.forEach((col) => {
            const cellAddress = col + row;
            if (
                worksheet[cellAddress] &&
                typeof worksheet[cellAddress].v === "string" &&
                worksheet[cellAddress].v.trim() !== ""
            ) {
                worksheet[cellAddress].l = { Target: worksheet[cellAddress].v };
            }
        });
    }

    // Enable filters on the header row
    worksheet["!autofilter"] = { ref: "A1:N1" };

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Job Applications");

    // Generate the XLSX file
    const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
    });

    // Create a Blob and trigger download
    const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${fileName}.xlsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
