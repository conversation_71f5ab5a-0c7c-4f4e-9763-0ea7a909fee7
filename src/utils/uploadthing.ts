import {
    generateUploadButton,
    generateUploadDropzone,
    generateUploader,
    generateReactHelpers,
} from "@uploadthing/react";

import type { OurFileRouter } from "@/app/api/uploadthing/core";

export const UploadButton = generateUploadButton<OurFileRouter>();
export const UploadDropzone = generateUploadDropzone<OurFileRouter>();
export const useUploader = generateUploader<OurFileRouter>();
export const { useUploadThing, uploadFiles } =
    generateReactHelpers<OurFileRouter>();
// Server-only exports
// These should only be imported in server components or API routes
export const createServerSideHelpers = () => {
    // This is a dynamic import that only runs on the server
    if (typeof window !== "undefined") {
        throw new Error("Server-side helpers cannot be used on the client");
    }

    const { UTApi } = require("uploadthing/server");
    return {
        utapi: new UTApi(),
    };
};
