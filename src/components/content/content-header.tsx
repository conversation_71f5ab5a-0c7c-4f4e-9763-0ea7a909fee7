import React, { memo } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw, <PERSON>Lef<PERSON>, ExternalLink } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

interface ContentHeaderProps {
    application: any;
    isLoading?: boolean;
    onRefresh: () => void;
}

const ContentHeader = memo(function ContentHeader({
    application,
    isLoading = false,
    onRefresh,
}: ContentHeaderProps) {
    if (!application) return null;

    const statusColors = {
        not_applied: "secondary",
        applied: "default",
        interviewing: "outline",
        offered: "secondary",
        rejected: "destructive",
    } as const;

    const statusColor =
        statusColors[application.status as keyof typeof statusColors] ||
        "secondary";

    return (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 pb-4 border-b">
            <div className="flex-1 min-w-0">
                {/* Breadcrumb */}
                <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-2">
                    <Link
                        href="/dashboard"
                        className="hover:text-foreground transition-colors"
                    >
                        Dashboard
                    </Link>
                    <span>/</span>
                    <span className="text-foreground font-medium truncate">
                        {application.company} - {application.position}
                    </span>
                </nav>

                {/* Title and Status */}
                <div className="flex items-start gap-3">
                    <div className="flex-1 min-w-0">
                        <h1 className="text-lg sm:text-xl font-semibold text-foreground truncate">
                            {application.position}
                        </h1>
                        <div className="flex items-center gap-2 mt-1">
                            <p className="text-sm text-muted-foreground">
                                at {application.company}
                            </p>
                            {application.location && (
                                <>
                                    <span className="text-muted-foreground">
                                        •
                                    </span>
                                    <p className="text-sm text-muted-foreground">
                                        {application.location}
                                    </p>
                                </>
                            )}
                        </div>
                    </div>
                    <Badge
                        variant={statusColor}
                        className="text-xs whitespace-nowrap"
                    >
                        {application.status?.replace("_", " ") || "Not Applied"}
                    </Badge>
                </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={onRefresh}
                    disabled={isLoading}
                    className="h-8 px-3"
                >
                    <RefreshCw
                        className={`h-4 w-4 mr-1.5 ${
                            isLoading ? "animate-spin" : ""
                        }`}
                    />
                    <span className="hidden sm:inline">Refresh</span>
                </Button>

                <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="h-8 px-3"
                >
                    <Link href={`/dashboard/applications/${application.id}`}>
                        <ExternalLink className="h-4 w-4 mr-1.5" />
                        <span className="hidden sm:inline">View Details</span>
                    </Link>
                </Button>

                <Button variant="ghost" size="sm" asChild className="h-8 px-2">
                    <Link href="/dashboard">
                        <ArrowLeft className="h-4 w-4" />
                        <span className="sr-only">Back to Dashboard</span>
                    </Link>
                </Button>
            </div>
        </div>
    );
});

export default ContentHeader;
