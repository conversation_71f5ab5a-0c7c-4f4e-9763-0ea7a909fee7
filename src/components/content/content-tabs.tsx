import React, { memo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { ApplicationQuestionChat } from "@/components/application-question-chat";
import { ContentType } from "@/hooks/use-content-management";
import EnhancedContentDisplay from "./enhanced-content-display";

interface ContentTabsProps {
    activeTab: string;
    onTabChange: (value: string) => void;
    combinedData: any;
    isGeneratingContent: Record<ContentType, boolean>;
    onGenerateContent: (type: ContentType) => void;
    onContentUpdate: (field: ContentType, value: string) => Promise<void>;
    onCopy: (text: string, label: string) => void;
    onDownload: (content: string, filename: string) => void;
    resume?: any;
}

const ContentTabs = memo(function ContentTabs({
    activeTab,
    onTabChange,
    combinedData,
    isGeneratingContent,
    onGenerateContent,
    onContentUpdate,
    onCopy,
    onDownload,
    resume,
}: ContentTabsProps) {
    if (!combinedData) return null;

    const contentTypes: {
        key: ContentType;
        placeholder: string;
        maxLength: number;
    }[] = [
        {
            key: "coverLetter",
            placeholder: "Your cover letter content will appear here...",
            maxLength: 10000,
        },
        {
            key: "linkedinMessage",
            placeholder: "Your LinkedIn message content will appear here...",
            maxLength: 5000,
        },
        {
            key: "coldEmail",
            placeholder: "Your cold email content will appear here...",
            maxLength: 5000,
        },
    ];

    return (
        <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
            {contentTypes.map((contentType) => (
                <TabsContent
                    key={contentType.key}
                    value={contentType.key}
                    className="mt-0"
                >
                    <EnhancedContentDisplay
                        content={combinedData[contentType.key] || ""}
                        type={contentType.key}
                        isGenerating={isGeneratingContent[contentType.key]}
                        onSave={async (value) => {
                            await onContentUpdate(contentType.key, value);
                        }}
                        onGenerate={() => onGenerateContent(contentType.key)}
                        onCopy={() =>
                            onCopy(
                                combinedData[contentType.key] || "",
                                contentType.key
                            )
                        }
                        onDownload={() =>
                            onDownload(
                                combinedData[contentType.key] || "",
                                `${contentType.key}-${combinedData.company}.txt`
                            )
                        }
                        placeholder={contentType.placeholder}
                        maxLength={contentType.maxLength}
                    />
                </TabsContent>
            ))}

            <TabsContent value="appQuestions" className="mt-0">
                <div className="rounded-lg border border-border bg-card overflow-hidden">
                    <div className="p-4 bg-primary/5 border-b border-border">
                        <div className="flex items-center">
                            <div className="h-5 w-5 bg-primary rounded mr-3" />
                            <div>
                                <h3 className="font-semibold text-card-foreground">
                                    Application Question Assistant
                                </h3>
                                <p className="text-sm text-muted-foreground mt-1">
                                    Get help answering common application
                                    questions for {combinedData.company}.
                                </p>
                            </div>
                        </div>
                    </div>
                    <ApplicationQuestionChat
                        application={combinedData}
                        resume={resume}
                    />
                </div>
            </TabsContent>
        </Tabs>
    );
});

export default ContentTabs;
