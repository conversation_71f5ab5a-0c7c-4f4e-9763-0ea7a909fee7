"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";

interface ContentErrorFallbackProps {
    error?: Error | null;
    resetError?: () => void;
    applicationId?: string;
}

export function ContentErrorFallback({
    error,
    resetError,
    applicationId,
}: ContentErrorFallbackProps) {
    const errorMessage = error?.message || "An unexpected error occurred";
    const isNetworkError =
        errorMessage.includes("fetch") || errorMessage.includes("network");
    const isNotFound =
        errorMessage.includes("404") || errorMessage.includes("not found");

    return (
        <div className="flex-1 flex items-center justify-center p-4 sm:p-6">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                        <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                    </div>
                    <CardTitle className="text-lg">
                        {isNotFound
                            ? "Application Not Found"
                            : "Something went wrong"}
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Alert variant="destructive">
                        <AlertDescription className="text-sm">
                            {isNotFound
                                ? "The application you're looking for doesn't exist or has been deleted."
                                : isNetworkError
                                ? "Unable to connect to the server. Please check your internet connection."
                                : errorMessage}
                        </AlertDescription>
                    </Alert>

                    <div className="flex flex-col gap-2">
                        {resetError && !isNotFound && (
                            <Button
                                onClick={resetError}
                                variant="default"
                                size="sm"
                                className="w-full"
                            >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Try Again
                            </Button>
                        )}

                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            asChild
                        >
                            <Link href="/dashboard">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Dashboard
                            </Link>
                        </Button>

                        {applicationId && !isNotFound && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="w-full"
                                onClick={() => window.location.reload()}
                            >
                                Refresh Page
                            </Button>
                        )}
                    </div>

                    {process.env.NODE_ENV === "development" && error && (
                        <details className="mt-4 p-3 bg-muted rounded text-xs">
                            <summary className="cursor-pointer font-medium">
                                Error Details (Dev Mode)
                            </summary>
                            <pre className="mt-2 whitespace-pre-wrap break-words">
                                {error.stack}
                            </pre>
                        </details>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}

export class ContentErrorBoundary extends React.Component<
    React.PropsWithChildren<{ applicationId?: string }>,
    { hasError: boolean; error: Error | null }
> {
    constructor(props: React.PropsWithChildren<{ applicationId?: string }>) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error("Content page error:", error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <ContentErrorFallback
                    error={this.state.error}
                    resetError={() =>
                        this.setState({ hasError: false, error: null })
                    }
                    applicationId={this.props.applicationId}
                />
            );
        }

        return this.props.children;
    }
}
