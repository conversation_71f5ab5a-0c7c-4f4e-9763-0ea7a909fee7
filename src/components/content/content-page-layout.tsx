"use client";

import React, { memo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
    ArrowLeft,
    Sparkles,
    Download,
    Copy,
    RefreshCw,
    Building2,
    MapPin,
    Calendar,
    FileText,
    MessageSquare,
    Mail,
    HelpCircle,
    ChevronRight,
    Zap,
    CheckCircle2,
} from "lucide-react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

interface ContentPageLayoutProps {
    application: any;
    generatedContent: any;
    activeTab: string;
    onTabChange: (tab: string) => void;
    onGenerateContent: (type: string) => void;
    onContentUpdate: (field: string, value: string) => Promise<void>;
    onFieldUpdate: (field: string, value: string) => Promise<void>;
    isGeneratingContent: Record<string, boolean>;
    onCopy: (text: string, label: string) => void;
    onDownload: (content: string, filename: string) => void;
    onRefresh: () => void;
    isLoading?: boolean;
    children: React.ReactNode;
}

const ContentPageLayout = memo(function ContentPageLayout({
    application,
    generatedContent,
    activeTab,
    onTabChange,
    onGenerateContent,
    isGeneratingContent,
    onCopy,
    onDownload,
    onRefresh,
    isLoading = false,
    children,
}: ContentPageLayoutProps) {
    const [hoveredAction, setHoveredAction] = useState<string | null>(null);

    if (!application) return null;

    const statusConfig = {
        not_applied: {
            color: "bg-gray-100 text-gray-800",
            label: "Not Applied",
        },
        applied: { color: "bg-blue-100 text-blue-800", label: "Applied" },
        interviewing: {
            color: "bg-amber-100 text-amber-800",
            label: "Interviewing",
        },
        offered: { color: "bg-green-100 text-green-800", label: "Offered" },
        rejected: { color: "bg-red-100 text-red-800", label: "Rejected" },
    };

    const currentStatus =
        statusConfig[application.status as keyof typeof statusConfig] ||
        statusConfig.not_applied;

    const tabConfig = [
        {
            id: "coverLetter",
            label: "Cover Letter",
            icon: FileText,
            description: "Professional introduction",
            gradient: "from-blue-500 to-cyan-500",
        },
        {
            id: "linkedinMessage",
            label: "LinkedIn",
            icon: MessageSquare,
            description: "Network outreach",
            gradient: "from-indigo-500 to-purple-500",
        },
        {
            id: "coldEmail",
            label: "Cold Email",
            icon: Mail,
            description: "Direct contact",
            gradient: "from-green-500 to-emerald-500",
        },
        {
            id: "appQuestions",
            label: "Questions",
            icon: HelpCircle,
            description: "Interview prep",
            gradient: "from-orange-500 to-red-500",
        },
    ];

    const activeContent = generatedContent?.[activeTab];
    const hasContent = activeContent && activeContent.length > 0;

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
            {/* Header Section with Glass Morphism */}
            <div className="sticky top-0 z-10 backdrop-blur-xl bg-white/80 border-b border-gray-200/50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        {/* Navigation */}
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="sm"
                                asChild
                                className="hover:bg-gray-100/80"
                            >
                                <Link href="/dashboard">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Dashboard
                                </Link>
                            </Button>
                            <div className="flex items-center text-sm text-gray-500">
                                <ChevronRight className="h-4 w-4 mx-1" />
                                <span className="font-medium text-gray-900">
                                    {application.company}
                                </span>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-3">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={onRefresh}
                                disabled={isLoading}
                                className="hover:bg-gray-50"
                            >
                                <RefreshCw
                                    className={`h-4 w-4 mr-2 ${
                                        isLoading ? "animate-spin" : ""
                                    }`}
                                />
                                Refresh
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Hero Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-8"
                >
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        {/* Title & Meta */}
                        <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                                <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
                                    <Sparkles className="h-5 w-5 text-white" />
                                </div>
                                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                                    AI-Generated Content
                                </h1>
                            </div>
                            <p className="text-gray-600 text-lg">
                                Personalized application materials for your{" "}
                                {application.position} role
                            </p>
                        </div>

                        {/* Quick Stats */}
                        <div className="flex items-center gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {Object.keys(generatedContent || {}).length}
                                </div>
                                <div className="text-sm text-gray-500">
                                    Generated
                                </div>
                            </div>
                            <Separator
                                orientation="vertical"
                                className="h-12"
                            />
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {formatDistanceToNow(
                                        new Date(application.createdAt),
                                        { addSuffix: false }
                                    )}
                                </div>
                                <div className="text-sm text-gray-500">Ago</div>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Application Overview Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="mb-8"
                >
                    <Card className="border-0 shadow-lg bg-gradient-to-r from-white to-gray-50/50">
                        <CardContent className="p-6">
                            <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                                {/* Company Info */}
                                <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Building2 className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <div>
                                            <h2 className="text-xl font-semibold text-gray-900">
                                                {application.company}
                                            </h2>
                                            <p className="text-gray-600">
                                                {application.position}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-gray-500">
                                        {application.location && (
                                            <div className="flex items-center gap-1">
                                                <MapPin className="h-4 w-4" />
                                                {application.location}
                                            </div>
                                        )}
                                        <div className="flex items-center gap-1">
                                            <Calendar className="h-4 w-4" />
                                            {formatDistanceToNow(
                                                new Date(application.createdAt),
                                                { addSuffix: true }
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Status & Actions */}
                                <div className="flex items-center gap-4">
                                    <Badge
                                        className={`${currentStatus.color} border-0`}
                                    >
                                        {currentStatus.label}
                                    </Badge>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                {/* Content Tabs with Enhanced Design */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                >
                    <Tabs
                        value={activeTab}
                        onValueChange={onTabChange}
                        className="space-y-6"
                    >
                        {/* Enhanced Tab List */}
                        <div className="relative">
                            <TabsList className="grid w-full grid-cols-4 h-auto p-1 bg-gray-100 rounded-xl">
                                {tabConfig.map((tab) => {
                                    const Icon = tab.icon;
                                    const isActive = activeTab === tab.id;
                                    const hasTabContent =
                                        generatedContent?.[tab.id];

                                    return (
                                        <TabsTrigger
                                            key={tab.id}
                                            value={tab.id}
                                            className={`relative flex flex-col items-center gap-2 p-4 rounded-lg transition-all duration-200 ${
                                                isActive
                                                    ? "bg-white shadow-md"
                                                    : "hover:bg-white/50"
                                            }`}
                                        >
                                            <div
                                                className={`p-2 rounded-lg transition-all duration-200 ${
                                                    isActive
                                                        ? `bg-gradient-to-r ${tab.gradient}`
                                                        : "bg-gray-200"
                                                }`}
                                            >
                                                <Icon
                                                    className={`h-4 w-4 ${
                                                        isActive
                                                            ? "text-white"
                                                            : "text-gray-600"
                                                    }`}
                                                />
                                            </div>
                                            <div className="text-center">
                                                <div
                                                    className={`text-sm font-medium ${
                                                        isActive
                                                            ? "text-gray-900"
                                                            : "text-gray-600"
                                                    }`}
                                                >
                                                    {tab.label}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {tab.description}
                                                </div>
                                            </div>
                                            {hasTabContent && (
                                                <div className="absolute -top-1 -right-1">
                                                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                                                </div>
                                            )}
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>
                        </div>

                        {/* Content Area with Actions */}
                        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                            {/* Main Content */}
                            <div className="lg:col-span-3">
                                <Card className="border-0 shadow-lg min-h-[600px]">
                                    {/* Content Header */}
                                    <div className="p-6 border-b border-gray-100">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div
                                                    className={`p-2 rounded-lg bg-gradient-to-r ${
                                                        tabConfig.find(
                                                            (t) =>
                                                                t.id ===
                                                                activeTab
                                                        )?.gradient ||
                                                        "from-gray-400 to-gray-500"
                                                    }`}
                                                >
                                                    {React.createElement(
                                                        tabConfig.find(
                                                            (t) =>
                                                                t.id ===
                                                                activeTab
                                                        )?.icon || FileText,
                                                        {
                                                            className:
                                                                "h-5 w-5 text-white",
                                                        }
                                                    )}
                                                </div>
                                                <div>
                                                    <h3 className="text-lg font-semibold text-gray-900">
                                                        {
                                                            tabConfig.find(
                                                                (t) =>
                                                                    t.id ===
                                                                    activeTab
                                                            )?.label
                                                        }
                                                    </h3>
                                                    <p className="text-sm text-gray-500">
                                                        {
                                                            tabConfig.find(
                                                                (t) =>
                                                                    t.id ===
                                                                    activeTab
                                                            )?.description
                                                        }
                                                    </p>
                                                </div>
                                            </div>

                                            {/* Action Buttons */}
                                            {hasContent &&
                                                activeTab !==
                                                    "appQuestions" && (
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                onCopy(
                                                                    activeContent,
                                                                    activeTab
                                                                )
                                                            }
                                                            onMouseEnter={() =>
                                                                setHoveredAction(
                                                                    "copy"
                                                                )
                                                            }
                                                            onMouseLeave={() =>
                                                                setHoveredAction(
                                                                    null
                                                                )
                                                            }
                                                            className="hover:bg-blue-50 hover:border-blue-200"
                                                        >
                                                            <Copy className="h-4 w-4 mr-2" />
                                                            Copy
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                onDownload(
                                                                    activeContent,
                                                                    `${activeTab}-${application.company}.txt`
                                                                )
                                                            }
                                                            onMouseEnter={() =>
                                                                setHoveredAction(
                                                                    "download"
                                                                )
                                                            }
                                                            onMouseLeave={() =>
                                                                setHoveredAction(
                                                                    null
                                                                )
                                                            }
                                                            className="hover:bg-green-50 hover:border-green-200"
                                                        >
                                                            <Download className="h-4 w-4 mr-2" />
                                                            Download
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                onGenerateContent(
                                                                    activeTab
                                                                )
                                                            }
                                                            disabled={
                                                                isGeneratingContent[
                                                                    activeTab
                                                                ]
                                                            }
                                                            onMouseEnter={() =>
                                                                setHoveredAction(
                                                                    "regenerate"
                                                                )
                                                            }
                                                            onMouseLeave={() =>
                                                                setHoveredAction(
                                                                    null
                                                                )
                                                            }
                                                            className="hover:bg-purple-50 hover:border-purple-200"
                                                        >
                                                            <RefreshCw
                                                                className={`h-4 w-4 mr-2 ${
                                                                    isGeneratingContent[
                                                                        activeTab
                                                                    ]
                                                                        ? "animate-spin"
                                                                        : ""
                                                                }`}
                                                            />
                                                            Regenerate
                                                        </Button>
                                                    </div>
                                                )}
                                        </div>
                                    </div>

                                    {/* Content Body */}
                                    <CardContent className="p-6">
                                        <AnimatePresence mode="wait">
                                            <motion.div
                                                key={activeTab}
                                                initial={{ opacity: 0, x: 20 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                exit={{ opacity: 0, x: -20 }}
                                                transition={{ duration: 0.2 }}
                                                className="min-h-[400px]"
                                            >
                                                {children}
                                            </motion.div>
                                        </AnimatePresence>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Enhanced Sidebar */}
                            <div className="lg:col-span-1">
                                <div className="space-y-6">
                                    {/* Generation Status */}
                                    <Card className="border-0 shadow-md">
                                        <CardContent className="p-4">
                                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                                <Zap className="h-4 w-4 text-yellow-500" />
                                                Generation Status
                                            </h4>
                                            <div className="space-y-2">
                                                {tabConfig
                                                    .slice(0, 3)
                                                    .map((tab) => {
                                                        const hasContent =
                                                            generatedContent?.[
                                                                tab.id
                                                            ];
                                                        const isGenerating =
                                                            isGeneratingContent[
                                                                tab.id
                                                            ];

                                                        return (
                                                            <div
                                                                key={tab.id}
                                                                className="flex items-center justify-between"
                                                            >
                                                                <span className="text-sm text-gray-600">
                                                                    {tab.label}
                                                                </span>
                                                                {isGenerating ? (
                                                                    <div className="flex items-center gap-1">
                                                                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                                                                        <span className="text-xs text-blue-600">
                                                                            Generating...
                                                                        </span>
                                                                    </div>
                                                                ) : hasContent ? (
                                                                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                                                                ) : (
                                                                    <div className="w-4 h-4 border border-gray-300 rounded-full" />
                                                                )}
                                                            </div>
                                                        );
                                                    })}
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Quick Actions */}
                                    <Card className="border-0 shadow-md">
                                        <CardContent className="p-4">
                                            <h4 className="font-semibold text-gray-900 mb-3">
                                                Quick Actions
                                            </h4>
                                            <div className="space-y-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() =>
                                                        onGenerateContent(
                                                            "coverLetter"
                                                        )
                                                    }
                                                    disabled={
                                                        isGeneratingContent.coverLetter
                                                    }
                                                    className="w-full justify-start"
                                                >
                                                    <FileText className="h-4 w-4 mr-2" />
                                                    Generate Cover Letter
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() =>
                                                        onGenerateContent(
                                                            "linkedinMessage"
                                                        )
                                                    }
                                                    disabled={
                                                        isGeneratingContent.linkedinMessage
                                                    }
                                                    className="w-full justify-start"
                                                >
                                                    <MessageSquare className="h-4 w-4 mr-2" />
                                                    Generate LinkedIn Message
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() =>
                                                        onGenerateContent(
                                                            "coldEmail"
                                                        )
                                                    }
                                                    disabled={
                                                        isGeneratingContent.coldEmail
                                                    }
                                                    className="w-full justify-start"
                                                >
                                                    <Mail className="h-4 w-4 mr-2" />
                                                    Generate Cold Email
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                    </Tabs>
                </motion.div>
            </div>
        </div>
    );
});

export default ContentPageLayout;
