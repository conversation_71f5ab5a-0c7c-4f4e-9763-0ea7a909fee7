"use client";

import React, { memo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { EditableContentSimple } from "@/components/ui/editable-content-simple";
import { LoadingAnimation } from "@/components/ui/loading-animation";
import {
    Sparkles,
    Wand2,
    AlertCircle,
    Lightbulb,
    Copy,
    Check,
    Download,
    RefreshCw,
    MessageSquare,
    Edit3,
} from "lucide-react";

interface EnhancedContentDisplayProps {
    content: string;
    type: "coverLetter" | "linkedinMessage" | "coldEmail";
    isGenerating: boolean;
    onSave: (value: string) => Promise<void>;
    onGenerate: () => void;
    onCopy: () => void;
    onDownload: () => void;
    placeholder: string;
    maxLength: number;
}

const contentTypeConfig = {
    coverLetter: {
        title: "Cover Letter",
        subtitle: "Professional introduction to hiring managers",
        icon: Spark<PERSON>,
        color: "blue",
        tips: [
            "Replace [Hiring Manager Name] with actual names when possible",
            "Customize the opening paragraph for each application",
            "Highlight 2-3 key achievements that match the job requirements",
        ],
        gradient: "from-blue-500 to-cyan-500",
    },
    linkedinMessage: {
        title: "LinkedIn Message",
        subtitle: "Personalized outreach for networking",
        icon: MessageSquare,
        color: "purple",
        tips: [
            "Keep messages under 300 characters for better response rates",
            "Reference specific details from their profile",
            "Include a clear call-to-action",
        ],
        gradient: "from-purple-500 to-pink-500",
    },
    coldEmail: {
        title: "Cold Email",
        subtitle: "Direct outreach to decision makers",
        icon: Wand2,
        color: "green",
        tips: [
            "Use a compelling subject line",
            "Keep the email concise and scannable",
            "Include social proof or mutual connections",
        ],
        gradient: "from-green-500 to-emerald-500",
    },
};

const EnhancedContentDisplay = memo(function EnhancedContentDisplay({
    content,
    type,
    isGenerating,
    onSave,
    onGenerate,
    onCopy,
    onDownload,
    placeholder,
    maxLength,
}: EnhancedContentDisplayProps) {
    const [showTips, setShowTips] = useState(false);
    const [copied, setCopied] = useState(false);
    const [actionHover, setActionHover] = useState<string | null>(null);

    const config = contentTypeConfig[type];
    const Icon = config.icon;

    const handleCopy = async () => {
        await onCopy();
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const wordCount = content
        ? content.split(/\s+/).filter((word) => word.length > 0).length
        : 0;
    const charCount = content?.length || 0;

    if (isGenerating) {
        return (
            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex flex-col items-center justify-center py-16 px-8"
            >
                <div className="p-4 rounded-full bg-primary mb-6">
                    <Icon className="h-8 w-8 text-primary-foreground animate-pulse" />
                </div>

                <div className="mt-6 text-center">
                    <div className="text-sm text-muted-foreground">
                        AI is crafting personalized content based on your
                        profile and job requirements
                    </div>
                </div>
            </motion.div>
        );
    }

    if (!content) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-16 px-8"
            >
                <div className="inline-flex p-6 rounded-full bg-primary mb-6">
                    <Icon className="h-12 w-12 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-2">
                    Ready to generate your {config.title.toLowerCase()}?
                </h3>
                <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                    {config.subtitle}. Our AI will create personalized content
                    based on your profile and the job requirements.
                </p>
                <Button onClick={onGenerate} size="lg" className="shadow-lg">
                    <Wand2 className="h-5 w-5 mr-2" />
                    Generate {config.title}
                </Button>
            </motion.div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
        >
            {/* Tips Card */}
            <Card className="border-l-4 border-l-primary bg-primary/5">
                <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                        <Lightbulb className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                            <h4 className="font-medium text-foreground mb-2">
                                Optimization Tips
                            </h4>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setShowTips(!showTips)}
                                className="text-primary hover:text-primary/80 p-0 h-auto font-normal"
                            >
                                {showTips
                                    ? "Hide tips"
                                    : "Show tips to improve this content"}
                            </Button>
                            <AnimatePresence>
                                {showTips && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: "auto" }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="mt-3"
                                    >
                                        <ul className="space-y-1 text-sm text-muted-foreground">
                                            {config.tips.map((tip, index) => (
                                                <li
                                                    key={index}
                                                    className="flex items-start gap-2"
                                                >
                                                    <span className="text-primary mt-1">
                                                        •
                                                    </span>
                                                    {tip}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Content Statistics */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center gap-4">
                    <span>{wordCount} words</span>
                    <span>•</span>
                    <span>{charCount} characters</span>
                    <span>•</span>
                    <Badge variant="outline" className="text-xs">
                        <Edit3 className="h-3 w-3 mr-1" />
                        Editable
                    </Badge>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCopy}
                        onMouseEnter={() => setActionHover("copy")}
                        onMouseLeave={() => setActionHover(null)}
                        className="h-8 px-3"
                    >
                        {copied ? (
                            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                        ) : (
                            <Copy className="h-4 w-4" />
                        )}
                        {actionHover === "copy" && !copied && (
                            <span className="ml-2">Copy</span>
                        )}
                        {copied && (
                            <span className="ml-2 text-green-600 dark:text-green-400">
                                Copied!
                            </span>
                        )}
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onDownload}
                        onMouseEnter={() => setActionHover("download")}
                        onMouseLeave={() => setActionHover(null)}
                        className="h-8 px-3"
                    >
                        <Download className="h-4 w-4" />
                        {actionHover === "download" && (
                            <span className="ml-2">Download</span>
                        )}
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onGenerate}
                        onMouseEnter={() => setActionHover("regenerate")}
                        onMouseLeave={() => setActionHover(null)}
                        className="h-8 px-3"
                    >
                        <RefreshCw className="h-4 w-4" />
                        {actionHover === "regenerate" && (
                            <span className="ml-2">Regenerate</span>
                        )}
                    </Button>
                </div>
            </div>

            {/* Editable Content */}
            <Card className="border-2 border-dashed border-border hover:border-primary/50 transition-colors">
                <CardContent className="p-0">
                    <EditableContentSimple
                        content={content}
                        onSave={onSave}
                        placeholder={placeholder}
                        maxLength={maxLength}
                        className="border-0 min-h-[400px] text-base leading-relaxed"
                    />
                </CardContent>
            </Card>

            {/* Content Analysis */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
                    <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                            {Math.round((charCount / maxLength) * 100)}%
                        </div>
                        <div className="text-sm text-green-700 dark:text-green-300">
                            Length Optimal
                        </div>
                    </CardContent>
                </Card>
                <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
                    <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                            {wordCount}
                        </div>
                        <div className="text-sm text-blue-700 dark:text-blue-300">
                            Total Words
                        </div>
                    </CardContent>
                </Card>
                <Card className="border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-900/20">
                    <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                            A+
                        </div>
                        <div className="text-sm text-purple-700 dark:text-purple-300">
                            AI Quality
                        </div>
                    </CardContent>
                </Card>
            </div>
        </motion.div>
    );
});

export default EnhancedContentDisplay;
