import React, { memo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { EditableField } from "@/components/ui/editable-field";
import { ResumeSelector } from "@/components/resume-selector";
import { Badge } from "@/components/ui/badge";
import { Calendar, MapPin, Building, FileText } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface ApplicationDetailsSidebarProps {
    application: any;
    onFieldUpdate: (field: string, value: string) => Promise<void>;
}

const ApplicationDetailsSidebar = memo(function ApplicationDetailsSidebar({
    application,
    onFieldUpdate,
}: ApplicationDetailsSidebarProps) {
    if (!application) return null;

    const createdDate = new Date(application.createdAt);
    const timeAgo = formatDistanceToNow(createdDate, { addSuffix: true });

    return (
        <div className="space-y-4">
            {/* Application Info Card */}
            <Card className="border-border bg-card">
                <CardHeader className="px-3 py-3 sm:p-6 pb-2 sm:pb-4">
                    <CardTitle className="text-sm sm:text-base font-medium flex items-center gap-2 text-card-foreground">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        Application Details
                    </CardTitle>
                </CardHeader>
                <CardContent className="px-3 pb-3 sm:px-6 sm:pb-6">
                    <div className="space-y-3 sm:space-y-4 text-sm">
                        <EditableField
                            label="Company"
                            value={application.company || ""}
                            onSave={(value) => onFieldUpdate("company", value)}
                            placeholder="Enter company name..."
                            maxLength={255}
                            data-testid="company-field"
                        />

                        <EditableField
                            label="Position"
                            value={application.position || ""}
                            onSave={(value) => onFieldUpdate("position", value)}
                            placeholder="Enter position title..."
                            maxLength={255}
                            data-testid="position-field"
                        />

                        <EditableField
                            label="Location"
                            value={application.location || ""}
                            onSave={(value) => onFieldUpdate("location", value)}
                            placeholder="Enter location..."
                            maxLength={255}
                            data-testid="location-field"
                        />

                        <EditableField
                            label="Notes"
                            value={application.notes || ""}
                            onSave={(value) => onFieldUpdate("notes", value)}
                            placeholder="Add your notes about this application..."
                            multiline
                            rows={3}
                            maxLength={2000}
                            data-testid="notes-field"
                        />
                    </div>
                </CardContent>
            </Card>

            {/* Application Metadata */}
            <Card className="border-border bg-card">
                <CardHeader className="px-3 py-3 sm:p-6 pb-2 sm:pb-4">
                    <CardTitle className="text-sm sm:text-base font-medium flex items-center gap-2 text-card-foreground">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        Application Info
                    </CardTitle>
                </CardHeader>
                <CardContent className="px-3 pb-3 sm:px-6 sm:pb-6 space-y-3">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-card-foreground">
                            Status:
                        </span>
                        <Badge variant="secondary" className="text-xs">
                            {application.status?.replace("_", " ") ||
                                "Not Applied"}
                        </Badge>
                    </div>

                    {/* Created Date */}
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-card-foreground">
                            Created:
                        </span>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span title={createdDate.toLocaleDateString()}>
                                {timeAgo}
                            </span>
                        </div>
                    </div>

                    {/* Location Badge */}
                    {application.location && (
                        <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-card-foreground">
                                Location:
                            </span>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <MapPin className="h-3 w-3" />
                                <span>{application.location}</span>
                            </div>
                        </div>
                    )}

                    {/* Job Description Preview */}
                    {application.jobDescription && (
                        <div className="pt-2 border-t border-border">
                            <span className="text-sm font-medium text-card-foreground">
                                Job Description:
                            </span>
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-3">
                                {application.jobDescription.substring(0, 150)}
                                {application.jobDescription.length > 150 &&
                                    "..."}
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Resume Selector */}
            <ResumeSelector
                applicationId={application.id}
                resumeId={application.resumeId}
            />
        </div>
    );
});

export default ApplicationDetailsSidebar;
