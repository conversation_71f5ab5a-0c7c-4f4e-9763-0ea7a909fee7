"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import PreloaderParticles from "./preloader-particles";

interface PreloaderProps {
    isLoading: boolean;
    onAnimationComplete?: () => void;
}

export function Preloader({ isLoading, onAnimationComplete }: PreloaderProps) {
    const [progress, setProgress] = useState(0);
    const [phase, setPhase] = useState<"initial" | "progress" | "complete">(
        "initial"
    );

    useEffect(() => {
        let progressInterval: NodeJS.Timeout;

        if (isLoading) {
            setPhase("initial");
            // Start at 35% immediately for better feedback
            setProgress(35);

            // Almost immediately transition to progress phase
            const timer = setTimeout(() => {
                setPhase("progress");

                let currentProgress = 35;
                progressInterval = setInterval(() => {
                    if (currentProgress < 100) {
                        // Faster progress - increase by 5% each time
                        currentProgress += 5;
                        if (currentProgress > 100) currentProgress = 100;
                        setProgress(currentProgress);

                        if (currentProgress >= 100) {
                            clearInterval(progressInterval);
                            setPhase("complete");
                        }
                    }
                }, 100); // Faster interval
            }, 50); // Very short delay

            return () => {
                clearTimeout(timer);
                clearInterval(progressInterval);
            };
        } else {
            // When no longer loading, ensure we complete to 100%
            if (progress < 100) {
                setProgress(100);
                setPhase("complete");
            }
        }
    }, [isLoading, progress]);

    return (
        <AnimatePresence mode="wait">
            {isLoading && (
                <motion.div
                    className="fixed inset-0 z-[999] bg-black flex flex-col items-center justify-center overflow-hidden"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{
                        opacity: 0,
                        transition: { duration: 0.3 }, // Faster transition
                    }}
                    onAnimationComplete={() => {
                        if (phase === "complete" && !isLoading) {
                            onAnimationComplete?.();
                        }
                    }}
                >
                    {/* Interactive Particles Background */}
                    <PreloaderParticles isActive={isLoading} />

                    {/* Brand logo */}
                    <motion.div
                        className="absolute top-8 left-8 text-3xl font-bold tracking-tighter"
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, ease: "easeOut" }} // Faster animation
                    >
                        <span className="text-gradient-purple-blue">Hire</span>
                        <span className="text-gradient-pink-cyan">Rizz</span>
                    </motion.div>

                    {/* Central loading animation */}
                    <div className="relative w-64 h-64 flex items-center justify-center">
                        {/* Rotating outer ring */}
                        <motion.div
                            className="absolute w-full h-full border-[1px] border-white/10 rounded-full"
                            initial={{ rotate: 0 }}
                            animate={{ rotate: 360 }}
                            transition={{
                                duration: 8,
                                ease: "linear",
                                repeat: Infinity,
                            }}
                        />

                        {/* Pulsing inner ring with progress indicator */}
                        <motion.div
                            className="absolute w-full h-full rounded-full flex items-center justify-center"
                            style={{
                                background: `conic-gradient(from 0deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) ${progress}%, transparent ${progress}%, transparent 100%)`,
                            }}
                            initial={{ opacity: 0.4, scale: 0.9 }}
                            animate={{
                                opacity: [0.4, 0.8, 0.4],
                                scale: [0.9, 0.92, 0.9],
                            }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                            }}
                        >
                            <div className="w-[98%] h-[98%] rounded-full bg-black" />
                        </motion.div>

                        {/* Progress glow effect */}
                        <motion.div
                            className="absolute w-full h-full rounded-full"
                            style={{
                                boxShadow: `0 0 30px ${
                                    progress / 3
                                }px hsla(var(--primary), ${progress / 200})`,
                                background: "transparent",
                            }}
                        />

                        {/* Percentage text */}
                        <motion.div
                            className="relative text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary z-10"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.2 }} // Faster appearance
                        >
                            {Math.floor(progress)}%
                        </motion.div>
                    </div>

                    {/* Loading message */}
                    <motion.div
                        className="mt-16 overflow-hidden"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3, duration: 0.4 }} // Faster animation
                    >
                        <div className="flex flex-col items-center text-center">
                            <motion.p
                                className="text-xl font-light text-white/80 uppercase tracking-widest"
                                animate={{ opacity: [0.5, 1, 0.5] }}
                                transition={{
                                    duration: 1.5, // Faster animation
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                }}
                            >
                                Loading Experience
                            </motion.p>
                            <div className="mt-3 flex space-x-2">
                                {[0, 1, 2].map((i) => (
                                    <motion.div
                                        key={i}
                                        className="w-2 h-2 bg-secondary rounded-full"
                                        animate={{
                                            y: [0, -8, 0],
                                            opacity: [0.5, 1, 0.5],
                                        }}
                                        transition={{
                                            duration: 0.6, // Faster animation
                                            repeat: Infinity,
                                            delay: i * 0.15,
                                            ease: "easeInOut",
                                        }}
                                    />
                                ))}
                            </div>
                        </div>
                    </motion.div>

                    {/* Dynamic background lines */}
                    <div
                        className="absolute inset-0 flex justify-between z-[-1]"
                        style={{ perspective: "1000px" }}
                    >
                        {Array.from({ length: 10 }).map((_, i) => (
                            <motion.div
                                key={i}
                                className="h-full w-[1px] bg-gradient-to-b from-transparent via-primary/20 to-transparent"
                                initial={{
                                    opacity: 0,
                                    rotateX: 90,
                                    translateZ: -100,
                                }}
                                animate={{
                                    opacity: [0, 0.4, 0],
                                    rotateX: [90, 0, 90],
                                    translateZ: [-100, 0, -100],
                                }}
                                transition={{
                                    duration: 2 + i / 3, // Faster animation
                                    repeat: Infinity,
                                    delay: i * 0.2,
                                    ease: "easeInOut",
                                }}
                            />
                        ))}
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}

export default Preloader;
