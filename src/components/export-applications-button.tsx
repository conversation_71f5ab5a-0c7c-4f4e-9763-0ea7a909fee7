"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download, Loader2 } from "lucide-react";
import { exportApplicationsToXLSX, JobApplicationExport } from "@/utils/export";
import { toast } from "sonner";

export function ExportApplicationsButton() {
    const [isExporting, setIsExporting] = useState(false);

    const handleExport = async () => {
        setIsExporting(true);
        try {
            // Fetch applications from the API
            const response = await fetch("/api/applications/export");

            if (!response.ok) {
                throw new Error("Failed to fetch applications");
            }

            const data = await response.json();

            // Export to XLSX
            exportApplicationsToXLSX(
                data.applications as JobApplicationExport[],
                `job-applications-${new Date().toISOString().split("T")[0]}`
            );

            toast.success("Applications exported successfully");
        } catch (error) {
            console.error("Error exporting applications:", error);
            toast.error("Failed to export applications");
        } finally {
            setIsExporting(false);
        }
    };

    return (
        <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={isExporting}
            className="flex items-center gap-1"
        >
            {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
                <Download className="h-4 w-4" />
            )}
            <span>Export XLSX</span>
        </Button>
    );
}
