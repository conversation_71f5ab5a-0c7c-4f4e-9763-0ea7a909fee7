"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { FileText, Home } from "lucide-react";
import { Button } from "@/components/ui/button";

export function Navigation() {
    const pathname = usePathname();
    return (
        <nav className="grid items-start gap-2">
            <Link href="/dashboard">
                <Button
                    variant={pathname === "/dashboard" ? "default" : "ghost"}
                    className={`w-full justify-start ${
                        pathname === "/dashboard"
                            ? ""
                            : "hover:bg-primary/10 hover:text-primary"
                    }`}
                >
                    <Home className="mr-2 h-4 w-4" />
                    Dashboard
                </Button>
            </Link>
            <Link href="/dashboard/applications">
                <Button
                    variant={
                        pathname === "/dashboard/applications"
                            ? "default"
                            : "ghost"
                    }
                    className={`w-full justify-start ${
                        pathname === "/dashboard/applications"
                            ? ""
                            : "hover:bg-primary/10 hover:text-primary"
                    }`}
                >
                    <FileText className="mr-2 h-4 w-4" />
                    Applications
                </Button>
            </Link>
            <Link href="/dashboard/resumes">
                <Button
                    variant={
                        pathname === "/dashboard/resumes" ? "default" : "ghost"
                    }
                    className={`w-full justify-start ${
                        pathname === "/dashboard/resumes"
                            ? ""
                            : "hover:bg-primary/10 hover:text-primary"
                    }`}
                >
                    <FileText className="mr-2 h-4 w-4" />
                    Resumes
                </Button>
            </Link>
        </nav>
    );
}
