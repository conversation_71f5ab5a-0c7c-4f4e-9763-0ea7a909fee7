"use client";

import * as React from "react";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";
import dynamic from "next/dynamic";

// Dynamically import the client component with no SSR
const LoadingAnimation = dynamic(
    () => import("@/components/ui/loading-animation")
);

export function DashboardLoadingState() {
    return (
        <div className="space-y-6">
            <LoadingSkeleton
                variant="dashboard"
                size="lg"
                text="Loading Dashboard"
                subText="Preparing your data..."
            />
            <div className="flex items-center justify-center py-12">
                <LoadingSkeleton
                    size="lg"
                    text="Loading Dashboard"
                    subText="Preparing your data..."
                />
            </div>
        </div>
    );
}
