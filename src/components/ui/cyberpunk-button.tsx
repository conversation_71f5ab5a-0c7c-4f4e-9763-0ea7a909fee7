"use client";

import * as React from "react";
import { VariantProps, cva } from "class-variance-authority";
import { cn } from "@/lib/utils";

const cyberpunkButtonVariants = cva(
    "inline-flex items-center justify-center rounded-md font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none active:scale-95",
    {
        variants: {
            variant: {
                default:
                    "bg-[#5e4b8b] text-white hover:bg-[#5e4b8b]/80 border-[0.5px] border-[#5e4b8b]/80 shadow-[0_0_10px_-3px_#5e4b8b]",
                outline:
                    "border border-[#5e4b8b] bg-transparent text-foreground hover:bg-[#5e4b8b]/10 hover:text-[#5e4b8b]",
                secondary:
                    "bg-[#6d9dc5] text-white hover:bg-[#6d9dc5]/80 border-[0.5px] border-[#6d9dc5]/80 shadow-[0_0_10px_-3px_#6d9dc5]",
                accent: "bg-[#ff6e61] text-white hover:bg-[#ff6e61]/80 border-[0.5px] border-[#ff6e61]/80 shadow-[0_0_10px_-3px_#ff6e61]",
                ghost: "hover:bg-[#5e4b8b]/10 hover:text-[#5e4b8b]",
                destructive:
                    "bg-[#da1b61] text-white hover:bg-[#da1b61]/90 shadow-[0_0_10px_-3px_#da1b61]",
            },
            size: {
                default: "h-10 py-2 px-4",
                sm: "h-9 px-3 rounded-md text-sm",
                lg: "h-11 px-8 rounded-md",
                icon: "h-10 w-10",
            },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    }
);

export interface CyberpunkButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof cyberpunkButtonVariants> {}

export const CyberpunkButton = React.forwardRef<
    HTMLButtonElement,
    CyberpunkButtonProps
>(({ className, variant, size, ...props }, ref) => {
    return (
        <button
            className={cn(
                cyberpunkButtonVariants({ variant, size, className })
            )}
            ref={ref}
            {...props}
        />
    );
});

CyberpunkButton.displayName = "CyberpunkButton";
