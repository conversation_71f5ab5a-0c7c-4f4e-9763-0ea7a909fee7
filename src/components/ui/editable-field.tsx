"use client";

import { useState, useEffect, useCallback, useRef, memo } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Check, Loader2, Edit3 } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

interface EditableFieldProps {
    value: string;
    onSave: (value: string) => Promise<void>;
    placeholder?: string;
    multiline?: boolean;
    className?: string;
    label?: string;
    rows?: number;
    debounceMs?: number;
    minLength?: number;
    maxLength?: number;
    type?: "text" | "email" | "url";
}

export const EditableField = memo(function EditableField({
    value: initialValue,
    onSave,
    placeholder = "Click to edit...",
    multiline = false,
    className,
    label,
    rows = 3,
    debounceMs = 1000,
    minLength,
    maxLength,
    type = "text",
}: EditableFieldProps) {
    const [value, setValue] = useState(initialValue);
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [lastSaved, setLastSaved] = useState<Date | null>(null);
    const [saveError, setSaveError] = useState<string | null>(null);

    const inputRef = useRef<HTMLTextAreaElement | HTMLInputElement>(null);
    const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const lastSaveRef = useRef<string>(initialValue);

    // Update local value when prop changes
    useEffect(() => {
        setValue(initialValue);
        lastSaveRef.current = initialValue;
    }, [initialValue]);

    // Auto-save with debouncing
    const debouncedSave = useCallback(
        async (newValue: string) => {
            // Don't save if value hasn't actually changed
            if (newValue === lastSaveRef.current) return;

            // Clear any existing timeout
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }

            // Set new timeout
            saveTimeoutRef.current = setTimeout(async () => {
                try {
                    setIsSaving(true);
                    setSaveError(null);
                    await onSave(newValue);
                    lastSaveRef.current = newValue;
                    setLastSaved(new Date());
                } catch (error) {
                    setSaveError(
                        error instanceof Error
                            ? error.message
                            : "Failed to save"
                    );
                } finally {
                    setIsSaving(false);
                }
            }, debounceMs);
        },
        [onSave, debounceMs]
    );

    // Handle value changes
    const handleChange = useCallback(
        (newValue: string) => {
            setValue(newValue);
            debouncedSave(newValue);
        },
        [debouncedSave]
    );

    // Handle focus
    const handleFocus = useCallback(() => {
        setIsEditing(true);
    }, []);

    // Handle blur
    const handleBlur = useCallback(() => {
        setIsEditing(false);
        // Force immediate save on blur if there are pending changes
        if (saveTimeoutRef.current && value !== lastSaveRef.current) {
            clearTimeout(saveTimeoutRef.current);
            onSave(value)
                .then(() => {
                    lastSaveRef.current = value;
                    setLastSaved(new Date());
                })
                .catch((error) => {
                    setSaveError(
                        error instanceof Error
                            ? error.message
                            : "Failed to save"
                    );
                });
        }
    }, [value, onSave]);

    // Handle enter key for single-line fields
    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent) => {
            if (!multiline && e.key === "Enter") {
                e.preventDefault();
                inputRef.current?.blur();
            }
            if (e.key === "Escape") {
                setValue(lastSaveRef.current);
                inputRef.current?.blur();
            }
        },
        [multiline]
    );

    // Auto-focus when editing starts
    useEffect(() => {
        if (isEditing && inputRef.current) {
            inputRef.current.focus();
            // Select all text for easy editing
            if (inputRef.current instanceof HTMLInputElement) {
                inputRef.current.select();
            } else if (inputRef.current instanceof HTMLTextAreaElement) {
                inputRef.current.setSelectionRange(
                    0,
                    inputRef.current.value.length
                );
            }
        }
    }, [isEditing]);

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
        };
    }, []);

    const InputComponent = multiline ? Textarea : Input;
    const isEmpty = !value || value.trim().length === 0;

    return (
        <div className={cn("space-y-2", className)}>
            {label && (
                <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">{label}</label>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <AnimatePresence mode="wait">
                            {isSaving && (
                                <motion.div
                                    key="saving"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.8 }}
                                    className="flex items-center gap-1"
                                >
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                    <span>Saving...</span>
                                </motion.div>
                            )}
                            {!isSaving && lastSaved && (
                                <motion.div
                                    key="saved"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.8 }}
                                    className="flex items-center gap-1 text-green-600"
                                >
                                    <Check className="h-3 w-3" />
                                    <span>
                                        Saved {lastSaved.toLocaleTimeString()}
                                    </span>
                                </motion.div>
                            )}
                            {!isSaving && !isEditing && !lastSaved && (
                                <motion.div
                                    key="edit-hint"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    className="flex items-center gap-1"
                                >
                                    <Edit3 className="h-3 w-3" />
                                    <span>Click to edit</span>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </div>
            )}

            <div className="relative">
                <InputComponent
                    ref={inputRef as any}
                    value={value}
                    onChange={(e) => handleChange(e.target.value)}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    onKeyDown={handleKeyDown}
                    placeholder={placeholder}
                    className={cn(
                        "transition-all duration-200",
                        isEditing && "ring-2 ring-primary/20 border-primary",
                        isEmpty && !isEditing && "text-muted-foreground italic",
                        saveError && "border-destructive"
                    )}
                    rows={multiline ? rows : undefined}
                    type={!multiline ? type : undefined}
                    minLength={minLength}
                    maxLength={maxLength}
                />

                {/* Save indicator overlay */}
                <AnimatePresence>
                    {isSaving && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="absolute inset-0 bg-background/80 flex items-center justify-center pointer-events-none"
                        >
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                Saving...
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>

            {/* Error message */}
            <AnimatePresence>
                {saveError && (
                    <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        className="text-sm text-destructive"
                    >
                        {saveError}
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Character count for text areas */}
            {multiline && maxLength && (
                <div className="text-xs text-muted-foreground text-right">
                    {value.length} / {maxLength}
                </div>
            )}
        </div>
    );
});
