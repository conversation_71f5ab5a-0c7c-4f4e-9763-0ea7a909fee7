"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export const TextGenerateEffect = ({
    words,
    className,
}: {
    words: string;
    className?: string;
}) => {
    const [renderedText, setRenderedText] = useState("");
    const [isGenerating, setIsGenerating] = useState(true);
    const [completedTyping, setCompletedTyping] = useState(false);

    useEffect(() => {
        if (!words || words.trim() === "") {
            setIsGenerating(false);
            return;
        }

        setIsGenerating(true);
        setCompletedTyping(false);
        setRenderedText("");

        let interval: ReturnType<typeof setInterval>;

        const timeout = setTimeout(() => {
            let i = 0;
            interval = setInterval(() => {
                if (i < words.length) {
                    setRenderedText(words.substring(0, i + 1));
                    i++;
                } else {
                    clearInterval(interval);
                    setIsGenerating(false);
                    setCompletedTyping(true);
                }
            }, 15); // Adjust speed here
        }, 100);

        return () => {
            clearTimeout(timeout);
            clearInterval(interval);
        };
    }, [words]);

    return (
        <div className={cn("text-lg", className)}>
            <div className="relative">
                {!completedTyping && isGenerating && (
                    <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{
                            duration: 0.5,
                            repeat: Infinity,
                            repeatType: "reverse",
                        }}
                        className="inline-block w-[2px] h-5 bg-primary ml-1 align-middle"
                    />
                )}
                <div className="whitespace-pre-wrap">{renderedText}</div>
            </div>
        </div>
    );
};
