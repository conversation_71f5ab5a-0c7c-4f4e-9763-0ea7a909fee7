"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface LoadingProps {
    variant?: "spinner" | "pulse" | "ripple" | "dots";
    size?: "sm" | "md" | "lg";
    text?: string;
    subText?: string;
    className?: string;
}

export const Loading = memo(function Loading({
    variant = "spinner",
    size = "md",
    text,
    subText,
    className,
}: LoadingProps) {
    const sizeClasses = {
        sm: "w-4 h-4",
        md: "w-8 h-8",
        lg: "w-12 h-12",
    };

    const textSizeClasses = {
        sm: "text-sm",
        md: "text-base",
        lg: "text-lg",
    };

    const renderSpinner = () => (
        <div
            className={cn(
                "animate-spin rounded-full border-2 border-primary border-t-transparent",
                sizeClasses[size]
            )}
        />
    );

    const renderPulse = () => (
        <div
            className={cn(
                "animate-pulse rounded-full bg-primary",
                sizeClasses[size]
            )}
        />
    );

    const renderRipple = () => (
        <div className="relative">
            <div
                className={cn(
                    "animate-ping rounded-full bg-primary opacity-75",
                    sizeClasses[size]
                )}
            />
            <div
                className={cn(
                    "absolute inset-0 rounded-full bg-primary",
                    sizeClasses[size]
                )}
            />
        </div>
    );

    const renderDots = () => (
        <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
                <motion.div
                    key={i}
                    className="w-2 h-2 bg-primary rounded-full"
                    animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                    transition={{
                        duration: 0.6,
                        repeat: Infinity,
                        delay: i * 0.2,
                    }}
                />
            ))}
        </div>
    );

    const renderVariant = () => {
        switch (variant) {
            case "pulse":
                return renderPulse();
            case "ripple":
                return renderRipple();
            case "dots":
                return renderDots();
            default:
                return renderSpinner();
        }
    };

    return (
        <div
            className={cn(
                "flex flex-col items-center justify-center gap-3",
                className
            )}
        >
            {renderVariant()}
            {text && (
                <p
                    className={cn(
                        "text-muted-foreground",
                        textSizeClasses[size]
                    )}
                >
                    {text}
                </p>
            )}
            {subText && (
                <p className="text-xs text-muted-foreground/70">{subText}</p>
            )}
        </div>
    );
});

// Full-screen loading overlay
export const LoadingOverlay = memo(function LoadingOverlay({
    text = "Loading...",
    subText,
}: {
    text?: string;
    subText?: string;
}) {
    return (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center">
            <Loading variant="ripple" size="lg" text={text} subText={subText} />
        </div>
    );
});

// Page loading component
export const PageLoading = memo(function PageLoading({
    text = "Loading page...",
}: {
    text?: string;
}) {
    return (
        <div className="flex items-center justify-center min-h-[60vh]">
            <Loading variant="spinner" size="lg" text={text} />
        </div>
    );
});

export default Loading;
