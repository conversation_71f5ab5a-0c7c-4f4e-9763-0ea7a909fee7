"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface CyberpunkCardProps {
    children: React.ReactNode;
    className?: string;
    delay?: number;
    showAnimatedBorder?: boolean;
    gradient?: "pink-cyan" | "cyan-purple" | "pink-purple";
}

const gradientMap = {
    "pink-cyan": "from-[hsl(var(--pink))] to-[hsl(var(--cyan))]",
    "cyan-purple": "from-[hsl(var(--cyan))] to-[hsl(var(--purple))]",
    "pink-purple": "from-[hsl(var(--pink))] to-[hsl(var(--purple))]",
};

export function CyberpunkCard({
    children,
    className,
    delay = 0,
    showAnimatedBorder = true,
    gradient = "pink-cyan",
}: CyberpunkCardProps) {
    return (
        <motion.div
            className={cn(
                "group relative rounded-xl border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-6 hover:border-[hsl(var(--primary))/0.3] transition-all hover:shadow-md hover:-translate-y-1",
                className
            )}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay }}
            viewport={{ once: true }}
        >
            {children}

            {showAnimatedBorder && (
                <div
                    className={cn(
                        "absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r rounded-b-xl scale-x-0 group-hover:scale-x-100 transition-transform duration-300",
                        gradientMap[gradient]
                    )}
                ></div>
            )}
        </motion.div>
    );
}
