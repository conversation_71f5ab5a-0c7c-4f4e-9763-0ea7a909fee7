"use client";

import React, { ErrorInfo, ReactNode, memo } from "react";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Home } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

export class ErrorBoundary extends React.Component<
    ErrorBoundaryProps,
    ErrorBoundaryState
> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error("Error caught by boundary:", error, errorInfo);

        // Call custom error handler if provided
        this.props.onError?.(error, errorInfo);

        this.setState({ error, errorInfo });
    }

    handleRetry = () => {
        this.setState({
            hasError: false,
            error: undefined,
            errorInfo: undefined,
        });
    };

    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                return this.props.fallback;
            }

            return (
                <ErrorFallback
                    error={this.state.error}
                    onRetry={this.handleRetry}
                />
            );
        }

        return this.props.children;
    }
}

// Fallback UI component
const ErrorFallback = memo(function ErrorFallback({
    error,
    onRetry,
}: {
    error?: Error;
    onRetry: () => void;
}) {
    const isDevelopment = process.env.NODE_ENV === "development";

    return (
        <div className="min-h-screen flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                        <AlertTriangle className="h-6 w-6 text-destructive" />
                    </div>
                    <CardTitle className="text-xl text-destructive">
                        Something went wrong
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground text-center">
                        We're sorry, but something unexpected happened. Please
                        try again.
                    </p>

                    {isDevelopment && error && (
                        <details className="mt-4 p-3 bg-muted rounded-md">
                            <summary className="text-sm font-medium cursor-pointer">
                                Error Details (Development)
                            </summary>
                            <div className="mt-2 text-xs text-muted-foreground">
                                <p className="font-mono">{error.message}</p>
                                {error.stack && (
                                    <pre className="mt-2 overflow-auto max-h-32 text-xs">
                                        {error.stack}
                                    </pre>
                                )}
                            </div>
                        </details>
                    )}

                    <div className="flex gap-2 pt-2">
                        <Button onClick={onRetry} className="flex-1">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Try Again
                        </Button>
                        <Button variant="outline" asChild className="flex-1">
                            <Link href="/dashboard">
                                <Home className="h-4 w-4 mr-2" />
                                Go Home
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
});

// Hook for error handling
export function useErrorHandler() {
    const handleError = React.useCallback((error: Error, context?: string) => {
        console.error(`Error${context ? ` in ${context}` : ""}:`, error);

        // You can add error reporting service here
        // Example: reportToSentry(error, context);
    }, []);

    return handleError;
}

// HOC for component error boundaries
export function withErrorBoundary<P extends object>(
    Component: React.ComponentType<P>,
    fallback?: ReactNode
) {
    const WrappedComponent = (props: P) => (
        <ErrorBoundary fallback={fallback}>
            <Component {...props} />
        </ErrorBoundary>
    );

    WrappedComponent.displayName = `withErrorBoundary(${
        Component.displayName || Component.name
    })`;

    return WrappedComponent;
}
