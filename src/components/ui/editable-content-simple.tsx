"use client";

import { useState, useEffect, useRef } from "react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Edit3 } from "lucide-react";

interface EditableContentSimpleProps {
    content: string;
    onSave: (content: string) => Promise<void>;
    placeholder?: string;
    className?: string;
    maxLength?: number;
}

export function EditableContentSimple({
    content: initialContent,
    onSave,
    placeholder = "Click to edit...",
    className,
    maxLength = 50000,
}: EditableContentSimpleProps) {
    const [localContent, setLocalContent] = useState(initialContent || "");
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const displayRef = useRef<HTMLDivElement>(null);
    const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const [hasSaved, setHasSaved] = useState(false);

    // Update local content when prop changes
    useEffect(() => {
        setLocalContent(initialContent || "");
    }, [initialContent]);

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        console.log("EditableContent: Click detected, entering edit mode");
        setIsEditing(true);
    };

    const handleChange = (value: string) => {
        console.log("EditableContent: Content changed, scheduling save");
        setLocalContent(value);

        // Clear existing timeout
        if (saveTimeoutRef.current) {
            clearTimeout(saveTimeoutRef.current);
        }

        // Set new timeout for auto-save
        saveTimeoutRef.current = setTimeout(async () => {
            setIsSaving(true);
            setHasSaved(false);
            try {
                console.log("EditableContent: Saving content...");
                await onSave(value);
                console.log("EditableContent: Content saved successfully");
                setHasSaved(true);
                setTimeout(() => setHasSaved(false), 2000);
            } catch (error) {
                console.error("EditableContent: Save failed:", error);
            } finally {
                setIsSaving(false);
            }
        }, 1000);
    };

    const handleBlur = () => {
        console.log("EditableContent: Blur detected, exiting edit mode");
        setIsEditing(false);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        // Escape key to exit edit mode
        if (e.key === "Escape") {
            e.preventDefault();
            setIsEditing(false);
            return;
        }

        // Quick select placeholders with Ctrl/Cmd + Shift + P
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === "P") {
            e.preventDefault();
            selectNextPlaceholder();
        }
    };

    const selectNextPlaceholder = () => {
        if (!textareaRef.current) return;

        const text = localContent;
        const currentPos = textareaRef.current.selectionStart;

        // Find next [placeholder] after current position
        const placeholderRegex = /\[[^\]]+\]/g;
        let match;

        // Reset regex to start from beginning
        placeholderRegex.lastIndex = 0;

        while ((match = placeholderRegex.exec(text)) !== null) {
            if (match.index >= currentPos) {
                textareaRef.current.setSelectionRange(
                    match.index,
                    match.index + match[0].length
                );
                textareaRef.current.focus();
                return;
            }
        }

        // If no placeholder found after cursor, wrap to beginning
        placeholderRegex.lastIndex = 0;
        match = placeholderRegex.exec(text);
        if (match) {
            textareaRef.current.setSelectionRange(
                match.index,
                match.index + match[0].length
            );
            textareaRef.current.focus();
        }
    };

    // Focus textarea when editing starts
    useEffect(() => {
        if (isEditing && textareaRef.current) {
            console.log("EditableContent: Focusing textarea in edit mode");
            // Use setTimeout to ensure textarea is rendered
            setTimeout(() => {
                if (textareaRef.current) {
                    textareaRef.current.focus();
                    // Position cursor at end
                    const length = localContent.length;
                    textareaRef.current.setSelectionRange(length, length);
                    console.log(
                        "EditableContent: Textarea focused and cursor positioned"
                    );
                }
            }, 0);
        }
    }, [isEditing, localContent]);

    // Render content with highlighted placeholders
    const renderContentWithHighlights = (content: string) => {
        if (!content) return null;

        const parts = content.split(/(\[[^\]]+\])/g);
        return parts.map((part, index) => {
            if (part.match(/^\[[^\]]+\]$/)) {
                return (
                    <span
                        key={index}
                        className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-1 rounded border border-yellow-300 dark:border-yellow-700"
                        title="Click to edit this placeholder"
                    >
                        {part}
                    </span>
                );
            }
            return part;
        });
    };

    console.log("EditableContent: Rendering", {
        isEditing,
        hasContent: !!localContent,
    });

    return (
        <div className={cn("relative", className)}>
            {isEditing ? (
                <div className="relative">
                    <Textarea
                        ref={textareaRef}
                        value={localContent}
                        onChange={(e) => handleChange(e.target.value)}
                        onBlur={handleBlur}
                        onKeyDown={handleKeyDown}
                        placeholder={placeholder}
                        maxLength={maxLength}
                        className={cn(
                            "w-full resize-none text-base leading-relaxed p-3 rounded-md border border-input bg-background",
                            // Match the display height exactly
                            className?.includes("min-h-")
                                ? className
                                : "min-h-[400px]"
                        )}
                        style={{
                            lineHeight: "1.6",
                            fontFamily: "inherit",
                        }}
                    />
                    {(isSaving || hasSaved) && (
                        <div className="absolute top-2 right-2 text-xs z-10">
                            {isSaving ? (
                                <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-2 py-1 rounded">
                                    Saving...
                                </span>
                            ) : (
                                <span className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 px-2 py-1 rounded">
                                    Saved ✓
                                </span>
                            )}
                        </div>
                    )}
                    <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
                        Press Escape to exit, Ctrl+Shift+P for placeholders
                    </div>
                </div>
            ) : (
                <div
                    ref={displayRef}
                    onClick={handleClick}
                    className={cn(
                        "p-3 rounded-md border border-input bg-background cursor-text hover:border-primary/50 hover:bg-muted/5 relative group text-base leading-relaxed transition-colors",
                        // Use the same min-height as passed in className
                        className?.includes("min-h-")
                            ? className
                            : "min-h-[400px]"
                    )}
                    style={{
                        lineHeight: "1.6",
                    }}
                >
                    {localContent ? (
                        <div className="whitespace-pre-wrap">
                            {renderContentWithHighlights(localContent)}
                        </div>
                    ) : (
                        <div className="text-muted-foreground italic">
                            {placeholder}
                        </div>
                    )}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Edit3 className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
                        Click anywhere to edit
                    </div>
                </div>
            )}
        </div>
    );
}
