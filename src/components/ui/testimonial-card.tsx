"use client";

import { motion } from "framer-motion";
import React from "react";

// Testimonial card component
export interface TestimonialCardProps {
    quote: string;
    author: string;
    title: string;
    gradient?: string;
}

export function TestimonialCard({
    quote,
    author,
    title,
    gradient = "from-[hsl(var(--cyan))] to-[#00A3C4]",
}: TestimonialCardProps) {
    return (
        <motion.div
            className="rounded-xl border border-[#333] bg-[#202020] p-6 relative overflow-hidden hover:border-[hsl(var(--cyan))]/30 transition-all"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true, margin: "-100px" }}
        >
            <div
                className={`absolute -top-24 -right-24 w-40 h-40 rounded-full bg-gradient-to-br ${gradient} opacity-10`}
            />
            <div className="flex flex-col h-full relative">
                <div className="mb-4">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={`bg-gradient-to-br ${gradient} bg-clip-text text-transparent`}
                    >
                        <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                        <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                    </svg>
                </div>
                <p className="text-[#AAAAAA] flex-1 mb-4">{quote}</p>
                <div>
                    <p className="font-semibold text-white">{author}</p>
                    <p className="text-sm text-[#888888]">{title}</p>
                </div>
            </div>
        </motion.div>
    );
}
