"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface CyberpunkTextProps {
    children: React.ReactNode;
    className?: string;
    variant?: "default" | "gradient" | "glitch" | "animated";
    gradient?:
        | "primary"
        | "secondary"
        | "accent"
        | "coral-amber"
        | "pink-purple";
    as?: React.ElementType;
}

export function CyberpunkText({
    children,
    className,
    variant = "default",
    gradient = "primary",
    as: Component = "span",
}: CyberpunkTextProps) {
    const getGradientClass = () => {
        switch (gradient) {
            case "primary":
                return "text-gradient-pink-purple";
            case "secondary":
                return "text-gradient-cyan-purple";
            case "accent":
                return "text-gradient-pink-cyan";
            case "coral-amber":
                return "text-gradient-coral-amber";
            default:
                return "text-gradient-pink-purple";
        }
    };

    const getVariantClass = () => {
        switch (variant) {
            case "default":
                return "text-[#5e4b8b]";
            case "gradient":
                return getGradientClass();
            case "glitch":
                return "text-[#5e4b8b] animate-glitch";
            case "animated":
                return `${getGradientClass()} animate-pulse`;
            default:
                return "text-[#5e4b8b]";
        }
    };

    return (
        <Component
            className={cn(
                "inline-block font-semibold",
                getVariantClass(),
                className
            )}
        >
            {children}
        </Component>
    );
}
