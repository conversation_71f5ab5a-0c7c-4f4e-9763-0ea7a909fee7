import { Loader2, Briefcase, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

type LoadingVariant =
    | "default"
    | "application"
    | "resume"
    | "simple"
    | "dashboard"
    | "card"
    | "table"
    | "list";
type LoadingSize = "sm" | "md" | "lg";

interface LoadingSkeletonProps {
    variant?: LoadingVariant;
    size?: LoadingSize;
    text?: string;
    subText?: string;
    className?: string;
    rows?: number; // For table or list variants
}

export function LoadingSkeleton({
    variant = "default",
    size = "md",
    text,
    subText,
    className,
    rows = 3, // Reduced default rows for simplicity
}: LoadingSkeletonProps) {
    // Size mappings
    const sizeClasses = {
        sm: "w-8 h-8",
        md: "w-12 h-12",
        lg: "w-16 h-16",
    };

    const iconSize = {
        sm: "w-4 h-4",
        md: "w-6 h-6",
        lg: "w-10 h-10",
    };

    // Text based on size
    const textSize = {
        sm: "text-sm",
        md: "text-base",
        lg: "text-xl",
    };

    // Render the appropriate loading icon based on variant
    const renderLoadingIcon = () => {
        switch (variant) {
            case "application":
                return (
                    <div className="relative">
                        <div
                            className={cn(
                                "rounded-full bg-primary/15 flex items-center justify-center",
                                sizeClasses[size]
                            )}
                        >
                            <Briefcase
                                className={cn(
                                    "text-primary animate-pulse",
                                    iconSize[size]
                                )}
                            />
                        </div>
                        <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-secondary animate-pulse" />
                    </div>
                );

            case "resume":
                return (
                    <div className="relative">
                        <div
                            className={cn(
                                "aspect-[3/4] bg-primary/5 border border-primary/20 rounded-md relative overflow-hidden flex items-center justify-center",
                                {
                                    "w-16": size === "sm",
                                    "w-24": size === "md",
                                    "w-28": size === "lg",
                                }
                            )}
                        >
                            <FileText
                                className={cn(
                                    "text-primary/70 animate-pulse",
                                    iconSize[size]
                                )}
                            />

                            {/* Simple scanner line */}
                            <div className="absolute w-full h-[2px] bg-secondary/60 top-1/2 animate-pulse"></div>
                        </div>

                        {/* Corner decorations */}
                        <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-accent opacity-70"></div>
                        <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-accent opacity-70"></div>
                    </div>
                );

            case "simple":
                return (
                    <div className="relative">
                        <Loader2
                            className={cn(
                                "animate-spin text-primary",
                                iconSize[size]
                            )}
                        />
                    </div>
                );

            case "dashboard":
                return (
                    <div className="space-y-6 w-full">
                        <div className="flex items-center justify-between">
                            <div className="h-10 w-32 bg-muted rounded animate-pulse"></div>
                            <div className="h-10 w-40 bg-muted rounded animate-pulse"></div>
                        </div>
                        // Removed the grid of numerical placeholders for
                        simplicity
                    </div>
                );

            case "card":
                return (
                    <div className="space-y-3 w-full">
                        <div className="h-4 w-[250px] bg-muted rounded animate-pulse"></div>
                        // Reduced to one primary element
                    </div>
                );

            case "table":
                return (
                    <div className="space-y-3 w-full">
                        <div className="grid grid-cols-4 gap-4 p-4 border-b">
                            <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
                            <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
                            <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
                            <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
                        </div>
                        {Array.from({ length: rows }).map((_, i) => (
                            <div key={i} className="grid grid-cols-4 gap-4 p-4">
                                <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
                                // Reduced to one column for simplicity
                            </div>
                        ))}
                    </div>
                );

            case "list":
                return (
                    <div className="space-y-4 w-full">
                        {[1].map(
                            (
                                i // Reduced to one item for simplicity
                            ) => (
                                <div key={i} className="border rounded-lg p-4">
                                    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                                        <div className="flex-1 space-y-2">
                                            <div className="h-5 w-1/3 bg-muted rounded animate-pulse"></div>
                                            // Reduced to one primary element
                                        </div>
                                        <div className="h-8 w-24 bg-muted rounded animate-pulse"></div>
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                );

            default:
                return (
                    <div className="relative">
                        <div
                            className={cn(
                                "rounded-full border-[3px] border-primary/20 relative",
                                sizeClasses[size]
                            )}
                        >
                            <Loader2
                                className={cn(
                                    "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-spin text-primary",
                                    iconSize[size]
                                )}
                            />
                            {/* Pulse effect */}
                            <div
                                className={cn(
                                    "absolute inset-0 rounded-full animate-[ping_2s_ease-in-out_infinite] bg-primary/10"
                                )}
                            ></div>
                        </div>
                    </div>
                );
        }
    };

    return (
        <div
            className={cn(
                "flex flex-col items-center justify-center",
                className
            )}
        >
            {renderLoadingIcon()}

            {(text || subText) &&
                variant !== "dashboard" &&
                variant !== "card" &&
                variant !== "table" &&
                variant !== "list" && (
                    <div className="mt-4 space-y-1 text-center">
                        {text && (
                            <h3
                                className={cn(
                                    "font-semibold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent",
                                    textSize[size]
                                )}
                            >
                                {text}
                            </h3>
                        )}
                        {subText && (
                            <p
                                className={cn("text-muted-foreground", {
                                    "text-xs": size === "sm",
                                    "text-sm": size === "md",
                                    "text-base": size === "lg",
                                })}
                            >
                                {subText}
                            </p>
                        )}
                    </div>
                )}

            {variant === "default" && (
                <div className="flex space-x-1.5 items-center mt-4">
                    {[0, 1, 2].map((i) => (
                        <div
                            key={i}
                            className={cn(
                                "rounded-full bg-secondary animate-pulse",
                                {
                                    "w-1.5 h-1.5": size === "sm",
                                    "w-2 h-2": size === "md",
                                    "w-3 h-3": size === "lg",
                                }
                            )}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}
