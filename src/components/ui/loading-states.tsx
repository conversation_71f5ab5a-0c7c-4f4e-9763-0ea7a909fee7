"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

// Base skeleton component
export const Skeleton = memo(function Skeleton({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <div
            className={cn("animate-pulse rounded-md bg-muted", className)}
            {...props}
        />
    );
});

// Card skeleton
export const CardSkeleton = memo(function CardSkeleton() {
    return (
        <div className="space-y-3">
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-4 w-[150px]" />
        </div>
    );
});

// Application card skeleton
export const ApplicationCardSkeleton = memo(function ApplicationCardSkeleton() {
    return (
        <div className="border rounded-lg p-6 space-y-4">
            <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-[200px]" />
                <Skeleton className="h-5 w-[80px]" />
            </div>
            <div className="space-y-2">
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-4 w-[180px]" />
            </div>
            <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-[100px]" />
                <Skeleton className="h-8 w-[80px]" />
            </div>
        </div>
    );
});

// Resume card skeleton
export const ResumeCardSkeleton = memo(function ResumeCardSkeleton() {
    return (
        <div className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded" />
                <div className="space-y-2">
                    <Skeleton className="h-4 w-[120px]" />
                    <Skeleton className="h-3 w-[80px]" />
                </div>
            </div>
            <div className="flex items-center justify-between">
                <Skeleton className="h-3 w-[60px]" />
                <Skeleton className="h-6 w-[80px]" />
            </div>
        </div>
    );
});

// Table skeleton
export const TableSkeleton = memo(function TableSkeleton({
    rows = 5,
}: {
    rows?: number;
}) {
    return (
        <div className="space-y-3">
            <div className="grid grid-cols-4 gap-4 p-4 border-b">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
            </div>
            {Array.from({ length: rows }).map((_, i) => (
                <div key={i} className="grid grid-cols-4 gap-4 p-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-4 w-2/3" />
                </div>
            ))}
        </div>
    );
});

// Smart loading indicator with progress
interface SmartLoadingProps {
    message?: string;
    progress?: number;
    size?: "sm" | "md" | "lg";
}

export const SmartLoading = memo(function SmartLoading({
    message = "Loading...",
    progress,
    size = "md",
}: SmartLoadingProps) {
    const sizeClasses = {
        sm: "h-6 w-6",
        md: "h-8 w-8",
        lg: "h-12 w-12",
    };

    return (
        <div className="flex flex-col items-center gap-4">
            <div className="relative">
                <motion.div
                    className={cn(
                        "border-2 border-muted rounded-full",
                        sizeClasses[size]
                    )}
                    animate={{ rotate: 360 }}
                    transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear",
                    }}
                >
                    <motion.div
                        className="absolute inset-0 border-2 border-primary border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{
                            duration: 0.75,
                            repeat: Infinity,
                            ease: "linear",
                        }}
                    />
                </motion.div>
                {progress !== undefined && (
                    <motion.div
                        className="absolute inset-0 flex items-center justify-center"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                    >
                        <span className="text-xs font-medium">
                            {Math.round(progress)}%
                        </span>
                    </motion.div>
                )}
            </div>
            <motion.p
                className="text-sm text-muted-foreground"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
            >
                {message}
            </motion.p>
        </div>
    );
});

// Page loading overlay
export const PageLoadingOverlay = memo(function PageLoadingOverlay({
    message = "Loading page...",
}: {
    message?: string;
}) {
    return (
        <motion.div
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
        >
            <SmartLoading message={message} size="lg" />
        </motion.div>
    );
});

// Inline loading spinner
export const InlineLoading = memo(function InlineLoading({
    className,
}: {
    className?: string;
}) {
    return (
        <motion.div
            className={cn("inline-flex items-center gap-2", className)}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
        >
            <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-muted-foreground">Loading...</span>
        </motion.div>
    );
});
