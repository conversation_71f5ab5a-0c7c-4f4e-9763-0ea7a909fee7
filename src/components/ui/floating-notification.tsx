"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Check } from "lucide-react";

const notifications = [
    {
        name: "<PERSON>",
        action: "just landed an interview",
        company: "at Google",
        timeAgo: "2m ago",
    },
    {
        name: "<PERSON>",
        action: "got hired",
        company: "by Microsoft",
        timeAgo: "5m ago",
    },
    {
        name: "<PERSON>",
        action: "created a perfect resume",
        company: "for Amazon",
        timeAgo: "8m ago",
    },
];

export function FloatingNotification() {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        const interval = setInterval(() => {
            setIsVisible(false);
            setTimeout(() => {
                setCurrentIndex((prev) => (prev + 1) % notifications.length);
                setIsVisible(true);
            }, 500);
        }, 5000);

        return () => clearInterval(interval);
    }, []);

    const notification = notifications[currentIndex];

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.div
                    initial={{ opacity: 0, y: 50, x: "-50%" }}
                    animate={{ opacity: 1, y: 0, x: "-50%" }}
                    exit={{ opacity: 0, y: -50, x: "-50%" }}
                    className="fixed bottom-8 left-1/2 transform -translate-x-1/2 bg-card border border-border rounded-full py-2 px-4 shadow-lg z-50"
                >
                    <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <Check className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                            <p className="text-sm">
                                <span className="font-semibold text-foreground">
                                    {notification.name}
                                </span>{" "}
                                <span className="text-muted-foreground">
                                    {notification.action}
                                </span>{" "}
                                <span className="font-medium text-primary">
                                    {notification.company}
                                </span>
                            </p>
                            <p className="text-xs text-muted-foreground">
                                {notification.timeAgo}
                            </p>
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
