"use client";

import { memo } from "react";
import { cn } from "@/lib/utils";

interface CyberpunkBackgroundProps {
    className?: string;
    showGrid?: boolean;
    showGlow?: boolean;
    variant?: "default" | "subtle";
}

function CyberpunkBackground({
    className,
    showGrid = true,
    showGlow = true,
    variant = "default",
}: CyberpunkBackgroundProps) {
    return (
        <div
            className={cn(
                "absolute inset-0 w-full h-full overflow-hidden pointer-events-none",
                className
            )}
        >
            {/* Cyberpunk-inspired grid - only render when needed */}
            {showGrid && (
                <div
                    className="absolute inset-0 bg-grid opacity-20 will-change-transform"
                    style={{
                        transform: "translateZ(0)",
                        backfaceVisibility: "hidden",
                    }}
                />
            )}

            {/* Glowing accent elements - only render when needed */}
            {showGlow && (
                <>
                    <div
                        className={cn(
                            "absolute -top-20 -right-20 w-96 h-96 rounded-full will-change-transform",
                            variant === "default"
                                ? "bg-[hsl(267,75%,53%)]/30 blur-[120px]" /* Vibrant purple */
                                : "bg-[hsl(267,75%,53%)]/20 blur-[100px]"
                        )}
                        style={{ transform: "translateZ(0)" }}
                    />
                    <div
                        className={cn(
                            "absolute -bottom-40 -left-20 w-96 h-96 rounded-full will-change-transform",
                            variant === "default"
                                ? "bg-[hsl(190,95%,50%)]/30 blur-[120px]" /* Bright cyan */
                                : "bg-[hsl(190,95%,50%)]/20 blur-[100px]"
                        )}
                        style={{ transform: "translateZ(0)" }}
                    />
                    <div
                        className={cn(
                            "absolute top-1/4 left-1/3 w-72 h-72 rounded-full will-change-transform",
                            variant === "default"
                                ? "bg-[hsl(330,80%,60%)]/30 blur-[100px]" /* Bright magenta */
                                : "bg-[hsl(330,80%,60%)]/20 blur-[80px]"
                        )}
                        style={{ transform: "translateZ(0)" }}
                    />
                    <div
                        className={cn(
                            "absolute bottom-1/3 right-1/4 w-64 h-64 rounded-full will-change-transform",
                            variant === "default"
                                ? "bg-[hsl(217,91%,60%)]/25 blur-[90px]" /* Bright blue */
                                : "bg-[hsl(217,91%,60%)]/15 blur-[70px]"
                        )}
                        style={{ transform: "translateZ(0)" }}
                    />
                </>
            )}

            {/* Enhanced gradient overlay */}
            <div
                className="absolute inset-0 bg-gradient-to-b from-background via-transparent to-background"
                style={{ opacity: variant === "default" ? 0.7 : 0.4 }}
            />
        </div>
    );
}

// Memoizing the component to prevent unnecessary re-renders
export { CyberpunkBackground };
export default memo(CyberpunkBackground);
