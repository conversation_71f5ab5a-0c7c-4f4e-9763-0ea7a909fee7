"use client";

import React, { useEffect, useRef, useCallback } from "react";
import { motion, useMotionValue, useSpring } from "framer-motion";
import throttle from "lodash/throttle";

export function CyberpunkCursor() {
    // Use refs instead of state for better performance
    const isClicked = useRef(false);
    const isMounted = useRef(false);
    const isTouch = useRef(false);

    // Motion values for smoother animations without re-renders
    const cursorX = useMotionValue(0);
    const cursorY = useMotionValue(0);

    // Use springs for natural, smoother movement
    const springConfig = { damping: 25, stiffness: 300 };
    const cursorXSpring = useSpring(cursorX, springConfig);
    const cursorYSpring = useSpring(cursorY, springConfig);

    // Check if device is touch-enabled
    useEffect(() => {
        isTouch.current =
            "ontouchstart" in window || navigator.maxTouchPoints > 0;

        // Don't add listeners for touch devices
        if (isTouch.current) return;

        isMounted.current = true;

        return () => {
            isMounted.current = false;
        };
    }, []);

    // Throttled mouse move handler for better performance
    const handleMouseMove = useCallback(
        throttle((e: MouseEvent) => {
            if (!isMounted.current) return;
            cursorX.set(e.clientX);
            cursorY.set(e.clientY);
        }, 16), // ~60fps
        []
    );

    // Mouse event handlers
    const handleMouseDown = useCallback(() => {
        isClicked.current = true;
    }, []);

    const handleMouseUp = useCallback(() => {
        isClicked.current = false;
    }, []);

    // Add event listeners
    useEffect(() => {
        if (isTouch.current) return; // Skip for touch devices

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mousedown", handleMouseDown);
        document.addEventListener("mouseup", handleMouseUp);

        return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mousedown", handleMouseDown);
            document.removeEventListener("mouseup", handleMouseUp);
        };
    }, [handleMouseMove, handleMouseDown, handleMouseUp]);

    // Don't render the cursor for touch devices
    if (isTouch.current) return null;

    return (
        <div className="cursor-container fixed inset-0 z-50 pointer-events-none overflow-hidden will-change-transform">
            {/* Main cursor */}
            <motion.div
                className="absolute w-5 h-5 bg-[hsl(var(--cyan))]/70 rounded-full mix-blend-screen"
                style={{
                    x: cursorXSpring,
                    y: cursorYSpring,
                    boxShadow: "0 0 12px 4px rgba(84, 234, 255, 0.4)",
                    mixBlendMode: "screen",
                    willChange: "transform",
                }}
                animate={{
                    scale: isClicked.current ? 0.7 : 1,
                }}
                transition={{
                    scale: { type: "spring", stiffness: 500, damping: 30 },
                }}
            />

            {/* Outer glow */}
            <motion.div
                className="absolute w-10 h-10 rounded-full border border-[hsl(var(--cyan))]/50 mix-blend-screen"
                style={{
                    x: cursorXSpring,
                    y: cursorYSpring,
                    willChange: "transform",
                    translateX: "-50%",
                    translateY: "-50%",
                }}
                animate={{
                    scale: isClicked.current ? 1.5 : 1,
                    opacity: isClicked.current ? 0.8 : 0.5,
                }}
                transition={{
                    scale: { type: "spring", stiffness: 300, damping: 20 },
                    opacity: { duration: 0.2 },
                }}
            />
        </div>
    );
}
