"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, Briefcase, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

export type LoadingVariant = "pulse" | "orbit" | "bloom" | "ripple";
export type LoadingSize = "sm" | "md" | "lg";
export type LoadingColor = "primary" | "secondary" | "accent" | "neutral";

export interface LoadingAnimationProps {
    variant?: LoadingVariant;
    size?: LoadingSize;
    color?: LoadingColor;
    text?: string;
    subText?: string;
    className?: string;
}

export function LoadingAnimation({
    variant = "pulse",
    size = "md",
    color = "primary",
    className,
}: LoadingAnimationProps) {
    // Size mappings
    const sizeClasses = {
        sm: "w-8 h-8",
        md: "w-12 h-12",
        lg: "w-16 h-16",
    };

    // Color mappings - use these instead of hardcoded colors
    const colorClasses = {
        primary: "bg-primary",
        secondary: "bg-secondary",
        accent: "bg-accent",
        neutral: "bg-muted-foreground",
    };

    const colorClassesLight = {
        primary: "bg-primary/20",
        secondary: "bg-secondary/20",
        accent: "bg-accent/20",
        neutral: "bg-muted-foreground/20",
    };

    // Render the appropriate loading animation based on variant
    const renderLoadingAnimation = () => {
        switch (variant) {
            case "orbit":
                return (
                    <motion.div
                        className={cn("relative", sizeClasses[size])}
                        animate={{ rotate: 360 }}
                        transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "linear",
                        }}
                    >
                        {/* Center dot */}
                        <motion.div
                            className={cn(
                                "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full",
                                colorClasses[color]
                            )}
                            style={{
                                width:
                                    size === "sm" ? 4 : size === "md" ? 6 : 8,
                                height:
                                    size === "sm" ? 4 : size === "md" ? 6 : 8,
                            }}
                        />

                        {/* Orbiting elements */}
                        {[0, 1, 2].map((i) => (
                            <motion.div
                                key={i}
                                className="absolute"
                                style={{
                                    width: "100%",
                                    height: "100%",
                                    rotate: i * 120,
                                }}
                            >
                                <motion.div
                                    className={cn(
                                        "absolute rounded-full",
                                        colorClasses[color]
                                    )}
                                    style={{
                                        width:
                                            size === "sm"
                                                ? 6
                                                : size === "md"
                                                ? 8
                                                : 10,
                                        height:
                                            size === "sm"
                                                ? 6
                                                : size === "md"
                                                ? 8
                                                : 10,
                                        top:
                                            size === "sm"
                                                ? -3
                                                : size === "md"
                                                ? -4
                                                : -5,
                                        left: "50%",
                                        marginLeft:
                                            size === "sm"
                                                ? -3
                                                : size === "md"
                                                ? -4
                                                : -5,
                                    }}
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{
                                        duration: 1.5,
                                        repeat: Infinity,
                                        delay: i * 0.5,
                                        ease: "easeInOut",
                                    }}
                                />
                            </motion.div>
                        ))}
                    </motion.div>
                );

            case "bloom":
                return (
                    <div className={cn("relative", sizeClasses[size])}>
                        {/* Center circle */}
                        <motion.div
                            className={cn(
                                "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full",
                                colorClasses[color]
                            )}
                            style={{
                                width:
                                    size === "sm" ? 8 : size === "md" ? 12 : 16,
                                height:
                                    size === "sm" ? 8 : size === "md" ? 12 : 16,
                            }}
                            animate={{
                                scale: [0.8, 1, 0.8],
                            }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                            }}
                        />

                        {/* Petals */}
                        {[0, 1, 2, 3, 4, 5].map((i) => (
                            <motion.div
                                key={i}
                                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                                style={{
                                    rotate: i * 60,
                                }}
                            >
                                <motion.div
                                    className={cn(
                                        "absolute rounded-full",
                                        colorClassesLight[color]
                                    )}
                                    style={{
                                        width:
                                            size === "sm"
                                                ? 6
                                                : size === "md"
                                                ? 8
                                                : 10,
                                        height:
                                            size === "sm"
                                                ? 14
                                                : size === "md"
                                                ? 22
                                                : 28,
                                        top:
                                            size === "sm"
                                                ? -7
                                                : size === "md"
                                                ? -11
                                                : -14,
                                        left:
                                            size === "sm"
                                                ? -3
                                                : size === "md"
                                                ? -4
                                                : -5,
                                    }}
                                    animate={{
                                        scaleY: [0.6, 1, 0.6],
                                        opacity: [0.5, 1, 0.5],
                                    }}
                                    transition={{
                                        duration: 2.5,
                                        repeat: Infinity,
                                        delay: i * 0.2,
                                        ease: "easeInOut",
                                    }}
                                />
                            </motion.div>
                        ))}
                    </div>
                );

            case "ripple":
                return (
                    <div className={cn("relative", sizeClasses[size])}>
                        {/* Center dot */}
                        <motion.div
                            className={cn(
                                "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full",
                                colorClasses[color]
                            )}
                            style={{
                                width:
                                    size === "sm" ? 6 : size === "md" ? 8 : 10,
                                height:
                                    size === "sm" ? 6 : size === "md" ? 8 : 10,
                            }}
                            animate={{
                                scale: [0.8, 1.2, 0.8],
                            }}
                            transition={{
                                duration: 1.5,
                                repeat: Infinity,
                                ease: "easeInOut",
                            }}
                        />

                        {/* Ripple waves */}
                        {[0, 1, 2].map((i) => (
                            <motion.div
                                key={i}
                                className={cn(
                                    "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full border-2",
                                    {
                                        "border-primary": color === "primary",
                                        "border-secondary":
                                            color === "secondary",
                                        "border-accent": color === "accent",
                                        "border-muted-foreground":
                                            color === "neutral",
                                    }
                                )}
                                style={{
                                    width: 4,
                                    height: 4,
                                }}
                                initial={{ scale: 0, opacity: 0.8 }}
                                animate={{
                                    scale: 6,
                                    opacity: 0,
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    delay: i * 0.6,
                                    ease: "easeOut",
                                }}
                            />
                        ))}
                    </div>
                );

            case "pulse":
            default:
                return (
                    <div className={cn("relative", sizeClasses[size])}>
                        {/* Main pulsing circle */}
                        <motion.div
                            className={cn(
                                "absolute top-0 left-0 w-full h-full rounded-full",
                                colorClassesLight[color]
                            )}
                            animate={{
                                scale: [1, 1.2, 1],
                                opacity: [0.7, 1, 0.7],
                            }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                            }}
                        />

                        {/* Smaller orbiting circles */}
                        {[0, 1, 2, 3].map((i) => (
                            <motion.div
                                key={i}
                                className="absolute top-0 left-0 w-full h-full"
                                initial={{ rotate: i * 90 }}
                                animate={{ rotate: i * 90 + 360 }}
                                transition={{
                                    duration: 4,
                                    repeat: Infinity,
                                    ease: "linear",
                                    delay: i * 0.25,
                                }}
                            >
                                <motion.div
                                    className={cn(
                                        "absolute rounded-full",
                                        colorClasses[color]
                                    )}
                                    style={{
                                        width:
                                            size === "sm"
                                                ? 4
                                                : size === "md"
                                                ? 6
                                                : 8,
                                        height:
                                            size === "sm"
                                                ? 4
                                                : size === "md"
                                                ? 6
                                                : 8,
                                        top: 0,
                                        left: "50%",
                                        marginLeft:
                                            size === "sm"
                                                ? -2
                                                : size === "md"
                                                ? -3
                                                : -4,
                                    }}
                                    animate={{ scale: [1, 1.3, 1] }}
                                    transition={{
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: "easeInOut",
                                    }}
                                />
                            </motion.div>
                        ))}
                    </div>
                );
        }
    };

    return (
        <div className={cn("flex items-center justify-center", className)}>
            {renderLoadingAnimation()}
        </div>
    );
}

export default LoadingAnimation;
