"use client";

import { motion } from "framer-motion";
import React from "react";

// Feature card component
export interface FeatureCardProps {
    title: string;
    description: string;
    icon: React.ReactNode;
    delay?: number;
}

export function FeatureCard({
    title,
    description,
    icon,
    delay = 0,
}: FeatureCardProps) {
    return (
        <motion.div
            className="group relative rounded-xl border border-[#333] bg-[#202020] p-6 hover:border-[hsl(var(--cyan))]/30 transition-all hover:shadow-md hover:-translate-y-1"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay }}
            viewport={{ once: true, margin: "-100px" }}
        >
            <div className="mb-4 rounded-lg bg-[#252525] p-3 inline-flex text-[hsl(var(--cyan))] border border-[#333]">
                {icon}
            </div>
            <h3 className="text-xl font-bold mb-2 text-white">{title}</h3>
            <p className="text-[#AAAAAA]">{description}</p>
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[hsl(var(--pink))] to-[hsl(var(--cyan))] rounded-b-xl scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
        </motion.div>
    );
}
