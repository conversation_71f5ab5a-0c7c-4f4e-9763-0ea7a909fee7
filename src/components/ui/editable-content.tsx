"use client";

import { useState, useEffect, useCallback, useRef, memo } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Check, Loader2, Edit3, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

interface EditableContentProps {
    content: string;
    onSave: (content: string) => Promise<void>;
    placeholder?: string;
    className?: string;
    label?: string;
    debounceMs?: number;
    maxLength?: number;
    readOnly?: boolean;
}

// Component to highlight placeholder text patterns
const HighlightedText = memo(function HighlightedText({
    text,
    isEditing,
    onClick,
}: {
    text: string;
    isEditing: boolean;
    onClick?: () => void;
}) {
    if (isEditing) return null;

    // Enhanced pattern to match various placeholder formats
    const placeholderPattern =
        /(\[([^\]]+)\]|\{([^}]+)\}|<<([^>]+)>>|\(([^)]+)\)(?=\s|$|[.,!?])|__([^_]+)__|_+([^_]+)_+|\*([^*]+)\*|YOUR_[A-Z_]+|COMPANY_[A-Z_]+|HIRING_MANAGER|RECRUITER_NAME)/gi;

    const parts = text.split(placeholderPattern);

    const handleClick = (e: React.MouseEvent) => {
        console.log("HighlightedText clicked");
        e.stopPropagation();
        if (onClick) {
            onClick();
        }
    };

    return (
        <div
            className="prose max-w-none whitespace-pre-wrap text-sm leading-relaxed"
            onClick={handleClick}
        >
            {parts.map((part, index) => {
                if (!part) return null;

                // Check if this part matches a placeholder pattern
                const isPlaceholder = placeholderPattern.test(part);
                placeholderPattern.lastIndex = 0; // Reset regex state

                if (isPlaceholder) {
                    return (
                        <span
                            key={index}
                            className="bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 px-1.5 py-1 rounded-md border border-amber-300 dark:border-amber-700 relative group font-medium shadow-sm hover:bg-amber-200 dark:hover:bg-amber-900/50 transition-colors cursor-pointer"
                        >
                            {part}
                            <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10 shadow-lg pointer-events-none">
                                Click to edit - Replace with specific details
                            </span>
                        </span>
                    );
                }

                return <span key={index}>{part}</span>;
            })}
        </div>
    );
});

export const EditableContent = memo(function EditableContent({
    content: initialContent,
    onSave,
    placeholder = "Enter content...",
    className,
    label,
    debounceMs = 1000,
    maxLength = 50000,
    readOnly = false,
}: EditableContentProps) {
    const [content, setContent] = useState(initialContent);
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [justSaved, setJustSaved] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Update local state when prop changes
    useEffect(() => {
        console.log(
            "InitialContent prop changed to:",
            initialContent?.substring(0, 50) + "..."
        );
        setContent(initialContent);
    }, [initialContent]);

    // Debounced save function
    const debouncedSave = useCallback(
        async (valueToSave: string) => {
            console.log(
                "debouncedSave called, readOnly:",
                readOnly,
                "valueChanged:",
                valueToSave !== initialContent
            );
            if (valueToSave === initialContent || readOnly) return;

            try {
                console.log("Saving content...");
                setIsSaving(true);
                setError(null);
                await onSave(valueToSave);
                console.log("Content saved successfully");
                setJustSaved(true);
                setTimeout(() => setJustSaved(false), 2000);
            } catch (err) {
                console.error("Save error:", err);
                const errorMessage =
                    err instanceof Error
                        ? err.message
                        : typeof err === "string"
                        ? err
                        : "Failed to save content";
                setError(errorMessage);
                // Revert to initial content on error
                setContent(initialContent);
            } finally {
                setIsSaving(false);
            }
        },
        [onSave, initialContent, readOnly]
    );

    // Handle content changes with debouncing
    const handleChange = useCallback(
        (newContent: string) => {
            console.log(
                "Content changed:",
                newContent.substring(0, 50) + "..."
            );
            setContent(newContent);
            setError(null);

            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }

            saveTimeoutRef.current = setTimeout(() => {
                console.log("Triggering save after debounce");
                debouncedSave(newContent);
            }, debounceMs);
        },
        [debouncedSave, debounceMs]
    );

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
        };
    }, []);

    const handleEditClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        console.log(
            "Edit clicked - readOnly:",
            readOnly,
            "current editing:",
            isEditing
        );
        if (readOnly) {
            console.log("Component is read-only, not switching to edit mode");
            return;
        }
        console.log("Switching to edit mode");
        setIsEditing(true);
        setTimeout(() => {
            if (textareaRef.current) {
                textareaRef.current.focus();
                console.log("Textarea focused");
            } else {
                console.log("Textarea ref not available");
            }
        }, 100);
    };

    const handleBlur = (e: React.FocusEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsEditing(false);
        // Save immediately on blur if there are unsaved changes
        if (saveTimeoutRef.current) {
            clearTimeout(saveTimeoutRef.current);
            debouncedSave(content);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Escape") {
            setIsEditing(false);
            setContent(initialContent);
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
        }
    };

    // Get status info
    const hasChanges = content !== initialContent;
    const showSaveStatus = (isSaving || justSaved || error) && hasChanges;

    return (
        <div className={cn("group relative", className)}>
            {label && (
                <label className="block text-sm font-medium mb-2">
                    {label}
                    {maxLength && (
                        <span className="text-xs text-muted-foreground ml-2">
                            {content.length}/{maxLength}
                        </span>
                    )}
                </label>
            )}

            <div className="relative">
                {isEditing ? (
                    <Textarea
                        ref={textareaRef}
                        value={content}
                        onChange={(e) => handleChange(e.target.value)}
                        onBlur={handleBlur}
                        onKeyDown={handleKeyDown}
                        placeholder={placeholder}
                        maxLength={maxLength}
                        className={cn(
                            "min-h-[200px] resize-none transition-all",
                            error && "border-red-500 focus:border-red-500"
                        )}
                        autoFocus
                    />
                ) : (
                    <div
                        onClick={handleEditClick}
                        onMouseDown={(e) => {
                            console.log("Mouse down on editable div");
                        }}
                        className={cn(
                            "min-h-[200px] p-3 rounded-md border border-input bg-background",
                            "cursor-text transition-all hover:border-primary/50",
                            !readOnly &&
                                "group-hover:shadow-sm hover:bg-muted/5",
                            !content && "text-muted-foreground italic",
                            readOnly && "cursor-default",
                            "relative"
                        )}
                        style={{ userSelect: "none" }}
                    >
                        {content ? (
                            <HighlightedText
                                text={content}
                                isEditing={isEditing}
                                onClick={() =>
                                    handleEditClick({
                                        preventDefault: () => {},
                                        stopPropagation: () => {},
                                    } as any)
                                }
                            />
                        ) : (
                            <span className="text-muted-foreground italic">
                                {placeholder}
                            </span>
                        )}

                        {!readOnly && (
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Edit3 className="h-4 w-4 text-muted-foreground" />
                            </div>
                        )}
                    </div>
                )}

                {/* Save status indicator */}
                <AnimatePresence>
                    {showSaveStatus && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="absolute -top-8 right-0 flex items-center gap-1 text-xs"
                        >
                            {isSaving && (
                                <>
                                    <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                                    <span className="text-blue-600">
                                        Saving...
                                    </span>
                                </>
                            )}
                            {justSaved && !isSaving && (
                                <>
                                    <Check className="h-3 w-3 text-green-500" />
                                    <span className="text-green-600">
                                        Saved
                                    </span>
                                </>
                            )}
                            {error && (
                                <>
                                    <AlertCircle className="h-3 w-3 text-red-500" />
                                    <span
                                        className="text-red-600"
                                        title={error}
                                    >
                                        Failed to save
                                    </span>
                                </>
                            )}
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>

            {error && <p className="text-sm text-red-600 mt-1">{error}</p>}
        </div>
    );
});
