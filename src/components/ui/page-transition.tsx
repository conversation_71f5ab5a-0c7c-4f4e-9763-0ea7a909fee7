"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";

interface PageTransitionProps {
    children: React.ReactNode;
    className?: string;
}

export function PageTransition({ children, className }: PageTransitionProps) {
    return (
        <AnimatePresence mode="wait">
            <motion.div
                className={className}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{
                    type: "spring",
                    stiffness: 260,
                    damping: 20,
                    duration: 0.3,
                }}
            >
                {children}
            </motion.div>
        </AnimatePresence>
    );
}

// Advanced transition with reveal effect
export function AdvancedPageTransition({
    children,
    className,
}: PageTransitionProps) {
    return (
        <div className={className}>
            <AnimatePresence mode="wait">
                <motion.div
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    variants={{
                        initial: {
                            opacity: 0,
                        },
                        animate: {
                            opacity: 1,
                        },
                        exit: {
                            opacity: 0,
                        },
                    }}
                >
                    {children}
                </motion.div>
            </AnimatePresence>
            <motion.div
                className="fixed inset-0 z-50 pointer-events-none"
                initial={{ scaleX: 1 }}
                animate={{ scaleX: 0 }}
                exit={{ scaleX: 1 }}
                transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
                style={{
                    originX: 0,
                    background: "hsl(var(--primary)/5)",
                    backdropFilter: "blur(5px)",
                }}
            />
        </div>
    );
}

// Default export for dynamic imports
export default PageTransition;
