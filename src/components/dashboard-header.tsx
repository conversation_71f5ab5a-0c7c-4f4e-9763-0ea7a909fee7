"use client";

import { memo, useEffect, useState } from "react";
import { UserButton } from "@clerk/nextjs";
import Link from "next/link";
import { Plus<PERSON>ir<PERSON>, Sparkles, User } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import dynamic from "next/dynamic";
import { CyberpunkButton } from "@/components/ui/cyberpunk-button";
import { CyberpunkText } from "@/components/ui/cyberpunk-text";
import { ThemeToggle } from "@/components/theme-toggle";
import { Skeleton } from "@/components/ui/skeleton";

// Dynamically import the mobile navigation to optimize initial load
const MobileNav = dynamic(() => import("@/components/mobile-nav"), {
    ssr: true,
});

function DashboardHeader() {
    // Add client-side only rendering flag
    const [isMounted, setIsMounted] = useState(false);

    // Set mounted flag on client-side
    useEffect(() => {
        setIsMounted(true);
    }, []);

    return (
        <header className="sticky top-0 z-40 border-b border-border bg-background/90 backdrop-blur-md will-change-transform">
            <div className="container flex h-16 items-center justify-between py-4">
                <div className="flex items-center gap-2">
                    <MobileNav />
                    <Link href="/" className="flex items-center gap-2">
                        <span className="text-xl font-bold relative">
                            <span className="text-gradient-purple-blue">
                                Hire
                            </span>
                            <span className="text-gradient-pink-cyan">
                                Rizz
                            </span>
                            <div className="absolute -top-1 -right-3 w-2 h-2 bg-secondary rounded-full animate-ping-slow" />
                        </span>
                    </Link>
                </div>
                <div className="flex items-center gap-4">
                    <div className="hidden md:flex">
                        <Link href="/dashboard/applications/new">
                            <CyberpunkButton
                                size="sm"
                                variant="default"
                                className="gap-1 bg-primary hover:bg-primary/90"
                            >
                                <PlusCircle className="h-4 w-4" />
                                <span>New Application</span>
                            </CyberpunkButton>
                        </Link>
                    </div>
                    <ThemeToggle />
                    {/* Only render UserButton when client-side mounted */}
                    {isMounted ? (
                        <UserButton
                            afterSignOutUrl="/"
                            appearance={{
                                elements: {
                                    userButtonAvatarBox: "h-8 w-8 rounded-full",
                                    userButtonPopoverCard:
                                        "shadow-lg border border-border",
                                    userButtonPopoverActionButtonText:
                                        "text-foreground",
                                    userButtonPopoverActionButtonIcon:
                                        "text-primary",
                                },
                            }}
                        />
                    ) : (
                        <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                            <Skeleton className="h-8 w-8" />
                        </div>
                    )}
                </div>
            </div>
        </header>
    );
}

export { DashboardHeader };
export default memo(DashboardHeader);
