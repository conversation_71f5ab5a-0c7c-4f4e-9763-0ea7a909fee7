"use client";

import { memo, useCallback, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu, Home, FileText } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Sheet,
    Sheet<PERSON>ontent,
    SheetTrigger,
    SheetTitle,
    SheetHeader,
} from "@/components/ui/sheet";

function MobileNav() {
    const [isOpen, setIsOpen] = useState(false);
    const pathname = usePathname();

    // Memoize the close handler
    const handleClose = useCallback(() => {
        setIsOpen(false);
    }, []);

    return (
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
                <Button
                    variant="ghost"
                    size="icon"
                    aria-label="Open menu"
                    className="focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2"
                >
                    <Menu className="h-5 w-5" />
                </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[250px] sm:w-[300px]">
                <SheetHeader>
                    <SheetTitle>
                        <Link
                            href="/"
                            onClick={handleClose}
                            className="flex items-center"
                        >
                            <span className="text-xl font-bold">
                                <span className="text-[#2563eb]">Hire</span>
                                <span className="text-[#14b8a6]">Rizz</span>
                            </span>
                        </Link>
                    </SheetTitle>
                </SheetHeader>
                <nav className="mt-8 grid gap-1">
                    <Link
                        href="/dashboard"
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                            pathname === "/dashboard"
                                ? "bg-primary/15 text-primary"
                                : "hover:bg-primary/5"
                        }`}
                        onClick={handleClose}
                    >
                        <Home className="h-4 w-4" />
                        <span>Dashboard</span>
                    </Link>
                    <Link
                        href="/dashboard/applications"
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                            pathname.includes("/dashboard/applications")
                                ? "bg-primary/15 text-primary"
                                : "hover:bg-primary/5"
                        }`}
                        onClick={handleClose}
                    >
                        <FileText className="h-4 w-4" />
                        <span>Applications</span>
                    </Link>
                    <Link
                        href="/dashboard/resumes"
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                            pathname.includes("/dashboard/resumes")
                                ? "bg-primary/15 text-primary"
                                : "hover:bg-primary/5"
                        }`}
                        onClick={handleClose}
                    >
                        <FileText className="h-4 w-4" />
                        <span>Resumes</span>
                    </Link>
                </nav>

                <div className="absolute bottom-6 left-6 right-6">
                    <Link href="/dashboard/applications/new">
                        <Button className="w-full" onClick={handleClose}>
                            New Application
                        </Button>
                    </Link>
                </div>
            </SheetContent>
        </Sheet>
    );
}

export { MobileNav };
export default memo(MobileNav);
