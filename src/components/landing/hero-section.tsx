"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useEffect } from "react";
import { ArrowRight } from "lucide-react";

interface HeroSectionProps {
    isAuthenticated: boolean;
}

export function HeroSection({ isAuthenticated }: HeroSectionProps) {
    const heroRef = useRef<HTMLDivElement>(null);
    const { scrollY } = useScroll();

    const bgY = useTransform(scrollY, [0, 500], [0, 100]);
    const opacityHero = useTransform(scrollY, [0, 300], [1, 0]);

    useEffect(() => {
        const imagePreload = new Image();
        imagePreload.src = "/grid.svg";
    }, []);

    return (
        <section
            ref={heroRef}
            className="relative overflow-hidden min-h-[85vh] flex items-center justify-center bg-background text-foreground"
        >
            {/* Animated background */}
            <div className="absolute inset-0 w-full h-full overflow-hidden">
                <motion.div
                    className="absolute inset-0 bg-grid"
                    style={{ y: bgY }}
                />
                <div className="absolute -top-20 -right-20 w-96 h-96 rounded-full bg-primary/20 blur-[100px]"></div>
                <div className="absolute -bottom-40 -left-20 w-96 h-96 rounded-full bg-accent/20 blur-[100px]"></div>

                {/* Scanner effect */}
                <div className="absolute inset-0 overflow-hidden opacity-40 pointer-events-none">
                    <motion.div
                        className="scanner-line h-[1px]"
                        animate={{ top: ["-100%", "100%"] }}
                        transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "linear",
                            repeatDelay: 1,
                        }}
                    />
                </div>

                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-b from-background via-transparent to-background pointer-events-none"></div>
            </div>

            <motion.div
                className="container relative grid items-center gap-6 py-12 text-center mx-auto max-w-screen-xl"
                style={{ opacity: opacityHero }}
            >
                <motion.div
                    className="flex max-w-[980px] flex-col items-center gap-4 mx-auto"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <motion.div
                        className="inline-flex items-center rounded-md border border-border px-4 py-1.5 text-sm font-medium bg-card/80 mb-4"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.1 }}
                    >
                        <span className="flex h-2 w-2 rounded-full bg-accent mr-2"></span>
                        <span className="text-gradient-coral-amber">
                            THE FUTURE OF JOB HUNTING
                        </span>
                    </motion.div>

                    <div className="relative mb-2">
                        <motion.h1
                            className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.7, delay: 0.2 }}
                        >
                            <motion.span
                                className="relative z-10 block reveal-text mb-2"
                                initial={{ y: 20 }}
                                animate={{ y: 0 }}
                                transition={{ duration: 0.7, delay: 0.3 }}
                            >
                                <span className="text-foreground">
                                    Personalized
                                </span>
                            </motion.span>
                            <motion.span
                                className="block text-gradient-pink-purple"
                                initial={{ y: 20 }}
                                animate={{ y: 0 }}
                                transition={{ duration: 0.7, delay: 0.4 }}
                            >
                                Outreach Toolkit
                            </motion.span>
                        </motion.h1>
                        <div className="absolute -top-10 -left-10 w-20 h-20 bg-accent/30 rounded-full blur-xl animate-glow"></div>
                    </div>

                    <motion.p
                        className="max-w-[600px] text-xl text-muted-foreground mt-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.7, delay: 0.5 }}
                    >
                        Streamline your job search with AI-powered
                        personalization. Create tailored resumes and cover
                        letters that{" "}
                        <span className="text-secondary">
                            actually get noticed
                        </span>
                        .
                    </motion.p>

                    <motion.div
                        className="flex flex-wrap gap-6 mt-8 justify-center"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.7, delay: 0.6 }}
                    >
                        <motion.a
                            href={
                                isAuthenticated
                                    ? "/dashboard/applications/new"
                                    : "/sign-up"
                            }
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="inline-block"
                        >
                            <Button
                                size="lg"
                                className="inline-flex animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-8 font-medium text-slate-400 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50"
                            >
                                {isAuthenticated
                                    ? "Create New Application"
                                    : "Get Started For Free"}
                            </Button>
                        </motion.a>
                        {isAuthenticated ? (
                            <motion.a
                                href="/dashboard"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="inline-block"
                            >
                                <Button
                                    variant="outline"
                                    size="lg"
                                    className="border-border hover:border-accent/50 hover:text-accent rounded-lg px-8"
                                >
                                    Dashboard Home
                                </Button>
                            </motion.a>
                        ) : (
                            <motion.a
                                href="#features"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="inline-block"
                            >
                                <Button
                                    variant="outline"
                                    size="lg"
                                    className="border-border hover:border-accent/50 hover:text-accent rounded-lg px-8"
                                >
                                    Explore Features
                                </Button>
                            </motion.a>
                        )}
                    </motion.div>
                </motion.div>

                {/* Decorative elements */}
                <div className="absolute top-1/3 right-[10%] w-32 h-32 rounded-full">
                    <div className="relative h-full w-full">
                        <div className="absolute inset-0 rounded-full border border-accent/30 animate-ping-slow"></div>
                        <div className="absolute inset-2 rounded-full border border-accent/20"></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                            <div className="h-3 w-3 rounded-full bg-accent opacity-80"></div>
                        </div>
                    </div>
                </div>
                <div className="absolute bottom-1/4 left-[5%] w-16 h-16 rounded-full border border-primary/30 animate-float-delayed"></div>
            </motion.div>
        </section>
    );
}
