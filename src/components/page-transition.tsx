"use client";

import { useEffect, useState, useRef } from "react";
import { usePathname } from "next/navigation";
import { Preloader } from "./preloader";

export function PageTransition({ children }: { children: React.ReactNode }) {
    const [isLoading, setIsLoading] = useState(false);
    const pathname = usePathname();
    const lastPathRef = useRef(pathname);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const navigationStartTimeRef = useRef<number | null>(null);

    // Track pathname changes for route changes
    useEffect(() => {
        // Only run when pathname changes and not on initial mount
        if (lastPathRef.current !== pathname) {
            // Record the navigation start time
            navigationStartTimeRef.current = Date.now();

            // Show loading state
            setIsLoading(true);

            // Calculate how long the navigation should display the loader
            const minLoaderDuration = 300; // Minimum time to show loader for visual feedback

            // Clear existing timeout to prevent memory leaks
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }

            // Set a timeout to hide the loading screen after the calculated delay
            timeoutRef.current = setTimeout(() => {
                setIsLoading(false);
                navigationStartTimeRef.current = null;
                // Update last path reference
                lastPathRef.current = pathname;
            }, minLoaderDuration);
        }

        // Cleanup function
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [pathname]);

    // Add event listener for page unload
    useEffect(() => {
        const handleBeforeUnload = () => {
            // Clean up on page unload
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
            // Clean up on unmount
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return (
        <>
            <Preloader
                isLoading={isLoading}
                onAnimationComplete={() => {
                    // Additional callback if needed
                }}
            />
            {children}
        </>
    );
}
