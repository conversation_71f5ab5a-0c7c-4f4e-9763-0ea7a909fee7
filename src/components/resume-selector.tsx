"use client";

import { useState } from "react";
import { useResumes, useUpdateApplicationResume } from "@/lib/hooks/api-hooks";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, FileText, Plus } from "lucide-react";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import Link from "next/link";

interface ResumeData {
    id: number;
    name: string;
    userId: string;
    fileUrl: string;
    filePath: string;
    fileType: string;
    isDefault: boolean;
    parsedContent: any;
    createdAt: string;
    updatedAt: string;
}

interface ResumeSelectorProps {
    applicationId: string | number;
    resumeId?: string | number;
}

export function ResumeSelector({
    applicationId,
    resumeId,
}: ResumeSelectorProps) {
    const { data, isLoading, error } = useResumes();
    const updateResume = useUpdateApplicationResume();

    // Initialize selectedResumeId as number | null based on resumeId prop
    const [selectedResumeId, setSelectedResumeId] = useState<number | null>(
        resumeId ? Number(resumeId) : null
    );

    // Handle resume selection change
    const handleResumeChange = (value: string) => {
        const newResumeId = value === "none" ? null : parseInt(value);
        setSelectedResumeId(newResumeId);
        updateResume.mutate({
            applicationId: applicationId.toString(),
            resumeId: newResumeId,
        });
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-4">
                <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
                <span>Loading resumes...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center p-4 text-red-500">
                <p>Failed to load resumes</p>
            </div>
        );
    }

    // Compute currentResume on the fly
    const currentResume = selectedResumeId
        ? data?.find((resume: ResumeData) => resume.id === selectedResumeId)
        : null;

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">Resume</CardTitle>
                <CardDescription>
                    Select a resume for this application
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                {data.length === 0 ? (
                    <div className="text-center p-4">
                        <p className="mb-4 text-muted-foreground">
                            No resumes found
                        </p>
                        <Button asChild>
                            <Link href="/dashboard/resumes/new">
                                <Plus className="mr-2 h-4 w-4" />
                                Add Resume
                            </Link>
                        </Button>
                    </div>
                ) : (
                    <>
                        <Select
                            value={selectedResumeId?.toString() || "none"}
                            onValueChange={handleResumeChange}
                            disabled={updateResume.isPending}
                        >
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select a resume" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                {data.map((resume: ResumeData) => (
                                    <SelectItem
                                        key={resume.id}
                                        value={resume.id.toString()}
                                    >
                                        {resume.name}
                                        {resume.isDefault ? " (Default)" : ""}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        {currentResume && (
                            <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                                <div className="flex items-center">
                                    <FileText className="h-5 w-5 mr-2 text-primary" />
                                    <div>
                                        <p className="font-medium">
                                            {currentResume.name}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {currentResume.fileType.toUpperCase()}
                                        </p>
                                    </div>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                    <Link
                                        href={`/dashboard/resumes/${currentResume.id}`}
                                    >
                                        View
                                    </Link>
                                </Button>
                            </div>
                        )}

                        <div className="flex justify-end">
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/dashboard/resumes">
                                    Manage Resumes
                                </Link>
                            </Button>
                        </div>
                    </>
                )}
            </CardContent>
        </Card>
    );
}
