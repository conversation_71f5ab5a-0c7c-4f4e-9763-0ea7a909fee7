"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import PreloaderParticles from "./preloader-particles";

export function AppLoading() {
    const [loading, setLoading] = useState(true);
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        // Simulate loading the app with a progress indicator
        const startTime = Date.now();
        const loadingDuration = 2500; // 2.5 seconds

        const updateProgress = () => {
            const elapsedTime = Date.now() - startTime;
            const newProgress = Math.min(
                100,
                Math.floor((elapsedTime / loadingDuration) * 100)
            );
            setProgress(newProgress);

            if (newProgress < 100) {
                requestAnimationFrame(updateProgress);
            } else {
                // Delay hiding the loader slightly
                setTimeout(() => {
                    setLoading(false);
                }, 400);
            }
        };

        // Start the animation
        requestAnimationFrame(updateProgress);

        // Cleanup on unmount
        return () => {
            setLoading(false);
        };
    }, []);

    return (
        <AnimatePresence mode="wait">
            {loading && (
                <motion.div
                    className="fixed inset-0 z-[9999] bg-black flex flex-col items-center justify-center overflow-hidden"
                    initial={{ opacity: 1 }}
                    exit={{
                        opacity: 0,
                        transition: { duration: 0.8, ease: "easeInOut" },
                    }}
                >
                    {/* Interactive particles */}
                    <PreloaderParticles isActive={loading} />

                    {/* Main content */}
                    <div className="relative flex flex-col items-center justify-center">
                        {/* Brand logo animation */}
                        <motion.div
                            className="text-6xl font-bold mb-16"
                            initial={{ y: 40, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ duration: 0.8, ease: "easeOut" }}
                        >
                            <motion.span
                                className="inline-block text-gradient-purple-blue"
                                initial={{ y: 40 }}
                                animate={{ y: 0 }}
                                transition={{ duration: 0.6, ease: "easeOut" }}
                            >
                                Job
                            </motion.span>
                            <motion.span
                                className="inline-block text-gradient-pink-cyan"
                                initial={{ y: 40 }}
                                animate={{ y: 0 }}
                                transition={{
                                    duration: 0.6,
                                    delay: 0.2,
                                    ease: "easeOut",
                                }}
                            >
                                Craft
                            </motion.span>
                        </motion.div>

                        {/* Progress indicator */}
                        <motion.div
                            className="relative w-64 h-1 bg-white/10 rounded-full overflow-hidden"
                            initial={{ opacity: 0, scaleX: 0.8 }}
                            animate={{ opacity: 1, scaleX: 1 }}
                            transition={{ delay: 0.4, duration: 0.6 }}
                        >
                            <motion.div
                                className="absolute left-0 top-0 bottom-0 bg-gradient-to-r from-primary to-secondary rounded-full"
                                style={{ width: `${progress}%` }}
                            />
                        </motion.div>

                        {/* Slogan text */}
                        <motion.div
                            className="mt-8 text-white/60 text-lg tracking-wide"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6, duration: 0.6 }}
                        >
                            Craft Your Career
                        </motion.div>
                    </div>

                    {/* Footer text */}
                    <motion.div
                        className="absolute bottom-8 text-white/40 text-sm"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 1, duration: 0.8 }}
                    >
                        © {new Date().getFullYear()} JobCraft
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}

export default AppLoading;
