"use client";

import Link from "next/link";
import { FileText, PlusCircle, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function QuickActions() {
    return (
        <div className="mt-6">
            <div className="rounded-lg border bg-card p-4">
                <h3 className="font-medium">Quick Actions</h3>
                <div className="mt-4 space-y-2">
                    <Link href="/dashboard/applications/new">
                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start gap-2"
                        >
                            <Sparkles className="h-4 w-4" />
                            Magic Apply
                        </Button>
                    </Link>
                    <Link href="/dashboard/applications/new">
                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start gap-2"
                        >
                            <PlusCircle className="h-4 w-4" />
                            New Application
                        </Button>
                    </Link>
                    <Link href="/dashboard/resumes/upload">
                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start gap-2"
                        >
                            <FileText className="h-4 w-4" />
                            Upload Resume
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    );
}
