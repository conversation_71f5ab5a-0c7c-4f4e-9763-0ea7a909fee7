"use client";

import { memo, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import Link from "next/link";
import { formatDate } from "date-fns";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
    Eye,
    Download,
    CheckCircle,
    Trash2,
    MoreVertical,
    FileText,
} from "lucide-react";
import { formatResumeName } from "@/lib/utils";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import type { Resume } from "@/lib/types";

interface ResumeCardProps {
    resume: Resume;
    onSetDefault: (id: number) => void;
    onDelete: (id: number) => Promise<void>;
    isDeleting?: boolean;
}

const getFileIcon = (fileType: string) => {
    switch (fileType?.toLowerCase()) {
        case "pdf":
            return <FileText className="h-6 w-6 text-red-500" />;
        case "docx":
        case "doc":
            return <FileText className="h-6 w-6 text-blue-500" />;
        default:
            return <FileText className="h-6 w-6 text-gray-500" />;
    }
};

export const ResumeCard = memo(function ResumeCard({
    resume,
    onSetDefault,
    onDelete,
    isDeleting = false,
}: ResumeCardProps) {
    const router = useRouter();
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [isDeletingLocal, setIsDeletingLocal] = useState(false);

    const handleDelete = async () => {
        setIsDeletingLocal(true);
        try {
            await onDelete(resume.id);
            setShowDeleteDialog(false);
        } catch (error) {
            // Error is handled in parent component
        } finally {
            setIsDeletingLocal(false);
        }
    };

    const handleSetDefault = () => {
        onSetDefault(resume.id);
    };

    const isLoading = isDeleting || isDeletingLocal;

    return (
        <>
            <motion.div
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{
                    opacity: isLoading ? 0.5 : 1,
                    y: 0,
                    scale: isLoading ? 0.98 : 1,
                }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                transition={{ duration: 0.4 }}
                className={`group relative flex h-full flex-col overflow-hidden rounded-xl border bg-card/60 p-5 transition-all duration-300 ${
                    isLoading
                        ? "pointer-events-none border-destructive/20"
                        : "hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10"
                }`}
            >
                {!isLoading && (
                    <Link
                        href={`/dashboard/resumes/${resume.id}`}
                        className="absolute inset-0 z-0"
                        aria-label={`View ${resume.name}`}
                    />
                )}

                <div className="flex items-start justify-between">
                    <div className="flex flex-1 items-center gap-4">
                        <div className="rounded-lg bg-primary/10 p-3">
                            {getFileIcon(resume.fileType)}
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="truncate font-semibold text-lg transition-colors group-hover:text-primary">
                                {formatResumeName(resume.name)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                {formatDate(new Date(resume.createdAt), "PP")}
                            </p>
                        </div>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="relative z-10 ml-2 flex-shrink-0"
                                onClick={(e) => e.stopPropagation()}
                                disabled={isLoading}
                            >
                                <MoreVertical className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                            align="end"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <DropdownMenuItem
                                onClick={() =>
                                    router.push(
                                        `/dashboard/resumes/${resume.id}`
                                    )
                                }
                                className="cursor-pointer"
                            >
                                <Eye className="mr-2 h-4 w-4" /> View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                                <a
                                    href={resume.fileUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="w-full cursor-pointer"
                                >
                                    <Download className="mr-2 h-4 w-4" />{" "}
                                    Download
                                </a>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {!resume.isDefault && (
                                <DropdownMenuItem
                                    onClick={handleSetDefault}
                                    className="cursor-pointer"
                                >
                                    <CheckCircle className="mr-2 h-4 w-4" /> Set
                                    as Default
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                onClick={() => setShowDeleteDialog(true)}
                                className="text-destructive cursor-pointer focus:bg-destructive/10 focus:text-destructive"
                            >
                                <Trash2 className="mr-2 h-4 w-4" /> Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                {resume.parsedContent?.summary && (
                    <div className="mt-4 border-t border-border/50 pt-4">
                        <p className="text-sm text-muted-foreground line-clamp-3">
                            {resume.parsedContent.summary}
                        </p>
                    </div>
                )}

                <div className="mt-auto flex items-center justify-between pt-4 text-xs text-muted-foreground">
                    <span className="rounded-full bg-muted px-2 py-0.5 font-mono uppercase">
                        {resume.fileType}
                    </span>
                    {resume.isDefault && (
                        <div className="flex items-center gap-1.5 text-primary font-medium">
                            <CheckCircle className="h-3.5 w-3.5" />
                            <span>Default</span>
                        </div>
                    )}
                </div>

                {isLoading && (
                    <div className="absolute inset-0 z-20 flex items-center justify-center bg-background/50 backdrop-blur-sm">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                            <span>Deleting...</span>
                        </div>
                    </div>
                )}
            </motion.div>

            <DeleteConfirmationDialog
                open={showDeleteDialog}
                onOpenChange={setShowDeleteDialog}
                onConfirm={handleDelete}
                isLoading={isDeletingLocal}
                title="Delete Resume"
                description={`Are you sure you want to delete "${formatResumeName(
                    resume.name
                )}"? This will permanently remove the resume from your account and our servers.`}
            />
        </>
    );
});
