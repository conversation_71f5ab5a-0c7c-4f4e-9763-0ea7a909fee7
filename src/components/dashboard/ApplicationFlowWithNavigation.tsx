"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";
import ApplicationFlow from "./ApplicationFlow";

interface Application {
    id: number;
    status: string | null;
}

interface ApplicationFlowWithNavigationProps {
    applications: Application[];
}

export default function ApplicationFlowWithNavigation({
    applications,
}: ApplicationFlowWithNavigationProps) {
    const router = useRouter();

    const handleStatusClick = useCallback(
        (status: string) => {
            router.push(`/dashboard/applications?status=${status}`);
        },
        [router]
    );

    return (
        <ApplicationFlow
            applications={applications}
            onStatusClick={handleStatusClick}
        />
    );
}
