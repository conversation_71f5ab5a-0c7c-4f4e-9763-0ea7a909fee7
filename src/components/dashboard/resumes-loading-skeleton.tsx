import { motion } from "framer-motion";

export function ResumesLoadingSkeleton() {
    const loadingSkeletons = Array.from({ length: 4 }).map((_, i) => (
        <div
            key={i}
            className="bg-card p-4 rounded-xl border border-border animate-pulse"
        >
            <div className="flex items-center gap-4">
                <div className="h-10 w-10 rounded-lg bg-muted"></div>
                <div className="flex-1 space-y-2">
                    <div className="h-4 w-3/4 rounded bg-muted"></div>
                    <div className="h-3 w-1/2 rounded bg-muted"></div>
                </div>
            </div>
        </div>
    ));

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="container py-8"
        >
            <div className="mb-8 flex items-center justify-between">
                <div className="space-y-1">
                    <div className="h-8 w-48 rounded bg-muted animate-pulse"></div>
                    <div className="h-4 w-64 rounded bg-muted animate-pulse"></div>
                </div>
                <div className="h-11 w-36 rounded-md bg-muted animate-pulse"></div>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {loadingSkeletons}
            </div>
        </motion.div>
    );
}
