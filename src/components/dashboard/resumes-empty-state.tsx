"use client";

import { FileText, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingAnimation } from "@/components/ui/loading-animation";

interface ResumesEmptyStateProps {
    onUploadClick: () => void;
    isNavigating: boolean;
}

export function ResumesEmptyState({
    onUploadClick,
    isNavigating,
}: ResumesEmptyStateProps) {
    return (
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center bg-card/50 border-2 border-dashed border-border rounded-xl p-8">
            <div className="h-20 w-20 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <FileText className="h-10 w-10 text-primary" />
            </div>
            <h2 className="text-2xl font-bold mb-2">No Resumes Found</h2>
            <p className="text-muted-foreground mb-6 max-w-sm">
                It looks like you haven't uploaded any resumes yet. Get started
                by uploading your first one.
            </p>
            <Button
                onClick={onUploadClick}
                className="h-11"
                disabled={isNavigating}
            >
                {isNavigating ? (
                    <div className="flex items-center gap-2">
                        <LoadingAnimation
                            variant="pulse"
                            size="sm"
                            className="text-current"
                        />
                        <span>Loading...</span>
                    </div>
                ) : (
                    <>
                        <Upload className="mr-2 h-5 w-5" />
                        Upload Your First Resume
                    </>
                )}
            </Button>
        </div>
    );
}
