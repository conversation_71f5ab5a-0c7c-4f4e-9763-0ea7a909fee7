"use client";

import React, { useState, useEffect, useMemo, useC<PERSON>back, JSX } from "react";
import {
    ReactFlow,
    Background,
    Controls,
    useNodesState,
    useEdgesState,
    Position,
    MarkerType,
    BackgroundVariant,
    Panel,
    useReactFlow,
    getNodesBounds,
    Handle,
    ReactFlowProvider,
    getViewportForBounds,
} from "@xyflow/react";
import { toPng } from "html-to-image";
import "@xyflow/react/dist/style.css";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
    Check,
    X,
    Clock,
    Send,
    MessageSquare,
    Award,
    Download,
} from "lucide-react";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";

// Define application types
interface Application {
    id: number;
    status: string | null;
}

// Status styles with consistent design
const statusStyles = {
    not_applied: {
        background: "bg-slate-50 dark:bg-slate-800/50",
        borderColor: "border-slate-200 dark:border-slate-700",
        icon: Clock,
        color: "text-slate-500 dark:text-slate-400",
        indicator: "bg-slate-400",
    },
    applied: {
        background: "bg-blue-50 dark:bg-blue-900/50",
        borderColor: "border-blue-200 dark:border-blue-800",
        icon: Send,
        color: "text-blue-500 dark:text-blue-400",
        indicator: "bg-blue-400",
    },
    interviewing: {
        background: "bg-amber-50 dark:bg-amber-900/50",
        borderColor: "border-amber-200 dark:border-amber-800",
        icon: MessageSquare,
        color: "text-amber-500 dark:text-amber-400",
        indicator: "bg-amber-400",
    },
    offer: {
        background: "bg-emerald-50 dark:bg-emerald-900/50",
        borderColor: "border-emerald-200 dark:border-emerald-800",
        icon: Award,
        color: "text-emerald-500 dark:text-emerald-400",
        indicator: "bg-emerald-400",
    },
    rejected: {
        background: "bg-rose-50 dark:bg-rose-900/50",
        borderColor: "border-rose-200 dark:border-rose-800",
        icon: X,
        color: "text-rose-500 dark:text-rose-400",
        indicator: "bg-rose-400",
    },
    accepted: {
        background: "bg-green-50 dark:bg-green-900/50",
        borderColor: "border-green-200 dark:border-green-800",
        icon: Check,
        color: "text-green-500 dark:text-green-400",
        indicator: "bg-green-400",
    },
};

// Status node component with handles
const StatusNode = ({ data }: { data: any }) => {
    const style = statusStyles[data.status as keyof typeof statusStyles];
    const Icon = style.icon;
    const hasApplications = data.count > 0;

    // Base styles for handles
    const handleBaseStyle: React.CSSProperties = {
        width: 8,
        height: 8,
        background: "#cbd5e1",
        border: "1px solid #94a3b8",
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
    };

    return (
        <div style={{ position: "relative", width: "160px" }}>
            {" "}
            {/* Adjusted width */}
            <Handle
                type="target"
                position={Position.Left}
                style={{ ...handleBaseStyle, left: -4 }}
            />
            <TooltipProvider delayDuration={100}>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Card
                            className={`p-2.5 rounded-lg border ${style.background} ${style.borderColor} shadow-sm 
                            transition-all duration-200 cursor-pointer hover:shadow-md hover:border-primary/20`}
                        >
                            <div className="flex items-center gap-2 mb-1">
                                <Icon
                                    className={`w-3.5 h-3.5 ${style.color}`}
                                />
                                <span className="font-medium text-sm text-foreground/90">
                                    {data.label}
                                </span>
                            </div>
                            <div className="flex justify-between items-center pl-1">
                                <div className="text-xs text-muted-foreground">
                                    {data.count}{" "}
                                    {data.count === 1
                                        ? "application"
                                        : "applications"}
                                </div>
                                {hasApplications && (
                                    <div
                                        className={`w-1.5 h-1.5 rounded-full ${style.indicator} mr-1`}
                                    ></div>
                                )}
                            </div>
                        </Card>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                        <p>Click to view {data.label} applications</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
            <Handle
                type="source"
                position={Position.Right}
                style={{ ...handleBaseStyle, right: -4 }}
            />
        </div>
    );
};

// Flow legend component

// Initial node configurations - Adjusted for better initial layout
const initialNodes = [
    {
        id: "not_applied",
        type: "status",
        position: { x: 80, y: 180 }, // Shifted left
        data: { label: "Not Applied", status: "not_applied", count: 0 },
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
    },
    {
        id: "applied",
        type: "status",
        position: { x: 280, y: 180 }, // Shifted left
        data: { label: "Applied", status: "applied", count: 0 },
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
    },
    {
        id: "interviewing",
        type: "status",
        position: { x: 480, y: 100 }, // Shifted left
        data: { label: "Interviewing", status: "interviewing", count: 0 },
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
    },
    {
        id: "offer",
        type: "status",
        position: { x: 680, y: 100 }, // Kept Y, adjusted X slightly if needed for connections
        data: { label: "Offer", status: "offer", count: 0 },
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
    },
    {
        id: "rejected",
        type: "status",
        position: { x: 680, y: 180 }, // Kept Y, adjusted X slightly
        data: { label: "Rejected", status: "rejected", count: 0 },
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
    },
    {
        id: "accepted",
        type: "status",
        position: { x: 880, y: 100 }, // Shifted right slightly
        data: { label: "Accepted", status: "accepted", count: 0 },
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
    },
];

// Updated edges with styling matching the image
const initialEdges = [
    {
        id: "e1",
        source: "not_applied",
        target: "applied",
        type: "straight",
        markerEnd: {
            type: MarkerType.ArrowClosed,
            color: "#334155",
            width: 15,
            height: 15,
        },
        style: { strokeWidth: 1.5, stroke: "#334155" }, // Darker grey/black
    },
    {
        id: "e2",
        source: "applied",
        target: "interviewing",
        type: "straight",
        markerEnd: {
            type: MarkerType.ArrowClosed,
            color: "#334155",
            width: 15,
            height: 15,
        },
        style: { strokeWidth: 1.5, stroke: "#334155" },
    },
    {
        id: "e3",
        source: "interviewing",
        target: "offer",
        type: "default", // Using default for smoother curve
        markerEnd: {
            type: MarkerType.ArrowClosed,
            color: "#334155",
            width: 15,
            height: 15,
        },
        style: { strokeWidth: 1.5, stroke: "#334155" },
    },
    // {
    //     id: "e4",
    //     source: "interviewing",
    //     target: "rejected",
    //     type: "default",
    //     markerEnd: {
    //         type: MarkerType.ArrowClosed,
    //         color: "#334155",
    //         width: 15,
    //         height: 15,
    //     },
    //     style: { strokeWidth: 1.5, stroke: "#334155" },
    // },
    {
        id: "e5",
        source: "offer",
        target: "accepted",
        type: "straight",
        markerEnd: {
            type: MarkerType.ArrowClosed,
            color: "#334155",
            width: 15,
            height: 15,
        },
        style: { strokeWidth: 1.5, stroke: "#334155" },
    },
    // {
    //     id: "e6",
    //     source: "offer",
    //     target: "rejected",
    //     type: "default",
    //     markerEnd: {
    //         type: MarkerType.ArrowClosed,
    //         color: "#334155",
    //         width: 15,
    //         height: 15,
    //     },
    //     style: { strokeWidth: 1.5, stroke: "#334155" },
    // },
    {
        id: "e7",
        source: "applied",
        target: "rejected",
        type: "default",
        label: "Rejected Early",
        labelStyle: { fill: "#475569", fontSize: 9, fontWeight: 500 }, // Adjusted label style
        labelBgStyle: { fill: "#ffffff", fillOpacity: 0.6, padding: "2px 4px" },
        labelBgBorderRadius: 4,
        markerEnd: {
            type: MarkerType.ArrowClosed,
            color: "#334155",
            width: 15,
            height: 15,
        },
        style: { strokeWidth: 1.5, stroke: "#334155" },
    },
];

interface ApplicationFlowProps {
    applications: Application[];
    onStatusClick?: (status: string) => void;
}

// Download utility
function downloadImage(dataUrl: string) {
    const a = document.createElement("a");
    a.setAttribute("download", "application-flow.png");
    a.setAttribute("href", dataUrl);
    a.click();
}

// Main Flow Component
function FlowComponent({
    applications = [],
    onStatusClick,
}: ApplicationFlowProps): JSX.Element {
    const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
    const [isReady, setIsReady] = useState(false);
    const { getNodes } = useReactFlow();

    // Count applications by status
    useEffect(() => {
        if (!applications?.length) return;
        const statusCounts: Record<string, number> = {};
        applications.forEach((app) => {
            const status = app.status || "not_applied";
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        setNodes((nds) =>
            nds.map((node) => ({
                ...node,
                data: { ...node.data, count: statusCounts[node.id] || 0 },
            }))
        );
    }, [applications, setNodes]);

    // Client-side only rendering
    useEffect(() => {
        setIsReady(true);
        return () => setIsReady(false);
    }, []);

    const nodeTypes = useMemo(() => ({ status: StatusNode }), []);

    const handleNodeClick = useCallback(
        (_: React.MouseEvent, node: { id: string; data: any }) => {
            if (onStatusClick) {
                onStatusClick(node.id);
            }
        },
        [onStatusClick]
    );

    // Download Handler - Refined for accurate capture
    const onDownload = useCallback(() => {
        const nodesBounds = getNodesBounds(getNodes());
        const padding = 25; // Consistent padding

        // Calculate the viewport needed to encompass all nodes with padding
        const transform = getViewportForBounds(
            nodesBounds,
            nodesBounds.width + padding * 2,
            nodesBounds.height + padding * 2,
            0.5, // minZoom
            2, // maxZoom
            padding
        );

        const imageWidth = nodesBounds.width + padding * 2;
        const imageHeight = nodesBounds.height + padding * 2;

        const flowElement = document.querySelector(
            ".react-flow__viewport"
        ) as HTMLElement | null;
        if (!flowElement) {
            console.error("Could not find flow viewport element.");
            return;
        }

        toPng(flowElement, {
            backgroundColor: "#ffffff",
            width: imageWidth,
            height: imageHeight,
            style: {
                width: `${imageWidth}px`,
                height: `${imageHeight}px`,
                // Apply the calculated transform for accurate capture
                transform: `translate(${transform.x}px, ${transform.y}px) scale(${transform.zoom})`,
            },
        })
            .then((dataUrl: string) => downloadImage(dataUrl))
            .catch((err: Error) => {
                console.error("Error generating image:", err);
            });
    }, [getNodes]);

    if (!isReady) {
        return (
            <div className="w-full h-[370px] border rounded-lg bg-background/50 flex items-center justify-center">
                <p>Loading visualization...</p>
            </div>
        );
    }

    return (
        <div className="w-full h-[400px] border rounded-lg bg-white dark:bg-slate-900/30 relative shadow-sm overflow-hidden">
            {" "}
            {/* Updated height and background */}
            <div className="absolute top-3 left-4 z-10">
                <h2 className="text-base font-semibold text-foreground/90">
                    {" "}
                    {/* Adjusted text size */}
                    Application Journey Flow
                </h2>
                <p className="text-xs text-muted-foreground">
                    Click on a stage to view details
                </p>
            </div>
            <ReactFlow
                nodes={nodes}
                edges={edges}
                nodeTypes={nodeTypes}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onNodeClick={handleNodeClick}
                fitView
                minZoom={0.4}
                maxZoom={1.2}
                proOptions={{ hideAttribution: true }}
            >
                <Background
                    color="#e2e8f0" // Lighter background dots
                    gap={20} // Slightly larger gap
                    size={0.5} // Smaller dots
                    variant={BackgroundVariant.Dots}
                />
                <Controls
                    showInteractive={false}
                    position="bottom-left"
                    className="!m-2 !shadow-md !border !border-border/50 !rounded-lg !bg-background/80"
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "4px",
                        padding: "4px",
                    }}
                />

                <Panel position="bottom-center" className="pb-2">
                    {" "}
                    {/* Adjusted panel padding */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onDownload}
                        className="shadow-md bg-background hover:bg-muted/50 border-border/50"
                    >
                        <Download className="h-3.5 w-3.5 mr-1.5" />{" "}
                        {/* Adjusted icon size */}
                        Download Flow
                    </Button>
                </Panel>
            </ReactFlow>
        </div>
    );
}

// Export with ReactFlowProvider
export default function ApplicationFlow(
    props: ApplicationFlowProps
): JSX.Element {
    return (
        <ReactFlowProvider>
            <FlowComponent {...props} />
        </ReactFlowProvider>
    );
}
