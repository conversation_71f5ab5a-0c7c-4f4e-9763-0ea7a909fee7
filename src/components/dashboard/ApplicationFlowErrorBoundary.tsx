"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw } from "lucide-react";

interface ErrorBoundaryProps {
    children: ReactNode;
}

interface ErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
    errorInfo: string;
}

export class ApplicationFlowErrorBoundary extends Component<
    ErrorBoundaryProps,
    ErrorBoundaryState
> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false, error: null, errorInfo: "" };
    }

    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        // Log error to an error reporting service
        console.error("Application flow error:", error, errorInfo);
        this.setState({
            errorInfo: errorInfo.componentStack || "Unknown component stack",
        });
    }

    handleRetry = (): void => {
        this.setState({ hasError: false, error: null, errorInfo: "" });
    };

    render(): ReactNode {
        if (this.state.hasError) {
            return (
                <div className="w-full h-[350px] border rounded-lg bg-background/50 flex flex-col items-center justify-center p-4 shadow-sm">
                    <div className="rounded-full bg-amber-100 dark:bg-amber-900 p-3 mb-4">
                        <AlertTriangle className="w-8 h-8 text-amber-600 dark:text-amber-300" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">
                        Visualization Error
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4 text-center max-w-md">
                        {this.state.error?.message ||
                            "There was an error displaying the application flow visualization."}
                    </p>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={this.handleRetry}
                            className="flex items-center gap-1.5"
                        >
                            <RefreshCw className="h-4 w-4" />
                            Try Again
                        </Button>
                    </div>
                    {process.env.NODE_ENV === "development" && (
                        <details className="mt-4 text-xs text-muted-foreground bg-muted/50 p-2 rounded-md max-w-full overflow-auto">
                            <summary className="cursor-pointer">
                                Error details (developer only)
                            </summary>
                            <pre className="mt-2 whitespace-pre-wrap">
                                {this.state.error?.stack ||
                                    JSON.stringify(this.state.error)}
                            </pre>
                        </details>
                    )}
                </div>
            );
        }

        return this.props.children;
    }
}
