"use client";

import { memo } from "react";

interface ApplicationStats {
    status:
        | "not_applied"
        | "applied"
        | "interviewing"
        | "offer"
        | "rejected"
        | "accepted"
        | null;
    count: number;
}

interface ApplicationStatusVisualProps {
    stats: ApplicationStats[];
}

function ApplicationStatusVisual({ stats }: ApplicationStatusVisualProps) {
    // Calculate total applications
    const totalApplications = stats.reduce(
        (sum, stat) => sum + Number(stat.count),
        0
    );

    // Prepare data for status counts
    const statusCounts = {
        not_applied: 0,
        applied: 0,
        interviewing: 0,
        offer: 0,
        rejected: 0,
        accepted: 0,
    };

    stats.forEach((stat) => {
        if (stat.status) {
            statusCounts[stat.status] = Number(stat.count);
        }
    });

    return (
        <div className="rounded-xl border bg-card shadow-md overflow-hidden">
            <div className="flex h-3">
                <div
                    className="bg-[hsl(210,70%,60%)]" /* Vibrant Blue */
                    style={{
                        width: `${
                            totalApplications > 0
                                ? (statusCounts.not_applied /
                                      totalApplications) *
                                  100
                                : 0
                        }%`,
                        transition: "width 0.5s ease-in-out",
                    }}
                />
                <div
                    className="bg-[hsl(190,95%,50%)]" /* Bright Cyan */
                    style={{
                        width: `${
                            totalApplications > 0
                                ? (statusCounts.applied / totalApplications) *
                                  100
                                : 0
                        }%`,
                        transition: "width 0.5s ease-in-out",
                    }}
                />
                <div
                    className="bg-[hsl(267,75%,53%)]" /* Vibrant Purple */
                    style={{
                        width: `${
                            totalApplications > 0
                                ? (statusCounts.interviewing /
                                      totalApplications) *
                                  100
                                : 0
                        }%`,
                        transition: "width 0.5s ease-in-out",
                    }}
                />
                <div
                    className="bg-[hsl(165,86%,50%)]" /* Bright Teal */
                    style={{
                        width: `${
                            totalApplications > 0
                                ? ((statusCounts.offer +
                                      statusCounts.accepted) /
                                      totalApplications) *
                                  100
                                : 0
                        }%`,
                        transition: "width 0.5s ease-in-out",
                    }}
                />
                <div
                    className="bg-[hsl(330,80%,60%)]" /* Bright Magenta */
                    style={{
                        width: `${
                            totalApplications > 0
                                ? (statusCounts.rejected / totalApplications) *
                                  100
                                : 0
                        }%`,
                        transition: "width 0.5s ease-in-out",
                    }}
                />
            </div>
            <div className="grid grid-cols-5 p-5 text-center text-sm">
                <div>
                    <p className="font-medium mb-1">Not Applied</p>
                    <p className="text-[hsl(210,70%,60%)] font-bold">
                        {statusCounts.not_applied}
                    </p>
                </div>
                <div>
                    <p className="font-medium mb-1">Applied</p>
                    <p className="text-[hsl(190,95%,50%)] font-bold">
                        {statusCounts.applied}
                    </p>
                </div>
                <div>
                    <p className="font-medium mb-1">Interviewing</p>
                    <p className="text-[hsl(267,75%,53%)] font-bold">
                        {statusCounts.interviewing}
                    </p>
                </div>
                <div>
                    <p className="font-medium mb-1">Offers</p>
                    <p className="text-[hsl(165,86%,50%)] font-bold">
                        {statusCounts.offer + statusCounts.accepted}
                    </p>
                </div>
                <div>
                    <p className="font-medium mb-1">Rejected</p>
                    <p className="text-[hsl(330,80%,60%)] font-bold">
                        {statusCounts.rejected}
                    </p>
                </div>
            </div>
        </div>
    );
}

export default memo(ApplicationStatusVisual);
