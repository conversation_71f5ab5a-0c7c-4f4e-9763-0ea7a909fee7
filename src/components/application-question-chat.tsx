"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { SendHorizontal, Loader2 } from "lucide-react";
import {
    useApplicationChat,
    useAnswerApplicationQuestion,
} from "@/lib/hooks/api-hooks";
import { LoadingAnimation } from "@/components/ui/loading-animation";
import { motion, AnimatePresence } from "framer-motion";

// Common questions suggestions
const SUGGESTED_QUESTIONS = [
    "Why are you interested in working for our company?",
    "What makes you a good fit for this position?",
    "Tell me about a project you're most proud of.",
    "What are your greatest strengths?",
    "How do you handle challenges or obstacles?",
    "Where do you see yourself in 5 years?",
    "Why should we hire you?",
    "How do you stay updated with industry trends?",
];

type Message = {
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
};

interface ApplicationQuestionChatProps {
    application: {
        id: string;
        company: string;
        position: string;
        jobDescription?: string;
    };
    resume?: {
        parsedContent?: any;
    };
}

export function ApplicationQuestionChat({
    application,
    resume,
}: ApplicationQuestionChatProps) {
    // Fetch existing chat history
    const { data: chatData, isLoading: isLoadingChat } = useApplicationChat(
        application.id
    );

    const [localMessages, setLocalMessages] = useState<Message[]>([
        {
            role: "assistant",
            content:
                "Hi! I'm your AI assistant for job applications. Ask me any question about this application, and I'll help craft a professional answer.",
            timestamp: new Date(),
        },
    ]);

    // Combine server messages with local state
    const [input, setInput] = useState("");
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const { mutate: answerQuestion, isPending: isGenerating } =
        useAnswerApplicationQuestion();

    // Convert server chat history to our Message format if available
    useEffect(() => {
        if (chatData?.chat && chatData.chat.length > 0) {
            const formattedMessages = chatData.chat.map((msg: any) => ({
                role: msg.speaker === "user" ? "user" : "assistant",
                content: msg.message,
                timestamp: new Date(msg.time),
            })) as Message[];

            // Only set if we have messages to avoid overwriting welcome message
            if (formattedMessages.length > 0) {
                setLocalMessages([
                    {
                        role: "assistant",
                        content:
                            "Hi! I'm your AI assistant for job applications. Ask me any question about this application, and I'll help craft a professional answer.",
                        timestamp: new Date(),
                    },
                    ...formattedMessages,
                ]);
            }
        }
    }, [chatData]);

    // Auto-scroll to bottom of messages
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [localMessages]);

    const handleSendMessage = () => {
        if (!input.trim() || isGenerating) return;

        // Add user message to local state immediately for UI feedback
        const userMessage: Message = {
            role: "user",
            content: input.trim(),
            timestamp: new Date(),
        };
        setLocalMessages((prev) => [...prev, userMessage]);

        // Generate AI response
        answerQuestion(
            {
                question: input.trim(),
                applicationId: application.id,
            },
            {
                onSuccess: (data) => {
                    const assistantMessage: Message = {
                        role: "assistant",
                        content: data.answer,
                        timestamp: new Date(),
                    };
                    setLocalMessages((prev) => [...prev, assistantMessage]);
                    setInput("");
                },
            }
        );
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    const handleSuggestedQuestion = (question: string) => {
        setInput(question);
    };

    // Show loading state while fetching initial chat history
    if (isLoadingChat) {
        return (
            <div className="flex justify-center items-center h-[400px]">
                <LoadingAnimation variant="ripple" size="lg" />
            </div>
        );
    }

    return (
        <div className="flex flex-col h-[600px] max-h-[80vh]">
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-secondary/5 rounded-t-lg">
                <AnimatePresence>
                    {localMessages.map((message, index) => (
                        <motion.div
                            key={index}
                            className={`flex ${
                                message.role === "user"
                                    ? "justify-end"
                                    : "justify-start"
                            }`}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div
                                className={`max-w-[80%] rounded-lg px-4 py-3 ${
                                    message.role === "user"
                                        ? "bg-primary text-primary-foreground"
                                        : "bg-card border"
                                }`}
                            >
                                <div className="whitespace-pre-wrap">
                                    {message.content}
                                </div>
                                <div
                                    className={`text-xs mt-1 ${
                                        message.role === "user"
                                            ? "text-primary-foreground/70"
                                            : "text-muted-foreground"
                                    }`}
                                >
                                    {message.timestamp.toLocaleTimeString([], {
                                        hour: "2-digit",
                                        minute: "2-digit",
                                    })}
                                </div>
                            </div>
                        </motion.div>
                    ))}
                    {isGenerating && (
                        <motion.div
                            className="flex justify-start"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div className="max-w-[80%] rounded-lg px-4 py-3 bg-card border">
                                <LoadingAnimation variant="pulse" size="sm" />
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
                <div ref={messagesEndRef} />
            </div>

            {localMessages.length === 1 && (
                <div className="px-4 pb-4 pt-2 bg-muted/20">
                    <p className="text-sm text-muted-foreground mb-2">
                        Suggested questions:
                    </p>
                    <div className="flex flex-wrap gap-2">
                        {SUGGESTED_QUESTIONS.slice(0, 4).map(
                            (question, index) => (
                                <Button
                                    key={index}
                                    variant="outline"
                                    size="sm"
                                    className="text-xs"
                                    onClick={() =>
                                        handleSuggestedQuestion(question)
                                    }
                                >
                                    {question}
                                </Button>
                            )
                        )}
                    </div>
                </div>
            )}

            <div className="p-4 border-t">
                <div className="flex gap-2">
                    <Textarea
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="Type a question (e.g., 'Why do you want to work for us?')"
                        className="flex-1 min-h-[60px] resize-none"
                        disabled={isGenerating}
                    />
                    <Button
                        onClick={handleSendMessage}
                        disabled={!input.trim() || isGenerating}
                        className="self-end"
                    >
                        {isGenerating ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <SendHorizontal className="h-4 w-4" />
                        )}
                    </Button>
                </div>
            </div>
        </div>
    );
}
