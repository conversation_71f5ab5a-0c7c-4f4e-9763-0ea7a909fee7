"use client";

import { useEffect, useRef, memo } from "react";

interface PreloaderParticlesProps {
    isActive: boolean;
}

function PreloaderParticles({ isActive }: PreloaderParticlesProps) {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        if (!isActive || !canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        // Set canvas dimensions
        const setCanvasDimensions = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };
        setCanvasDimensions();
        window.addEventListener("resize", setCanvasDimensions);

        // Particle class
        class Particle {
            x: number;
            y: number;
            size: number;
            speedX: number;
            speedY: number;
            color: string;

            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 3 + 1;
                this.speedX = Math.random() * 1 - 0.5;
                this.speedY = Math.random() * 1 - 0.5;
                this.color = `hsla(${Math.random() * 60 + 240}, 100%, 70%, ${
                    Math.random() * 0.5 + 0.3
                })`;
            }

            update() {
                this.x += this.speedX;
                this.y += this.speedY;

                if (this.x > canvas.width) this.x = 0;
                else if (this.x < 0) this.x = canvas.width;
                if (this.y > canvas.height) this.y = 0;
                else if (this.y < 0) this.y = canvas.height;
            }

            draw() {
                if (!ctx) return;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // Create particles
        const particlesArray: Particle[] = [];
        const particleCount = Math.min(
            50,
            Math.floor((canvas.width * canvas.height) / 15000)
        );

        for (let i = 0; i < particleCount; i++) {
            particlesArray.push(new Particle());
        }

        // Animation loop
        let animationId: number;
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            for (let i = 0; i < particlesArray.length; i++) {
                particlesArray[i].update();
                particlesArray[i].draw();
            }

            animationId = requestAnimationFrame(animate);
        };

        animate();

        // Cleanup
        return () => {
            window.removeEventListener("resize", setCanvasDimensions);
            cancelAnimationFrame(animationId);
        };
    }, [isActive]);

    return <canvas ref={canvasRef} className="absolute inset-0 z-[-1]" />;
}

export default memo(PreloaderParticles);
