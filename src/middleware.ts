import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

// Define public routes
const isPublicRoute = createRouteMatcher([
    "/",
    "/api/webhook",
    "/sign-in(.*)",
    "/sign-up(.*)",
    "/sso-callback(.*)",
    "/api/uploadthing(.*)",
    "/__clerk(.*)", // Important: allow Clerk's internal routes
]);

// Define API routes to match all /api/* paths
const isApiRoute = createRouteMatcher(["/api/(.*)"]);

// Define dashboard routes that should be protected but not with 404
const isDashboardRoute = createRouteMatcher(["/dashboard(.*)"]);

// Protect all routes except public ones
export default clerkMiddleware(async (auth, req) => {
    const url = new URL(req.url);

    // Special handling for Clerk session cookies
    const hasClerkSession =
        req.headers.get("cookie")?.includes("__session=") ||
        req.headers.get("cookie")?.includes("__clerk_db_jwt=");

    if (isDashboardRoute(req) && hasClerkSession) {
        return NextResponse.next();
    }

    if (isApiRoute(req) && hasClerkSession) {
        return NextResponse.next();
    }

    // Handle Clerk auth flow
    const clerkStatus = url.searchParams.get("__clerk_status");
    const isSigningIn = clerkStatus === "signed_in";
    const isSigningUp = clerkStatus === "signed_up";

    if (
        (isSigningIn || isSigningUp) &&
        (url.pathname.startsWith("/sign-in") ||
            url.pathname.startsWith("/sign-up"))
    ) {
        const redirectTarget =
            url.searchParams.get("redirect_url") || "/dashboard";
        return NextResponse.redirect(new URL(redirectTarget, req.url));
    }

    // Allow public routes
    if (isPublicRoute(req)) {
        return NextResponse.next();
    }

    // Check authentication
    const { userId } = await auth();

    // Handle API routes
    if (isApiRoute(req)) {
        return NextResponse.next();
    }

    // Handle dashboard routes
    if (isDashboardRoute(req)) {
        if (!userId) {
            const signInUrl = new URL("/sign-in", req.url);
            signInUrl.searchParams.set("redirect_url", req.url);
            return NextResponse.redirect(signInUrl);
        }
        return NextResponse.next();
    }

    // For all other routes, return 404 if not authenticated
    if (!userId) {
        return new Response("Not Found", { status: 404 });
    }

    return NextResponse.next();
});

export const config = {
    matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
