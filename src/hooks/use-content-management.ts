import { useCallback, useState, useMemo } from "react";
import { useQueryClient } from "@tanstack/react-query";
import {
    useApplication,
    useGeneratedContent,
    useGenerateContent,
    useUpdateGeneratedContent,
    useUpdateApplicationField,
    type ApplicationStatus,
} from "@/lib/hooks/api-hooks";
import { toast } from "sonner";

export type ContentType = "coverLetter" | "linkedinMessage" | "coldEmail";

interface UseContentManagementProps {
    applicationId: string;
}

export function useContentManagement({ applicationId }: UseContentManagementProps) {
    const [isGeneratingContent, setIsGeneratingContent] = useState<Record<ContentType, boolean>>({
        coverLetter: false,
        linkedinMessage: false,
        coldEmail: false,
    });

    const queryClient = useQueryClient();

    // Data fetching
    const {
        data: application,
        isLoading: isLoadingApp,
        error: applicationError,
    } = useApplication(applicationId);

    const {
        data: generatedContent,
        isLoading: isLoadingContent,
        error: contentError,
        refetch: refetchContent,
    } = useGeneratedContent(applicationId);

    // Mutations
    const { mutate: generateContent } = useGenerateContent();
    const updateField = useUpdateApplicationField();
    const updateContent = useUpdateGeneratedContent();

    // Combined data with memoization
    const combinedData = useMemo(() => {
        if (!application) return null;
        
        return {
            ...application,
            coverLetter: generatedContent?.coverLetter || null,
            linkedinMessage: generatedContent?.linkedinMessage || null,
            coldEmail: generatedContent?.coldEmail || null,
        };
    }, [application, generatedContent]);

    // Content generation handler
    const handleGenerateContent = useCallback(
        async (type: ContentType) => {
            if (!applicationId || !application) {
                toast.error("Application data not available");
                return;
            }

            setIsGeneratingContent(prev => ({ ...prev, [type]: true }));

            try {
                const contentType = type === "coverLetter" 
                    ? "cover-letter" 
                    : type === "linkedinMessage" 
                    ? "linkedin-message" 
                    : "cold-email";

                await new Promise<void>((resolve, reject) => {
                    generateContent({
                        type: contentType,
                        applicationId,
                        company: application.company,
                        position: application.position,
                        jobDescription: application.jobDescription,
                        forceRegenerate: true,
                        parsedJobDescription: application.parsedJobDescription,
                        resumeId: application.resumeId,
                    }, {
                        onSuccess: () => {
                            toast.success(`${type} generated successfully!`);
                            resolve();
                        },
                        onError: (error) => {
                            toast.error(`Failed to generate ${type}`);
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error(`Error generating ${type}:`, error);
            } finally {
                setIsGeneratingContent(prev => ({ ...prev, [type]: false }));
            }
        },
        [applicationId, application, generateContent]
    );

    // Content update handler
    const handleContentUpdate = useCallback(
        async (field: ContentType, value: string) => {
            try {
                await updateContent.mutateAsync({
                    applicationId,
                    field,
                    value,
                });
                toast.success("Content updated successfully");
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Failed to update content";
                toast.error(errorMessage);
                throw error;
            }
        },
        [applicationId, updateContent]
    );

    // Field update handler
    const handleFieldUpdate = useCallback(
        async (field: string, value: string) => {
            try {
                await updateField.mutateAsync({
                    id: applicationId,
                    field,
                    value,
                });
                toast.success("Field updated successfully");
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Failed to update field";
                toast.error(errorMessage);
                throw error;
            }
        },
        [applicationId, updateField]
    );

    // Refresh handler
    const handleRefresh = useCallback(() => {
        queryClient.invalidateQueries({ queryKey: ["application", applicationId] });
        refetchContent();
        toast.success("Data refreshed");
    }, [queryClient, applicationId, refetchContent]);

    // Utility functions
    const copyToClipboard = useCallback(async (text: string, label: string) => {
        try {
            await navigator.clipboard.writeText(text);
            toast.success(`${label} copied to clipboard!`);
        } catch (error) {
            toast.error("Failed to copy to clipboard");
        }
    }, []);

    const downloadContent = useCallback((content: string, filename: string) => {
        try {
            const blob = new Blob([content], { type: "text/plain" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            toast.success("File downloaded successfully");
        } catch (error) {
            toast.error("Failed to download file");
        }
    }, []);

    const isLoading = isLoadingApp || isLoadingContent;
    const error = applicationError || contentError;

    return {
        // Data
        combinedData,
        application,
        generatedContent,
        
        // Loading states
        isLoading,
        isGeneratingContent,
        
        // Error states
        error,
        
        // Handlers
        handleGenerateContent,
        handleContentUpdate,
        handleFieldUpdate,
        handleRefresh,
        copyToClipboard,
        downloadContent,
    };
} 