"use client";

import { useState, useCallback, useMemo } from "react";
import { z } from "zod";
import { validateData, type ValidationResult } from "@/lib/validation";

interface UseFormValidationOptions<T> {
    schema: z.ZodSchema<T>;
    initialValues?: Partial<T>;
    onSubmit?: (data: T) => void | Promise<void>;
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
}

export function useFormValidation<T extends Record<string, any>>({
    schema,
    initialValues = {},
    onSubmit,
    validateOnChange = false,
    validateOnBlur = true,
}: UseFormValidationOptions<T>) {
    const [values, setValues] = useState<Partial<T>>(initialValues);
    const [errors, setErrors] = useState<Record<string, string[]>>({});
    const [touched, setTouched] = useState<Set<string>>(new Set());
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitCount, setSubmitCount] = useState(0);

    // Validation function
    const validate = useCallback(
        (data: Partial<T> = values): ValidationResult<T> => {
            return validateData(schema, data);
        },
        [schema, values]
    );

    // Field validation
    const validateField = useCallback(
        (field: string, value: any) => {
            try {
                // Create a partial object with just this field and validate
                const partialData = { [field]: value } as Partial<T>;
                const validation = validate(partialData);

                if (validation.success) {
                    setErrors((prev) => {
                        const newErrors = { ...prev };
                        delete newErrors[field];
                        return newErrors;
                    });
                } else if (validation.errors?.[field]) {
                    setErrors((prev) => ({
                        ...prev,
                        [field]: validation.errors![field],
                    }));
                }
            } catch (error) {
                console.error("Field validation error:", error);
            }
        },
        [validate]
    );

    // Set field value
    const setValue = useCallback(
        (field: string, value: any) => {
            setValues((prev) => ({ ...prev, [field]: value }));

            if (validateOnChange && touched.has(field)) {
                validateField(field, value);
            }
        },
        [validateOnChange, touched, validateField]
    );

    // Set multiple values
    const setMultipleValues = useCallback((newValues: Partial<T>) => {
        setValues((prev) => ({ ...prev, ...newValues }));
    }, []);

    // Handle blur
    const handleBlur = useCallback(
        (field: string) => {
            setTouched((prev) => new Set(prev).add(field));

            if (validateOnBlur) {
                const value = values[field];
                validateField(field, value);
            }
        },
        [validateOnBlur, values, validateField]
    );

    // Handle submit
    const handleSubmit = useCallback(
        async (e?: React.FormEvent) => {
            e?.preventDefault();
            setSubmitCount((prev) => prev + 1);

            const validation = validate();

            if (!validation.success) {
                setErrors(validation.errors || {});
                // Mark all fields as touched on submit attempt
                const allFields = Object.keys(values);
                setTouched(new Set(allFields));
                return;
            }

            if (onSubmit && validation.data) {
                try {
                    setIsSubmitting(true);
                    await onSubmit(validation.data);
                    // Clear form on successful submit
                    setValues({});
                    setErrors({});
                    setTouched(new Set());
                } catch (error) {
                    console.error("Submit error:", error);
                    setErrors({
                        general: ["An error occurred while submitting"],
                    });
                } finally {
                    setIsSubmitting(false);
                }
            }
        },
        [validate, onSubmit, schema]
    );

    // Reset form
    const reset = useCallback(() => {
        setValues(initialValues);
        setErrors({});
        setTouched(new Set());
        setSubmitCount(0);
    }, [initialValues]);

    // Clear errors
    const clearErrors = useCallback(() => {
        setErrors({});
    }, []);

    // Clear field error
    const clearFieldError = useCallback((field: string) => {
        setErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors[field];
            return newErrors;
        });
    }, []);

    // Computed values
    const isValid = useMemo(() => {
        const validation = validate();
        return validation.success;
    }, [validate]);

    const hasErrors = useMemo(() => {
        return Object.keys(errors).length > 0;
    }, [errors]);

    const getFieldError = useCallback(
        (field: string): string | undefined => {
            return errors[field]?.[0];
        },
        [errors]
    );

    const hasFieldError = useCallback(
        (field: string): boolean => {
            return Boolean(errors[field]?.length);
        },
        [errors]
    );

    const isFieldTouched = useCallback(
        (field: string): boolean => {
            return touched.has(field);
        },
        [touched]
    );

    // Field props helper
    const getFieldProps = useCallback(
        (field: string) => {
            return {
                value: values[field] || "",
                onChange: (
                    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
                ) => {
                    setValue(field, e.target.value);
                },
                onBlur: () => handleBlur(field),
                error: getFieldError(field),
                hasError: hasFieldError(field),
                touched: isFieldTouched(field),
            };
        },
        [
            values,
            setValue,
            handleBlur,
            getFieldError,
            hasFieldError,
            isFieldTouched,
        ]
    );

    return {
        // Values
        values,
        setValue,
        setValues: setMultipleValues,

        // Errors
        errors,
        getFieldError,
        hasFieldError,
        clearErrors,
        clearFieldError,

        // Touched
        touched,
        isFieldTouched,
        handleBlur,

        // Validation
        validate,
        isValid,
        hasErrors,

        // Form state
        isSubmitting,
        submitCount,

        // Actions
        handleSubmit,
        reset,

        // Helpers
        getFieldProps,
    };
}
