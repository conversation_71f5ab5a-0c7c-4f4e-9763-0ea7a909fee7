"use client";

import { useState, useEffect, useMemo, useCallback } from "react";

interface UseSearchOptions<T> {
    data: T[];
    searchKeys: (keyof T)[];
    debounceMs?: number;
    minSearchLength?: number;
    caseSensitive?: boolean;
}

export function useSearch<T extends Record<string, any>>({
    data,
    searchKeys,
    debounceMs = 300,
    minSearchLength = 2,
    caseSensitive = false,
}: UseSearchOptions<T>) {
    const [query, setQuery] = useState("");
    const [debouncedQuery, setDebouncedQuery] = useState("");

    // Debounce search query
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedQuery(query);
        }, debounceMs);

        return () => clearTimeout(timer);
    }, [query, debounceMs]);

    // Memoized search function
    const searchFunction = useCallback(
        (items: T[], searchTerm: string) => {
            if (!searchTerm || searchTerm.length < minSearchLength) {
                return items;
            }

            const normalizedQuery = caseSensitive
                ? searchTerm
                : searchTerm.toLowerCase();

            return items.filter((item) => {
                return searchKeys.some((key) => {
                    const value = item[key];
                    if (!value) return false;

                    const stringValue = String(value);
                    const normalizedValue = caseSensitive
                        ? stringValue
                        : stringValue.toLowerCase();

                    return normalizedValue.includes(normalizedQuery);
                });
            });
        },
        [searchKeys, minSearchLength, caseSensitive]
    );

    // Memoized filtered results
    const filteredData = useMemo(() => {
        return searchFunction(data, debouncedQuery);
    }, [data, debouncedQuery, searchFunction]);

    // Search metadata
    const isSearching = query !== debouncedQuery;
    const hasQuery = debouncedQuery.length >= minSearchLength;
    const resultCount = filteredData.length;

    return {
        query,
        setQuery,
        filteredData,
        isSearching,
        hasQuery,
        resultCount,
        originalCount: data.length,
    };
}

// Advanced search with filters
interface UseAdvancedSearchOptions<T> extends UseSearchOptions<T> {
    filters?: Record<string, (item: T) => boolean>;
    sortBy?: keyof T;
    sortOrder?: "asc" | "desc";
}

export function useAdvancedSearch<T extends Record<string, any>>({
    filters = {},
    sortBy,
    sortOrder = "asc",
    ...searchOptions
}: UseAdvancedSearchOptions<T>) {
    const [activeFilters, setActiveFilters] = useState<Set<string>>(new Set());

    const { filteredData: searchResults, ...searchProps } =
        useSearch(searchOptions);

    // Apply filters and sorting
    const processedData = useMemo(() => {
        let result = searchResults;

        // Apply active filters
        if (activeFilters.size > 0) {
            result = result.filter((item) => {
                return Array.from(activeFilters).every((filterKey) => {
                    const filter = filters[filterKey];
                    return filter ? filter(item) : true;
                });
            });
        }

        // Apply sorting
        if (sortBy) {
            result = [...result].sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];

                if (aValue === bValue) return 0;

                const comparison = aValue < bValue ? -1 : 1;
                return sortOrder === "asc" ? comparison : -comparison;
            });
        }

        return result;
    }, [searchResults, activeFilters, filters, sortBy, sortOrder]);

    const toggleFilter = useCallback((filterKey: string) => {
        setActiveFilters((prev) => {
            const newFilters = new Set(prev);
            if (newFilters.has(filterKey)) {
                newFilters.delete(filterKey);
            } else {
                newFilters.add(filterKey);
            }
            return newFilters;
        });
    }, []);

    const clearFilters = useCallback(() => {
        setActiveFilters(new Set());
    }, []);

    return {
        ...searchProps,
        filteredData: processedData,
        activeFilters,
        toggleFilter,
        clearFilters,
        filterCount: activeFilters.size,
    };
}
