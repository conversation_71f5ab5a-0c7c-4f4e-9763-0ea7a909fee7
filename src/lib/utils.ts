import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import DOMPurify from "dompurify";

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export function formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return (
        Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
    );
}

/**
 * Formats a resume filename for display by removing file extensions,
 * replacing underscores and hyphens with spaces, and cleaning up whitespace
 * @param name The resume filename to format
 * @returns Formatted, human-readable name
 */
export function formatResumeName(name: string): string {
    return name
        .replace(/\.(pdf|docx|doc)$/i, "") // Remove file extensions
        .replace(/_/g, " ") // Replace underscores with spaces
        .replace(/-/g, " ") // Replace hyphens with spaces
        .replace(/\s+/g, " ") // Replace multiple spaces with a single space
        .trim();
}

/**
 * Sanitizes a string to prevent XSS attacks
 * @param html The string to sanitize
 * @returns Sanitized string with dangerous content removed
 */
export function sanitizeHtml(html: string): string {
    if (typeof window === "undefined") {
        // Server-side - basic sanitization
        return html
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // Client-side - use DOMPurify
    return DOMPurify.sanitize(html, {
        USE_PROFILES: { html: true },
        ALLOWED_TAGS: ["b", "i", "em", "strong", "p", "br", "ul", "ol", "li"],
        ALLOWED_ATTR: [],
    });
}
