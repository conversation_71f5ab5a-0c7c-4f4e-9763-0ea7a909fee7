// src/lib/hooks/api-hooks.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { queryClient } from "@/lib/query-client";
// Application Queries
export type ApplicationStatus =
    | "not_applied"
    | "applied"
    | "interviewing"
    | "offer"
    | "rejected"
    | "accepted";

interface Application {
    id: string;
    company: string;
    position: string;
    status: ApplicationStatus;
    location?: string;
    jobDescription?: string;
    parsedJobDescription?: {
        company?: string;
        position?: string;
        location?: string;
        applicationUrl?: string;
        requiredSkills?: string[];
        requiredExperience?: string;
        keyQualifications?: string[];
        jobResponsibilities?: string[];
        estimatedSalaryRange?: string;
        companyValues?: string[];
        potentialChallenges?: string[];
    };
    notes?: string;
    createdAt: string;
    resumeId?: number;
    // Add other fields as needed
}

export function useApplication(id: string) {
    return useQuery({
        queryKey: ["application", id],
        queryFn: async (): Promise<Application> => {
            const response = await fetch(`/api/applications/${id}`, {
                credentials: "include",
            });
            if (!response.ok) {
                throw new Error("Failed to fetch application");
            }
            return response.json();
        },
        retry: 1,
    });
}

export function useApplications() {
    return useQuery({
        queryKey: ["applications"],
        queryFn: async () => {
            const response = await fetch("/api/applications", {
                credentials: "include",
            });
            if (!response.ok) throw new Error("Failed to fetch applications");
            return response.json();
        },
    });
}

// const mutation = useMutation({
//     mutationFn: createApplication,
//     onSuccess: (data) => {
//         if (formData.jobDescription) {
//             router.push(`/dashboard/applications/${data.id}/generate`);
//         } else {
//             router.push("/dashboard/applications");
//         }
//         queryClient.invalidateQueries({ queryKey: ["applications"] });
//     },
//     onError: (error) => {
//         setError(error.message || "Failed to create application");
//     },
// });

// Generated Content Queries
export function useGeneratedContent(applicationId: string) {
    return useQuery({
        queryKey: ["generatedContent", applicationId],
        queryFn: async () => {
            console.log(`Fetching content for application ${applicationId}`);
            const response = await fetch(
                `/api/applications/${applicationId}/content`,
                {
                    credentials: "include",
                    cache: "no-store", // Ensure fresh data
                }
            );
            console.log("Response status:", response.status);
            if (!response.ok)
                throw new Error("Failed to fetch generated content");
            const data = await response.json();
            console.log(
                `Received content for application ${applicationId}:`,
                data
            );
            console.log(
                "Cover letter preview:",
                data.coverLetter?.substring(0, 100)
            );
            return data;
        },
        enabled: !!applicationId,
        refetchInterval: false, // Don't automatically poll
        staleTime: 0, // Consider data always stale
        gcTime: 5 * 60 * 1000, // Cache for 5 minutes
    });
}

// Content Generation Mutation
export function useGenerateContent() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            type,
            applicationId,
            forceRegenerate = true,
            resumeId,
            company,
            position,
            jobDescription,
            parsedJobDescription,
        }: {
            type: string;
            applicationId: string;
            forceRegenerate?: boolean;
            resumeId?: number;
            company?: string;
            position?: string;
            jobDescription?: string;
            parsedJobDescription?: any;
        }) => {
            const response = await fetch(
                `/api/applications/${applicationId}/content`,
                {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        type:
                            type === "cover-letter"
                                ? "cover"
                                : type === "linkedin-message"
                                ? "linkedin"
                                : "email",
                        forceRegenerate,
                    }),
                    credentials: "include",
                }
            );

            if (!response.ok) throw new Error("Failed to generate content");
            return response.json();
        },
        onMutate: async (variables) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({
                queryKey: ["generatedContent", variables.applicationId],
            });

            // Return previous state in case we need to rollback
            return {
                prevData: queryClient.getQueryData([
                    "generatedContent",
                    variables.applicationId,
                ]),
            };
        },
        onSuccess: (data, variables, context) => {
            console.log("Generated content success:", data);

            // Get the content type without hyphens
            const contentType = variables.type.replace(/-/g, "");

            // Fetch the existing data
            const oldData =
                queryClient.getQueryData([
                    "generatedContent",
                    variables.applicationId,
                ]) || {};

            // Create new data object with the updated field
            const newData = {
                ...oldData,
                [contentType]: data[contentType],
            };

            console.log("Setting query data to:", newData);

            // Update the cache directly
            queryClient.setQueryData(
                ["generatedContent", variables.applicationId],
                newData
            );

            toast.success("Content generated successfully!");
        },
        onError: (error, variables, context) => {
            console.error("Error generating content:", error);

            // Restore previous data if available
            if (context?.prevData) {
                queryClient.setQueryData(
                    ["generatedContent", variables.applicationId],
                    context.prevData
                );
            }

            toast.error("Failed to generate content");
        },
        onSettled: (data, error, variables) => {
            // Always refetch after mutation to ensure data consistency
            queryClient.invalidateQueries({
                queryKey: ["generatedContent", variables.applicationId],
            });
        },
    });
}

// Resume Queries
export function useResume(resumeId: string) {
    return useQuery({
        queryKey: ["resume", resumeId],
        queryFn: async () => {
            const response = await fetch(`/api/resumes/${resumeId}`, {
                credentials: "include",
            });
            if (!response.ok) throw new Error("Failed to fetch resume");
            return response.json();
        },
        enabled: !!resumeId,
    });
}

export function useResumes() {
    return useQuery({
        queryKey: ["resumes"],
        queryFn: async () => {
            const response = await fetch("/api/resumes", {
                credentials: "include",
            });
            if (!response.ok) throw new Error("Failed to fetch resumes");
            return response.json();
        },
    });
}

// Application Resume Queries
export function useApplicationCurrentResume(applicationId: string) {
    return useQuery({
        queryKey: ["application-current-resume", applicationId],
        queryFn: async () => {
            const response = await fetch(
                `/api/applications/${applicationId}/resumes`,
                {
                    credentials: "include",
                }
            );
            if (!response.ok)
                throw new Error("Failed to fetch application current resume");
            return response.json();
        },
        enabled: !!applicationId,
    });
}

export function useUpdateApplicationResume() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            applicationId,
            resumeId,
        }: {
            applicationId: string;
            resumeId: number | null;
        }) => {
            const response = await fetch(
                `/api/applications/${applicationId}/resumes`,
                {
                    method: "PUT",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ resumeId }),
                    credentials: "include",
                }
            );

            if (!response.ok) {
                const error = await response.json();
                throw new Error(
                    error.error || "Failed to update application resume"
                );
            }

            return response.json();
        },
        onSuccess: (data, { applicationId }) => {
            // Update the application data in the cache
            queryClient.setQueryData(["application", applicationId], data);

            // Invalidate the application-resumes query to refresh the data
            queryClient.invalidateQueries({
                queryKey: ["application-resumes", applicationId],
            });

            toast.success("Resume updated successfully");
        },
        onError: (error: Error) => {
            toast.error(error.message || "Failed to update resume");
        },
    });
}

// Update application status
export function useUpdateApplicationStatus() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            id,
            status,
            application,
        }: {
            id: string;
            status: ApplicationStatus;
            application: Application;
        }) => {
            const response = await fetch(`/api/applications/${id}`, {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ ...application, status }),
                credentials: "include",
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || "Failed to update status");
            }

            return response.json();
        },
        onSuccess: (data, { id }) => {
            queryClient.setQueryData(["application", id], data);
            queryClient.invalidateQueries({ queryKey: ["applications"] });

            toast.success(`Application status updated successfully`);
        },
        onError: (error: Error) => {
            toast.error(error.message);
        },
    });
}

// Update generated content mutation
export function useUpdateGeneratedContent() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            applicationId,
            field,
            value,
        }: {
            applicationId: string;
            field: "coverLetter" | "linkedinMessage" | "coldEmail";
            value: string;
        }) => {
            const response = await fetch(
                `/api/applications/${applicationId}/content`,
                {
                    method: "PATCH",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ field, value }),
                    credentials: "include",
                }
            );

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `Failed to update ${field}`);
            }

            return response.json();
        },
        onMutate: async ({ applicationId, field, value }) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({
                queryKey: ["generatedContent", applicationId],
            });

            // Snapshot the previous value
            const previousContent = queryClient.getQueryData([
                "generatedContent",
                applicationId,
            ]) as any;

            // Optimistically update to the new value
            if (previousContent) {
                queryClient.setQueryData(["generatedContent", applicationId], {
                    ...previousContent,
                    [field]: value,
                });
            }

            // Return a context object with the snapshotted value
            return { previousContent };
        },
        onError: (err, { applicationId }, context) => {
            // If the mutation fails, use the context we returned above
            if (context?.previousContent) {
                queryClient.setQueryData(
                    ["generatedContent", applicationId],
                    context.previousContent
                );
            }
            console.error("Generated content update error:", err);
            // Don't re-throw here, let the component handle it through the mutation state
        },
        onSuccess: (data, { applicationId, field }) => {
            console.log("Update successful, response data:", data);

            // Get current data
            const currentData = queryClient.getQueryData([
                "generatedContent",
                applicationId,
            ]) as any;

            // Update the cache with the new value
            if (currentData) {
                queryClient.setQueryData(["generatedContent", applicationId], {
                    ...currentData,
                    [field]: data[field] || data.value || currentData[field],
                });
            }

            // Also invalidate to ensure consistency
            queryClient.invalidateQueries({
                queryKey: ["generatedContent", applicationId],
            });
        },
    });
}

// Update application field mutation
export function useUpdateApplicationField() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            id,
            field,
            value,
        }: {
            id: string;
            field: string;
            value: string;
        }) => {
            const response = await fetch(`/api/applications/${id}/field`, {
                method: "PATCH",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ field, value }),
                credentials: "include",
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `Failed to update ${field}`);
            }

            return response.json();
        },
        onMutate: async ({ id, field, value }) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: ["application", id] });

            // Snapshot the previous value
            const previousApplication = queryClient.getQueryData([
                "application",
                id,
            ]) as Application;

            // Optimistically update to the new value
            if (previousApplication) {
                queryClient.setQueryData(["application", id], {
                    ...previousApplication,
                    [field]: value,
                });
            }

            // Return a context object with the snapshotted value
            return { previousApplication };
        },
        onError: (err, { id }, context) => {
            // If the mutation fails, use the context we returned above
            if (context?.previousApplication) {
                queryClient.setQueryData(
                    ["application", id],
                    context.previousApplication
                );
            }
            throw err; // Re-throw to be handled by the component
        },
        onSuccess: (data, { id }) => {
            // Update the cache with the response data
            queryClient.setQueryData(["application", id], data);
            // Also invalidate applications list to keep it in sync
            queryClient.invalidateQueries({ queryKey: ["applications"] });
        },
    });
}

// Valid application statuses
export const VALID_STATUSES: ApplicationStatus[] = [
    "not_applied",
    "applied",
    "interviewing",
    "offer",
    "rejected",
    "accepted",
];

// Filtered applications query - now only filters by status
export function useFilteredApplications(filters?: {
    status?: ApplicationStatus;
}) {
    return useQuery({
        queryKey: ["applications", filters?.status],
        queryFn: async () => {
            // Build URL with query parameters
            let url = "/api/applications";

            if (filters?.status) {
                url += `?status=${filters.status}`;
            }

            const response = await fetch(url, {
                credentials: "include",
            });
            if (!response.ok) throw new Error("Failed to fetch applications");

            return response.json();
        },
        staleTime: 30 * 1000, // 30 seconds
    });
}
// Application Chat Hook
export function useApplicationChat(applicationId: string) {
    return useQuery({
        queryKey: ["application-chat", applicationId],
        queryFn: async () => {
            const response = await fetch(
                `/api/ai/application-questions?applicationId=${applicationId}`,
                {
                    credentials: "include",
                }
            );
            if (!response.ok) {
                throw new Error("Failed to fetch chat history");
            }
            return response.json();
        },
        enabled: !!applicationId,
    });
}

// Hook for generating answers to application questions
export function useAnswerApplicationQuestion() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            question,
            applicationId,
        }: {
            question: string;
            applicationId: string;
        }) => {
            const response = await fetch("/api/ai/application-questions", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ question, applicationId }),
                credentials: "include",
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Failed to generate answer");
            }

            return response.json();
        },
        onSuccess: (_, variables) => {
            // Invalidate chat history after successfully generating an answer
            queryClient.invalidateQueries({
                queryKey: ["application-chat", variables.applicationId],
            });
            toast.success("Answer generated successfully");
        },
        onError: (error) =>
            toast.error(
                error instanceof Error
                    ? error.message
                    : "Failed to generate answer"
            ),
    });
}

// Delete application
export function useDeleteApplication() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (applicationId: string) => {
            const response = await fetch(`/api/applications/${applicationId}`, {
                method: "DELETE",
                credentials: "include",
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(
                    errorData.error || "Failed to delete application"
                );
            }

            return response.json();
        },
        onSuccess: () => {
            // Invalidate applications queries to refresh the list
            queryClient.invalidateQueries({ queryKey: ["applications"] });
            toast.success("Application deleted successfully");
        },
        onError: (error: Error) => {
            toast.error(error.message || "Failed to delete application");
        },
    });
}
