import { z } from "zod";

// Base validation schemas
export const emailSchema = z
    .string()
    .email("Please enter a valid email address")
    .min(1, "Email is required");

export const urlSchema = z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal(""));

export const phoneSchema = z
    .string()
    .regex(/^[\+]?[0-9\s\-\(\)]{10,}$/, "Please enter a valid phone number")
    .optional()
    .or(z.literal(""));

export const nameSchema = z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces");

// File validation
export const fileSchema = z.object({
    name: z.string(),
    size: z.number().max(10 * 1024 * 1024, "File size must be less than 10MB"),
    type: z.enum(
        [
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
        {
            errorMap: () => ({
                message: "Only PDF and DOCX files are allowed",
            }),
        }
    ),
});

// Resume validation
export const resumeSchema = z.object({
    name: z
        .string()
        .min(1, "Resume name is required")
        .max(100, "Name is too long"),
    file: fileSchema,
});

// Job application validation
export const jobApplicationSchema = z.object({
    company: z
        .string()
        .min(1, "Company name is required")
        .max(255, "Company name is too long"),
    position: z
        .string()
        .min(1, "Position is required")
        .max(255, "Position is too long"),
    jobDescription: z
        .string()
        .min(10, "Job description must be at least 10 characters")
        .max(10000, "Job description is too long"),
    location: z
        .string()
        .max(255, "Location is too long")
        .optional()
        .or(z.literal("")),
    salary: z
        .string()
        .max(100, "Salary is too long")
        .optional()
        .or(z.literal("")),
    applicationUrl: urlSchema,
    contactName: nameSchema.optional().or(z.literal("")),
    contactEmail: emailSchema.optional().or(z.literal("")),
    contactPhone: phoneSchema,
    status: z.enum([
        "not_applied",
        "applied",
        "interviewing",
        "offer",
        "rejected",
        "accepted",
    ]),
    notes: z
        .string()
        .max(2000, "Notes are too long")
        .optional()
        .or(z.literal("")),
    jobLink: urlSchema,
});

// User profile validation
export const userProfileSchema = z.object({
    firstName: nameSchema.optional().or(z.literal("")),
    lastName: nameSchema.optional().or(z.literal("")),
    email: emailSchema,
});

// Content generation validation
export const contentGenerationSchema = z.object({
    applicationId: z.number().positive("Invalid application ID"),
    type: z.enum(["cover_letter", "linkedin_message", "cold_email"]),
    customPrompt: z
        .string()
        .max(1000, "Custom prompt is too long")
        .optional()
        .or(z.literal("")),
});

// Search validation
export const searchSchema = z.object({
    query: z
        .string()
        .min(1, "Search query is required")
        .max(200, "Search query is too long"),
    filters: z
        .object({
            status: z.array(z.string()).optional(),
            dateRange: z
                .object({
                    from: z.date().optional(),
                    to: z.date().optional(),
                })
                .optional(),
        })
        .optional(),
});

// Validation utilities
export type ValidationResult<T> = {
    success: boolean;
    data?: T;
    errors?: Record<string, string[]>;
};

export function validateData<T>(
    schema: z.ZodSchema<T>,
    data: unknown
): ValidationResult<T> {
    try {
        const result = schema.parse(data);
        return { success: true, data: result };
    } catch (error) {
        if (error instanceof z.ZodError) {
            const errors: Record<string, string[]> = {};
            error.errors.forEach((err) => {
                const path = err.path.join(".");
                if (!errors[path]) errors[path] = [];
                errors[path].push(err.message);
            });
            return { success: false, errors };
        }
        return { success: false, errors: { general: ["Validation failed"] } };
    }
}

// Custom validation functions
export const customValidators = {
    // Check if resume name is unique
    isUniqueResumeName: async (
        name: string,
        userId: string,
        excludeId?: number
    ) => {
        // This would be implemented with actual database check
        // For now, return true
        return true;
    },

    // Check if application already exists for company + position
    isUniqueApplication: async (
        company: string,
        position: string,
        userId: string,
        excludeId?: number
    ) => {
        // This would be implemented with actual database check
        // For now, return true
        return true;
    },

    // Validate job description quality
    isQualityJobDescription: (jobDescription: string) => {
        const wordCount = jobDescription.split(/\s+/).length;
        const hasRequirements =
            /requirement|skill|experience|qualifications/i.test(jobDescription);
        const hasResponsibilities = /responsible|duties|role|will/i.test(
            jobDescription
        );

        return {
            isValid: wordCount >= 50 && hasRequirements && hasResponsibilities,
            suggestions: [
                wordCount < 50 ? "Job description seems too short" : null,
                !hasRequirements
                    ? "Consider adding requirements section"
                    : null,
                !hasResponsibilities
                    ? "Consider adding responsibilities section"
                    : null,
            ].filter(Boolean),
        };
    },
};

// Form validation hook helpers
export const getFieldError = (
    errors: Record<string, string[]> | undefined,
    field: string
): string | undefined => {
    return errors?.[field]?.[0];
};

export const hasFieldError = (
    errors: Record<string, string[]> | undefined,
    field: string
): boolean => {
    return Boolean(errors?.[field]?.length);
};
