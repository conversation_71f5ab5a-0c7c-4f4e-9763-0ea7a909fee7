import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import * as schema from "./schema";

// Use direct URL to bypass connection pooler if available
const connectionString = process.env.DIRECT_URL || process.env.DATABASE_URL!;

// For development, disable SSL certificate validation
if (process.env.NODE_ENV === "development") {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
}

// Create a Neon SQL client
const sql = neon(connectionString, {
    fetchOptions: {
        cache: "no-store",
    },
});

// Create the database instance with Drizzle
export const db = drizzle(sql, { schema });

// Add error handling for development
if (process.env.NODE_ENV === "development") {
    // Handle connection errors manually
    process.on("unhandledRejection", (error: Error) => {
        if (
            error.message.includes("SASL_SIGNATURE_MISMATCH") ||
            error.message.includes("self signed certificate") ||
            error.message.includes("certificate")
        ) {
            console.error("Database connection error:", error.message);
            console.log(
                "Database SSL certificate validation is disabled for development"
            );
        }
    });
}

// Disable migrations for now
// We'll handle migrations manually
