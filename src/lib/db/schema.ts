import {
    pgTable,
    serial,
    text,
    timestamp,
    varchar,
    boolean,
    json,
    integer,
    uuid,
    pgEnum,
    jsonb,
    index,
} from "drizzle-orm/pg-core";
import { z } from "zod";

// Enums
export const applicationStatusEnum = pgEnum("application_status", [
    "not_applied",
    "applied",
    "interviewing",
    "offer",
    "rejected",
    "accepted",
]);

// Define chat message type
export const ChatMessageSchema = z.object({
    speaker: z.enum(["user", "bot"]),
    time: z.coerce.date(),
    message: z.string(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type ChatHistory = ChatMessage[];

// Users table (linked to Clerk)
export const users = pgTable("users", {
    id: text("id").primaryKey(), // Clerk user ID
    email: text("email").notNull().unique(),
    firstName: text("first_name"),
    lastName: text("last_name"),
    profileImage: text("profile_image"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Resumes table
export const resumes = pgTable(
    "resumes",
    {
        id: serial("id").primaryKey(),
        userId: text("user_id")
            .notNull()
            .references(() => users.id, { onDelete: "cascade" }),
        name: varchar("name", { length: 255 }).notNull(),
        fileUrl: text("file_url").notNull(),
        filePath: text("file_path"), // Store the file path/key for deletion
        fileType: varchar("file_type", { length: 10 }).notNull(), // PDF, DOCX
        isDefault: boolean("is_default").default(false),
        parsedContent: json("parsed_content"),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
    },
    (table) => ({
        userIdIdx: index("resumes_user_id_idx").on(table.userId),
        isDefaultIdx: index("resumes_is_default_idx").on(table.isDefault),
        createdAtIdx: index("resumes_created_at_idx").on(table.createdAt),
    })
);

// Job Applications table
export const jobApplications = pgTable(
    "job_applications",
    {
        id: serial("id").primaryKey(),
        userId: text("user_id")
            .notNull()
            .references(() => users.id, { onDelete: "cascade" }),
        resumeId: integer("resume_id").references(() => resumes.id),
        company: varchar("company", { length: 255 }).notNull(),
        position: varchar("position", { length: 255 }).notNull(),
        jobDescription: text("job_description"),
        parsedJobDescription: json("parsed_job_description"), // Store the parsed JD analysis results
        location: varchar("location", { length: 255 }),
        salary: varchar("salary", { length: 100 }),
        applicationUrl: text("application_url"),
        contactName: varchar("contact_name", { length: 255 }),
        contactEmail: varchar("contact_email", { length: 255 }),
        contactPhone: varchar("contact_phone", { length: 50 }),
        status: applicationStatusEnum("status").default("not_applied"),
        notes: text("notes"),
        appliedDate: timestamp("applied_date"),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
        jobLink: text("job_link"),
    },
    (table) => ({
        userIdIdx: index("applications_user_id_idx").on(table.userId),
        statusIdx: index("applications_status_idx").on(table.status),
        createdAtIdx: index("applications_created_at_idx").on(table.createdAt),
        companyPositionIdx: index("applications_company_position_idx").on(
            table.company,
            table.position
        ),
        userStatusIdx: index("applications_user_status_idx").on(
            table.userId,
            table.status
        ),
        appliedDateIdx: index("applications_applied_date_idx").on(
            table.appliedDate
        ),
    })
);

// Generated Documents table
export const generatedDocuments = pgTable(
    "generated_documents",
    {
        id: serial("id").primaryKey(),
        userId: text("user_id")
            .notNull()
            .references(() => users.id, { onDelete: "cascade" }),
        applicationId: integer("application_id").references(
            () => jobApplications.id,
            { onDelete: "cascade" }
        ),
        coverLetter: text("cover_letter"),
        linkedinMessage: text("linkedin_message"),
        coldEmail: text("cold_email"),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
        chat: jsonb("chat").$type<ChatHistory>().default([]),
    },
    (table) => ({
        userIdIdx: index("generated_docs_user_id_idx").on(table.userId),
        applicationIdIdx: index("generated_docs_application_id_idx").on(
            table.applicationId
        ),
        createdAtIdx: index("generated_docs_created_at_idx").on(
            table.createdAt
        ),
    })
);

// URL Shortener table
export const shortenedUrls = pgTable(
    "shortened_urls",
    {
        id: serial("id").primaryKey(),
        shortCode: varchar("short_code", { length: 10 }).notNull().unique(),
        originalUrl: text("original_url").notNull(),
        userId: text("user_id").references(() => users.id),
        applicationId: integer("application_id").references(
            () => jobApplications.id
        ),
        clickCount: integer("click_count").default(0),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        expiresAt: timestamp("expires_at"),
    },
    (table) => ({
        shortCodeIdx: index("shortened_urls_short_code_idx").on(
            table.shortCode
        ),
        userIdIdx: index("shortened_urls_user_id_idx").on(table.userId),
        createdAtIdx: index("shortened_urls_created_at_idx").on(
            table.createdAt
        ),
        expiresAtIdx: index("shortened_urls_expires_at_idx").on(
            table.expiresAt
        ),
    })
);
