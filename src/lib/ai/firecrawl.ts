import FireCrawlApp from "@mendable/firecrawl-js";
import { z } from "zod";

const FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY;
const IS_PRODUCTION = process.env.NODE_ENV === "production";
const APP_ENV =
    process.env.NEXT_PUBLIC_VERCEL_ENV || process.env.NODE_ENV || "unknown";

// Log environment info on module load for better debugging
console.log(
    `[FireCrawl] Module initialized in environment: ${APP_ENV}, API key exists: ${!!FIRECRAWL_API_KEY}`
);

export const schema = z.object({
    requiredSkills: z.array(z.string()).optional(),
    requiredExperience: z.string().optional(),
    potentialChallenges: z.array(z.string()).optional(),
    keyQualifications: z.array(z.string()).optional(),
    estimatedSalaryRange: z.string().optional(),
    companyValues: z.array(z.string()).optional(),
    jobResponsibilities: z.array(z.string()).optional(),
    tips: z.array(z.string()).optional(),
    company: z.string().optional(),
    position: z.string().optional(),
    location: z.string().optional(),
    salary: z.string().optional(),
    applicationUrl: z.string().optional(),
});

/**
 * Determine if a job site is likely to block scraping
 */
function isBlockedJobSite(url: string): {
    isBlocked: boolean;
    siteName?: string;
} {
    try {
        const parsedUrl = new URL(url);
        const hostname = parsedUrl.hostname.toLowerCase();

        // List of sites known to potentially block scraping
        const blockedSites = [
            { domain: "linkedin.com", name: "LinkedIn" },
            { domain: "indeed.com", name: "Indeed" },
            { domain: "glassdoor.com", name: "Glassdoor" },
            { domain: "smartrecruiters.com", name: "SmartRecruiters" },
            { domain: "lever.co", name: "Lever" },
            { domain: "greenhouse.io", name: "Greenhouse" },
            { domain: "workday.com", name: "Workday" },
            { domain: "taleo.net", name: "Taleo" },
        ];

        for (const site of blockedSites) {
            if (hostname.includes(site.domain)) {
                console.warn(
                    `[FireCrawl] Detected potentially blocked job site: ${site.name} (${hostname})`
                );
                return { isBlocked: true, siteName: site.name };
            }
        }

        return { isBlocked: false };
    } catch (error) {
        console.error(
            `[FireCrawl] URL parsing error: ${
                error instanceof Error ? error.message : String(error)
            }`
        );
        return { isBlocked: false };
    }
}

export async function extractJobDescription(jobUrl: string) {
    console.log(
        `[FireCrawl] Starting extraction for URL: ${jobUrl} in env: ${APP_ENV}`
    );

    // Track execution time for performance monitoring
    const startTime = Date.now();

    if (!FIRECRAWL_API_KEY) {
        console.error("[FireCrawl] API key is missing, extraction aborted");
        return {
            success: false,
            error: "FireCrawl API key is missing",
        };
    }

    // Check if URL is from a site known to cause issues
    const { isBlocked, siteName } = isBlockedJobSite(jobUrl);
    if (isBlocked && IS_PRODUCTION) {
        console.warn(
            `[FireCrawl] Skipping extraction for blocked site ${siteName}`
        );
        return {
            success: false,
            error: `This job posting is from ${siteName}, which may block automated extraction. Please enter details manually.`,
            blockedSite: siteName,
        };
    }

    const fireCrawlApp = new FireCrawlApp({ apiKey: FIRECRAWL_API_KEY });
    console.log(
        `[FireCrawl] Initialized FireCrawlApp, proceeding with extraction`
    );

    try {
        console.log(`[FireCrawl] Sending extraction request to FireCrawl API`);
        const result = await fireCrawlApp.extract([jobUrl], {
            prompt: "Extract key details from this job posting",
            schema: schema,
        });

        // Log execution time for performance monitoring
        const executionTime = Date.now() - startTime;
        console.log(
            `[FireCrawl] Extraction completed successfully in ${executionTime}ms`
        );

        // Log partial result data for validation
        if (result.success) {
            const extractedData = "data" in result ? result.data : result;
            console.log(
                `[FireCrawl] Extracted data: company=${
                    extractedData.company || "N/A"
                }, position=${extractedData.position || "N/A"}`
            );
        }

        return result;
    } catch (error) {
        const executionTime = Date.now() - startTime;
        console.error(
            `[FireCrawl] Extraction failed after ${executionTime}ms:`,
            error
        );

        // Enhanced error logging with status code detection
        let errorMessage = "Extraction failed";
        let statusCode = 500;

        if (error instanceof Error) {
            errorMessage = error.message;

            // Extract status code from error message if present
            const statusMatch = errorMessage.match(/status (?:code )?(\d+)/i);
            if (statusMatch && statusMatch[1]) {
                statusCode = parseInt(statusMatch[1], 10);
                console.error(
                    `[FireCrawl] Detected status code ${statusCode} in error message`
                );
            }

            // Log error details
            console.error(
                `[FireCrawl] Error type: ${error.name}, message: ${error.message}`
            );
            if (error.stack) {
                console.error(
                    `[FireCrawl] Stack trace: ${error.stack
                        .split("\n")
                        .slice(0, 3)
                        .join("\n")}`
                );
            }
        }

        return {
            success: false,
            error: errorMessage,
            statusCode,
            url: jobUrl,
            env: APP_ENV,
        };
    }
}
