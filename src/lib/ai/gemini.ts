import {
    GoogleGenerativeAI,
    GenerativeModel,
    GenerationConfig,
} from "@google/generative-ai";

// Gemini AI model configuration
// Separate GenerationConfig for clarity
type ModelConfig = {
    modelName: string;
} & Partial<GenerationConfig>; // Includes temperature, topK, topP, maxOutputTokens etc.

class GeminiSingleton {
    private static instance: GeminiSingleton;
    private genAI: GoogleGenerativeAI;
    private models: Map<string, GenerativeModel> = new Map();

    // Default configuration
    private defaultConfig: ModelConfig = {
        // Use Required for clarity
        modelName: "gemini-2.0-flash-lite", // Updated example - verify your model
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048, // Increased default slightly
        // candidateCount is another option if needed, but often default (1) is fine
    };

    // Fallback model name
    private fallbackModelName = "gemini-2.0-flash"; // Updated example - verify your model

    private constructor() {
        const apiKey = process.env.GOOGLE_AI_API_KEY;
        if (!apiKey) {
            // Log error for server-side visibility
            console.error(
                "FATAL ERROR: GOOGLE_AI_API_KEY not found in environment variables."
            );
            throw new Error(
                "GOOGLE_AI_API_KEY not found in environment variables"
            );
        }
        this.genAI = new GoogleGenerativeAI(apiKey);
        console.log("GeminiSingleton initialized."); // Log successful init
    }

    public static getInstance(): GeminiSingleton {
        if (!GeminiSingleton.instance) {
            GeminiSingleton.instance = new GeminiSingleton();
        }
        return GeminiSingleton.instance;
    }

    /**
     * Get or create a model instance based on configuration.
     * Ensures consistent generationConfig application.
     */
    public getModel(config?: Partial<ModelConfig>): GenerativeModel {
        // Combine default and provided config
        const effectiveConfig = { ...this.defaultConfig, ...config };
        const { modelName, ...generationConfig } = effectiveConfig;

        // Use a unique key for the cache based on model name + config hash/string?
        // Simple approach: cache only by model name, apply config each time
        // More complex: cache based on name + exact config (might lead to many instances)
        // Current approach caches based on name only, but uses config during getGenerativeModel
        // Let's stick to caching by name for simplicity, as config is reapplied anyway.

        const cacheKey = modelName; // Cache based on model name primarily

        if (!this.models.has(cacheKey)) {
            console.log(
                `Creating new GenerativeModel instance for: ${modelName}`
            );
            try {
                const model = this.genAI.getGenerativeModel({
                    model: modelName,
                    // Apply the specific generationConfig when getting the model
                    generationConfig: generationConfig as GenerationConfig,
                });
                this.models.set(cacheKey, model);
            } catch (error) {
                console.error(
                    `Error creating GenerativeModel for ${modelName}:`,
                    error
                );
                throw new Error(
                    `Failed to initialize GenerativeModel: ${modelName}. Error: ${error}`
                );
            }
        }

        // Although cached, we might want to ensure the generation config passed
        // is respected if it differs from the one used at creation.
        // However, the SDK structure applies config at model creation.
        // Re-creating the model or having complex caching logic might be needed
        // if dynamic config per *call* is strictly required for the *same model name*.
        // The current generateContent handles this better by passing config down.
        const modelInstance = this.models.get(cacheKey);
        if (!modelInstance) {
            // Should not happen due to the check above, but satisfies TypeScript
            throw new Error(
                `Failed to retrieve model instance for ${modelName} after creation attempt.`
            );
        }
        return modelInstance;
    }

    /**
     * Generate content with error handling and fallback, applying consistent config.
     */
    public async generateContent(
        prompt: string,
        config?: Partial<ModelConfig>
    ): Promise<string> {
        const primaryConfig = { ...this.defaultConfig, ...config };
        let primaryError: unknown | null = null;

        // --- Attempt 1: Primary Model ---
        try {
            console.log(
                `Attempting generation with primary model: ${primaryConfig.modelName}`
            );
            const model = this.getModel(primaryConfig); // Get model ensures it's created with right base config
            const result = await model.generateContent(prompt); // SDK might allow overriding config here in future? Check docs.
            return result.response.text();
        } catch (error) {
            console.warn(
                `Error using primary model (${primaryConfig.modelName}): ${error}. Trying fallback.`
            );
            primaryError = error; // Store primary error
        }

        // --- Attempt 2: Fallback Model ---
        // Use the same generation parameters (temp, topP etc.) but change the model name
        const fallbackConfig: ModelConfig = {
            ...primaryConfig, // Start with primary config (includes temperature etc.)
            modelName: this.fallbackModelName, // Override only the model name
        };

        // Avoid infinite loop if primary and fallback are the same
        if (primaryConfig.modelName === fallbackConfig.modelName) {
            console.error(
                "Primary and fallback models are the same. Cannot fallback."
            );
            // Re-throw the original primary error
            throw new Error(
                `AI generation failed with model ${primaryConfig.modelName}: ${primaryError}`
            );
        }

        try {
            console.log(
                `Attempting generation with fallback model: ${fallbackConfig.modelName}`
            );
            const fallbackModel = this.getModel(fallbackConfig);
            const result = await fallbackModel.generateContent(prompt);
            console.log(
                `Successfully generated content with fallback model: ${fallbackConfig.modelName}`
            );
            return result.response.text();
        } catch (fallbackError) {
            console.error(
                `Error with fallback model (${fallbackConfig.modelName}): ${fallbackError}`
            );
            // Throw a more informative error including both attempts
            throw new Error(
                `AI generation failed. Primary (${primaryConfig.modelName}) error: [${primaryError}], Fallback (${fallbackConfig.modelName}) error: [${fallbackError}]`
            );
        }
    }
}

// Export a single instance
export const gemini = GeminiSingleton.getInstance();

// --- Usage Example (remains the same) ---
// import { gemini } from '@/lib/ai/gemini';
//
// async function someFunction() {
//   try {
//     // Using default model (e.g., gemini-1.5-flash-latest with fallback to gemini-pro)
//     const response1 = await gemini.generateContent("Write a short poem about clouds.");
//     console.log("Response 1:", response1);
//
//     // Using a specific model configuration (overrides default model and temp)
//     const response2 = await gemini.generateContent("Translate 'hello world' to French.", {
//       modelName: "gemini-pro", // Specify model directly
//       temperature: 0.2
//     });
//     console.log("Response 2:", response2);
//
//   } catch (error) {
//      console.error("An error occurred during AI generation:", error);
//   }
// }
//
// someFunction();
