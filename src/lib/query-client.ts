import { QueryClient } from "@tanstack/react-query";

const queryClientConfig = {
    defaultOptions: {
        queries: {
            staleTime: 5 * 1000, // 5 seconds
            gcTime: 10 * 60 * 1000, // 10 minutes
            retry: 1,
            refetchOnWindowFocus: true,
            refetchOnMount: true,
            refetchOnReconnect: true,
        },
        mutations: {
            retry: 1,
        },
    },
};

export const queryClient = new QueryClient(queryClientConfig);
