import { auth, getAuth } from "@clerk/nextjs/server";
import { NextRequest } from "next/server";
import { headers } from "next/headers";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export function getServerAuth() {
    return auth();
}

export function getApiAuth(req: NextRequest) {
    return getAuth(req);
}

export function getPageAuth() {
    return auth();
}

export async function getOrCreateUser(
    userId: string,
    userEmail: string,
    firstName?: string,
    lastName?: string,
    profileImage?: string
) {
    if (!userId || !userEmail) {
        throw new Error("User ID and email are required");
    }

    try {
        // Check if user exists
        const existingUser = await db
            .select()
            .from(users)
            .where(eq(users.id, userId))
            .limit(1);

        if (existingUser.length > 0) {
            console.log("User found in database:", existingUser[0]);
            return existingUser[0];
        }

        // Create new user
        console.log("Creating new user:", {
            userId,
            userEmail,
            firstName,
            lastName,
        });
        const [newUser] = await db
            .insert(users)
            .values({
                id: userId,
                email: userEmail,
                firstName: firstName || null,
                lastName: lastName || null,
                profileImage: profileImage || null,
            })
            .returning();

        console.log("User created successfully:", newUser);
        return newUser;
    } catch (error) {
        console.error("Error getting or creating user:", error);
        throw error;
    }
}
