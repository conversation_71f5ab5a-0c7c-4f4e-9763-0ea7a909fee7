import { nanoid } from "nanoid";
import { db } from "@/lib/db";
import { shortenedUrls } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

// Length of short URL code
const SHORT_CODE_LENGTH = 7;

// Validation schemas
const UrlSchema = z
    .string()
    .trim()
    .url("Invalid URL format")
    .max(2048, "URL is too long");

const ShortCodeSchema = z
    .string()
    .trim()
    .min(1, "Short code cannot be empty")
    .max(10, "Short code is too long")
    .regex(/^[a-zA-Z0-9_-]+$/, "Short code contains invalid characters");

/**
 * Creates a shortened URL and stores it in the database
 */
export async function createShortUrl(
    originalUrl: string,
    userId?: string,
    applicationId?: number,
    expirationDays: number = 30
) {
    try {
        // Validate URL
        const validatedUrl = UrlSchema.parse(originalUrl);

        // Generate a unique short code
        const shortCode = nanoid(SHORT_CODE_LENGTH);

        // Calculate expiration date
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + expirationDays);

        // Store in database
        const result = await db
            .insert(shortenedUrls)
            .values({
                shortCode,
                originalUrl: validatedUrl,
                userId: userId || null,
                applicationId: applicationId || null,
                clickCount: 0,
                expiresAt,
            })
            .returning();

        return result[0];
    } catch (error) {
        // Check if this is a validation error
        if (error instanceof z.ZodError) {
            throw new Error(
                `URL validation failed: ${error.errors[0].message}`
            );
        }

        console.error("Error creating short URL:", error);
        throw new Error("Failed to create shortened URL");
    }
}

/**
 * Retrieves the original URL from a short code
 */
export async function getOriginalUrl(shortCode: string) {
    try {
        // Validate short code
        const validatedCode = ShortCodeSchema.safeParse(shortCode);

        if (!validatedCode.success) {
            console.error("Invalid short code format:", shortCode);
            return null;
        }

        // Find the URL record
        const urls = await db
            .select()
            .from(shortenedUrls)
            .where(eq(shortenedUrls.shortCode, validatedCode.data))
            .limit(1);

        if (urls.length === 0) {
            return null;
        }

        const url = urls[0];

        // Check if expired
        if (url.expiresAt && new Date(url.expiresAt) < new Date()) {
            // Delete expired URL
            await db.delete(shortenedUrls).where(eq(shortenedUrls.id, url.id));
            return null;
        }

        // Increment click count
        await db
            .update(shortenedUrls)
            .set({ clickCount: (url.clickCount || 0) + 1 })
            .where(eq(shortenedUrls.id, url.id));

        return url.originalUrl;
    } catch (error) {
        console.error("Error retrieving original URL:", error);
        return null;
    }
}

/**
 * Builds the full shortened URL including domain
 */
export function buildShortUrl(shortCode: string) {
    try {
        // Validate short code
        const validatedCode = ShortCodeSchema.parse(shortCode);

        const baseUrl =
            process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
        return `${baseUrl}/s/${validatedCode}`;
    } catch (error) {
        console.error("Error building short URL:", error);
        throw new Error("Invalid short code format");
    }
}
