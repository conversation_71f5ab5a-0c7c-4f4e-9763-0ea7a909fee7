import { Metadata, Viewport } from "next";

const defaultMetadata: Metadata = {
    metadataBase: new URL("https://hirerizz.xyz"),
    title: {
        default: "JobCraft - Craft Your Career",
        template: "%s | JobCraft",
    },
    description:
        "Create personalized job applications with AI assistance. Generate tailored resumes, cover letters, and outreach messages that stand out.",
    keywords: [
        "job application",
        "AI assistant",
        "resume builder",
        "cover letter generator",
        "job search",
        "career tools",
        "interview preparation",
        "LinkedIn message generator",
        "job hunting",
        "career development",
    ],
    authors: [{ name: "JobCraft Team" }],
    creator: "JobCraft",
    publisher: "JobCraft",
    formatDetection: {
        email: false,
        address: false,
        telephone: false,
    },
    openGraph: {
        type: "website",
        siteName: "JobCraft",
        title: "JobCraft - Craft Your Career",
        description:
            "Create personalized job applications with AI assistance. Generate tailored resumes, cover letters, and outreach messages that stand out.",
        images: [
            {
                url: "/og-image.png",
                width: 1200,
                height: 630,
                alt: "JobCraft - Craft Your Career",
            },
        ],
    },
    twitter: {
        card: "summary_large_image",
        title: "JobCraft - Craft Your Career",
        description:
            "Create personalized job applications with AI assistance. Generate tailored resumes, cover letters, and outreach messages that stand out.",
        images: ["/twitter-image.png"],
        creator: "@jobcraft",
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            "max-video-preview": -1,
            "max-image-preview": "large",
            "max-snippet": -1,
        },
    },
    icons: {
        icon: "/favicon.ico",
        shortcut: "/favicon-16x16.png",
        apple: "/apple-touch-icon.png",
        other: [
            {
                rel: "icon",
                type: "image/png",
                sizes: "32x32",
                url: "/favicon-32x32.png",
            },
            {
                rel: "mask-icon",
                url: "/safari-pinned-tab.svg",
                color: "#5e4b8b",
            },
        ],
    },
    manifest: "/site.webmanifest",
    verification: {
        google: "your-google-site-verification",
        yandex: "your-yandex-verification",
        yahoo: "your-yahoo-verification",
        other: {
            me: ["your-personal-site"],
        },
    },
    alternates: {
        canonical: "https://hirerizz.xyz",
        languages: {
            "en-US": "https://hirerizz.xyz/en-US",
        },
    },
    category: "technology",
};

// Separate viewport configuration
export const defaultViewport: Viewport = {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
    themeColor: "#5e4b8b",
};

export default defaultMetadata;
