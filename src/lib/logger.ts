type LogLevel = "DEBUG" | "INFO" | "WARN" | "ERROR";

interface LogContext {
    requestId?: string;
    userId?: string;
    operation?: string;
    duration?: number;
    error?: any;
    metadata?: Record<string, any>;
}

class Logger {
    private logLevel: LogLevel;

    constructor() {
        // Set log level based on environment
        this.logLevel = (process.env.LOG_LEVEL as LogLevel) || "INFO";
    }

    private shouldLog(level: LogLevel): boolean {
        const levels = ["DEBUG", "INFO", "WARN", "ERROR"];
        return levels.indexOf(level) >= levels.indexOf(this.logLevel);
    }

    private formatMessage(
        level: LogLevel,
        operation: string,
        message: string,
        context?: LogContext
    ): string {
        const timestamp = new Date().toISOString();
        const requestId = context?.requestId || "unknown";
        return `[${timestamp}] [${level}] [${operation}:${requestId}] ${message}`;
    }

    debug(operation: string, message: string, context?: LogContext) {
        if (this.shouldLog("DEBUG")) {
            console.log(
                this.formatMessage("DEBUG", operation, message, context),
                context?.metadata || ""
            );
        }
    }

    info(operation: string, message: string, context?: LogContext) {
        if (this.shouldLog("INFO")) {
            console.log(
                this.formatMessage("INFO", operation, message, context),
                context?.metadata || ""
            );
        }
    }

    warn(operation: string, message: string, context?: LogContext) {
        if (this.shouldLog("WARN")) {
            console.warn(
                this.formatMessage("WARN", operation, message, context),
                context?.metadata || ""
            );
        }
    }

    error(operation: string, message: string, context?: LogContext) {
        if (this.shouldLog("ERROR")) {
            console.error(
                this.formatMessage("ERROR", operation, message, context),
                {
                    metadata: context?.metadata,
                    error: context?.error,
                    stack: context?.error?.stack,
                }
            );
        }
    }

    // Specialized methods for delete operations
    deleteStart(
        operation: string,
        requestId: string,
        metadata: Record<string, any>
    ) {
        this.info(
            operation,
            `=== Starting ${operation.toLowerCase()} deletion process ===`,
            {
                requestId,
                metadata,
            }
        );
    }

    deleteSuccess(
        operation: string,
        requestId: string,
        duration: number,
        metadata: Record<string, any>
    ) {
        this.info(
            operation,
            `=== ${operation.toLowerCase()} deletion completed successfully ===`,
            {
                requestId,
                duration,
                metadata,
            }
        );
    }

    deleteError(
        operation: string,
        requestId: string,
        duration: number,
        error: any,
        metadata?: Record<string, any>
    ) {
        this.error(
            operation,
            `=== ${operation.toLowerCase()} deletion failed ===`,
            {
                requestId,
                duration,
                error,
                metadata,
            }
        );
    }

    timing(
        operation: string,
        requestId: string,
        action: string,
        duration: number
    ) {
        this.debug(operation, `${action} completed in ${duration}ms`, {
            requestId,
            metadata: { action, duration },
        });
    }
}

// Export singleton instance
export const logger = new Logger();

// Export helper functions for delete operations
export const createRequestId = (prefix: string = "req") =>
    `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

export const measureTime = <T>(
    fn: () => Promise<T>
): Promise<{ result: T; duration: number }> => {
    return new Promise(async (resolve, reject) => {
        const start = Date.now();
        try {
            const result = await fn();
            const duration = Date.now() - start;
            resolve({ result, duration });
        } catch (error) {
            const duration = Date.now() - start;
            reject({ error, duration });
        }
    });
};

// Export types for use in other files
export type { LogLevel, LogContext };
