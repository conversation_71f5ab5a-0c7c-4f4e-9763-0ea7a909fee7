import "./globals.css";
import { Inter } from "next/font/google";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { UploadThingProvider } from "@/components/uploadthing-provider";
import { PageTransition } from "@/components/page-transition";
import { Providers } from "@/components/providers";
import { cn } from "@/lib/utils";
import AppLoadingWrapper from "@/components/app-loading-wrapper";
import defaultMetadata, { defaultViewport } from "@/lib/metadata";
import Script from "next/script";

// Increase max listeners for process events
process.setMaxListeners(15);

const inter = Inter({
    subsets: ["latin"],
    variable: "--font-inter",
    display: "swap", // Optimize font loading
});

export const metadata = defaultMetadata;
export const viewport = defaultViewport;

// JSON-LD structured data for better SEO
const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "JobCraft",
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web",
    offers: {
        "@type": "Offer",
        price: "0",
        priceCurrency: "USD",
    },
    description:
        "AI-powered job application assistant that helps create personalized resumes, cover letters, and outreach messages.",
    aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: "4.8",
        ratingCount: "150",
    },
    featureList: [
        "AI-powered resume parsing",
        "Cover letter generation",
        "LinkedIn message creation",
        "Interview preparation",
        "Application tracking",
    ],
};

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <ClerkProvider
            appearance={{
                elements: {
                    formButtonPrimary:
                        "bg-primary hover:bg-primary/90 text-sm normal-case",
                    footerActionLink: "text-secondary hover:text-secondary/90",
                    card: "bg-card border border-border rounded-xl shadow-xl",
                },
                variables: {
                    colorPrimary: "#5e4b8b",
                    colorTextOnPrimaryBackground: "white",
                    fontFamily: "Inter, sans-serif",
                },
            }}
            publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
            signInUrl="/sign-in"
            signUpUrl="/sign-up"
            afterSignInUrl="/dashboard"
            afterSignUpUrl="/dashboard"
        >
            <html
                lang="en"
                suppressHydrationWarning
                className={cn(inter.variable)}
            >
                <head>
                    {/* Add structured data */}
                    <script
                        type="application/ld+json"
                        dangerouslySetInnerHTML={{
                            __html: JSON.stringify(structuredData),
                        }}
                    />
                    {/* Preconnect to important third-party domains */}
                    <link
                        rel="preconnect"
                        href="https://fonts.googleapis.com"
                    />
                    <link
                        rel="preconnect"
                        href="https://fonts.gstatic.com"
                        crossOrigin="anonymous"
                    />
                    {/* Add preload for critical resources */}
                    <link rel="preload" href="/grid.svg" as="image" />
                </head>
                <body
                    className={cn(
                        "min-h-screen bg-background antialiased",
                        inter.className
                    )}
                >
                    <UploadThingProvider>
                        <Providers>
                            <AppLoadingWrapper />
                            <PageTransition>{children}</PageTransition>
                        </Providers>
                    </UploadThingProvider>
                    {/* Google Analytics */}
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
                        strategy="afterInteractive"
                    />
                    <Script id="google-analytics" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
                        `}
                    </Script>
                </body>
            </html>
        </ClerkProvider>
    );
}
