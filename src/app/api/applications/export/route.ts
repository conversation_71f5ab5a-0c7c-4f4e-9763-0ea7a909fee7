import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { jobApplications } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET(req: NextRequest) {
    const { userId } = getAuth(req);
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    try {
        // Fetch all applications for the user
        const applications = await db
            .select({
                id: jobApplications.id,
                company: jobApplications.company,
                position: jobApplications.position,
                location: jobApplications.location,
                salary: jobApplications.salary,
                applicationUrl: jobApplications.applicationUrl,
                contactName: jobApplications.contactName,
                contactEmail: jobApplications.contactEmail,
                contactPhone: jobApplications.contactPhone,
                status: jobApplications.status,
                notes: jobApplications.notes,
                appliedDate: jobApplications.appliedDate,
                createdAt: jobApplications.createdAt,
                updatedAt: jobApplications.updatedAt,
                jobLink: jobApplications.jobLink,
            })
            .from(jobApplications)
            .where(eq(jobApplications.userId, userId))
            .orderBy(jobApplications.createdAt);

        return NextResponse.json({ applications });
    } catch (error) {
        console.error("Error exporting applications:", error);
        return NextResponse.json(
            { error: "Failed to export applications" },
            { status: 500 }
        );
    }
}
