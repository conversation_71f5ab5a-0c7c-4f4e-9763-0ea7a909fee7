import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { jobApplications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

// Make the route dynamic to ensure params are properly handled
export const dynamic = "force-dynamic";

export async function GET(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        if (!id) {
            return NextResponse.json(
                { error: "Missing application ID" },
                { status: 400 }
            );
        }

        const applicationId = parseInt(id);

        if (isNaN(applicationId)) {
            return NextResponse.json(
                { error: "Invalid application ID" },
                { status: 400 }
            );
        }

        const [application] = await db
            .select()
            .from(jobApplications)
            .where(eq(jobApplications.id, applicationId))
            .limit(1);

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        if (application.userId !== userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        return NextResponse.json(application);
    } catch (error) {
        console.error("Error fetching application:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function PUT(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        if (!id) {
            return NextResponse.json(
                { error: "Missing application ID" },
                { status: 400 }
            );
        }

        const applicationId = parseInt(id);
        if (isNaN(applicationId)) {
            return NextResponse.json(
                { error: "Invalid application ID" },
                { status: 400 }
            );
        }

        // Check if application exists and belongs to user
        const [existingApplication] = await db
            .select()
            .from(jobApplications)
            .where(
                and(
                    eq(jobApplications.id, applicationId),
                    eq(jobApplications.userId, userId)
                )
            );

        if (!existingApplication) {
            return NextResponse.json(
                { error: "Application not found or not authorized" },
                { status: 404 }
            );
        }

        const body = await req.json();
        const {
            company,
            position,
            jobDescription,
            location,
            salary,
            applicationUrl,
            contactName,
            contactEmail,
            contactPhone,
            status,
            notes,
            resumeId,
        } = body;

        if (!company || !position) {
            return NextResponse.json(
                { error: "Company and position are required" },
                { status: 400 }
            );
        }

        // Update the application
        const [updatedApplication] = await db
            .update(jobApplications)
            .set({
                company,
                position,
                jobDescription: jobDescription || null,
                location: location || null,
                salary: salary || null,
                applicationUrl: applicationUrl || null,
                contactName: contactName || null,
                contactEmail: contactEmail || null,
                contactPhone: contactPhone || null,
                status: status || existingApplication.status,
                notes: notes || null,
                resumeId: resumeId || null,
                appliedDate:
                    status === "applied" &&
                    existingApplication.status !== "applied"
                        ? new Date()
                        : existingApplication.appliedDate,
                updatedAt: new Date(),
            })
            .where(
                and(
                    eq(jobApplications.id, applicationId),
                    eq(jobApplications.userId, userId)
                )
            )
            .returning();

        return NextResponse.json(updatedApplication);
    } catch (error) {
        console.error("Error updating application:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const startTime = Date.now();
    const requestId = `app_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

    console.log(
        `[DELETE_APPLICATION:${requestId}] === Starting application deletion process ===`
    );
    console.log(
        `[DELETE_APPLICATION:${requestId}] Request method: ${req.method}`
    );
    console.log(`[DELETE_APPLICATION:${requestId}] Request URL: ${req.url}`);
    console.log(
        `[DELETE_APPLICATION:${requestId}] User-Agent: ${req.headers.get(
            "user-agent"
        )}`
    );
    console.log(
        `[DELETE_APPLICATION:${requestId}] Referer: ${req.headers.get(
            "referer"
        )}`
    );

    try {
        const { userId } = getAuth(req);
        if (!userId) {
            console.warn(
                `[DELETE_APPLICATION:${requestId}] Unauthorized access attempt - no userId found`
            );
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        console.log(
            `[DELETE_APPLICATION:${requestId}] Authenticated user: ${userId}`
        );

        const { id } = await params;
        console.log(
            `[DELETE_APPLICATION:${requestId}] Received application ID: ${id}`
        );

        if (!id) {
            console.error(
                `[DELETE_APPLICATION:${requestId}] Missing application ID parameter`
            );
            return NextResponse.json(
                { error: "Missing application ID" },
                { status: 400 }
            );
        }

        const applicationId = parseInt(id);
        console.log(
            `[DELETE_APPLICATION:${requestId}] Parsed application ID: ${applicationId}`
        );

        if (isNaN(applicationId)) {
            console.error(
                `[DELETE_APPLICATION:${requestId}] Invalid application ID provided: ${id}`
            );
            return NextResponse.json(
                { error: "Invalid application ID" },
                { status: 400 }
            );
        }

        console.log(
            `[DELETE_APPLICATION:${requestId}] Starting database deletion operation...`
        );
        const deleteStart = Date.now();

        // Delete the application
        const deleteResult = await db
            .delete(jobApplications)
            .where(
                and(
                    eq(jobApplications.id, applicationId),
                    eq(jobApplications.userId, userId)
                )
            );

        const deleteTime = Date.now() - deleteStart;
        const totalRequestTime = Date.now() - startTime;

        console.log(
            `[DELETE_APPLICATION:${requestId}] Database deletion completed in ${deleteTime}ms`
        );
        console.log(
            `[DELETE_APPLICATION:${requestId}] Delete operation result:`,
            deleteResult
        );
        console.log(
            `[DELETE_APPLICATION:${requestId}] Total request processing time: ${totalRequestTime}ms`
        );

        const response = {
            success: true,
            metadata: {
                requestId,
                applicationId,
                userId,
                processingTimeMs: totalRequestTime,
                databaseDeleteTimeMs: deleteTime,
                deleteResult,
            },
        };

        console.log(
            `[DELETE_APPLICATION:${requestId}] === Application deletion completed successfully ===`,
            {
                applicationId,
                userId,
                totalTime: totalRequestTime,
                deleteTime,
            }
        );

        return NextResponse.json(response);
    } catch (error) {
        const totalRequestTime = Date.now() - startTime;
        console.error(
            `[DELETE_APPLICATION:${requestId}] === Application deletion failed after ${totalRequestTime}ms ===`,
            {
                error,
                errorMessage:
                    error instanceof Error ? error.message : "Unknown error",
                errorStack: error instanceof Error ? error.stack : undefined,
                errorName: error instanceof Error ? error.name : "Unknown",
                userId: req.headers.get("authorization")
                    ? "present"
                    : "missing",
                requestUrl: req.url,
            }
        );

        return NextResponse.json(
            {
                error: "Internal server error",
                metadata: {
                    requestId,
                    processingTimeMs: totalRequestTime,
                },
            },
            { status: 500 }
        );
    }
}
