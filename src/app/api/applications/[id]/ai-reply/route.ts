import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { generatedDocuments, jobApplications, resumes } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { ChatMessage } from "@/lib/db/schema";
import { gemini } from "@/lib/ai/gemini";

export async function POST(
    request: Request,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        // Verify authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }
        const { id } = await params;
        const applicationId = parseInt(id);

        // Get application details
        const application = await db.query.jobApplications.findFirst({
            where: and(
                eq(jobApplications.id, applicationId),
                eq(jobApplications.userId, userId)
            ),
        });

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        // Get document for the application
        const document = await db.query.generatedDocuments.findFirst({
            where: eq(generatedDocuments.applicationId, applicationId),
        });

        if (!document) {
            return NextResponse.json(
                { error: "Document not found" },
                { status: 404 }
            );
        }

        // Get chat history
        const chatHistory = document.chat || [];

        if (chatHistory.length === 0) {
            return NextResponse.json(
                { error: "No messages to respond to" },
                { status: 400 }
            );
        }

        // Get last message
        const lastMessage = chatHistory[chatHistory.length - 1];

        if (lastMessage.speaker !== "user") {
            return NextResponse.json(
                { error: "Last message is not from user" },
                { status: 400 }
            );
        }

        // Get resume if available
        let resumeContent = "";
        if (application.resumeId) {
            const resume = await db.query.resumes.findFirst({
                where: eq(resumes.id, application.resumeId),
            });

            if (resume?.parsedContent) {
                resumeContent =
                    typeof resume.parsedContent === "string"
                        ? resume.parsedContent
                        : JSON.stringify(resume.parsedContent);
            }
        }

        // Create context for AI
        let context = "";
        if (application.company) context += `Company: ${application.company}\n`;
        if (application.position)
            context += `Position: ${application.position}\n`;
        if (application.jobDescription)
            context += `Job Description: ${application.jobDescription}\n`;
        if (resumeContent) context += `Resume Summary: ${resumeContent}\n`;

        // Generate prompt for AI
        const prompt = `
You are an AI assistant helping a job applicant prepare answers for interview or application questions.

CONTEXT ABOUT THE JOB:
${context}

USER QUESTION: ${lastMessage.message}

Please provide a well-structured, professional response that:
1. Is personalized based on the job and company
2. Highlights relevant qualifications and experience
3. Shows enthusiasm and cultural fit
4. Is concise yet comprehensive (max 3-4 paragraphs)
5. Uses professional, confident language

Your response should be ready to use or easily adaptable for the application.
`;

        // Generate content using the Gemini singleton
        const answer = await gemini.generateContent(prompt);

        // Create AI message
        const aiMessage: ChatMessage = {
            speaker: "bot",
            time: new Date(),
            message: answer,
        };

        // Update chat history
        const updatedChatHistory = [...chatHistory, aiMessage];

        // Save to database
        await db
            .update(generatedDocuments)
            .set({ chat: updatedChatHistory })
            .where(eq(generatedDocuments.id, document.id));

        return NextResponse.json(
            {
                success: true,
                message: "AI reply generated successfully",
                reply: aiMessage,
                chat: updatedChatHistory,
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error generating AI reply:", error);
        return NextResponse.json(
            { error: "Failed to generate AI reply" },
            { status: 500 }
        );
    }
}
