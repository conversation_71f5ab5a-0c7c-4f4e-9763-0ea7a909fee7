import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes, jobApplications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const { userId } = getAuth(req);
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    try {
        const { id } = await params;
        if (!id) {
            return NextResponse.json(
                { error: "Missing application ID" },
                { status: 400 }
            );
        }

        const applicationId = parseInt(id);
        if (isNaN(applicationId)) {
            return NextResponse.json(
                { error: "Invalid application ID" },
                { status: 400 }
            );
        }

        // Verify the application exists and belongs to this user
        const application = await db.query.jobApplications.findFirst({
            where: and(
                eq(jobApplications.id, applicationId),
                eq(jobApplications.userId, userId)
            ),
        });

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        let currentResumeId = application.resumeId;

        // Check if the current resume still exists
        if (currentResumeId) {
            const [currentResume] = await db
                .select()
                .from(resumes)
                .where(
                    and(
                        eq(resumes.id, currentResumeId),
                        eq(resumes.userId, userId)
                    )
                )
                .limit(1);

            // If current resume doesn't exist, try to set default resume
            if (!currentResume) {
                console.log(
                    `Resume ${currentResumeId} not found, checking for default resume`
                );

                const [defaultResume] = await db
                    .select()
                    .from(resumes)
                    .where(
                        and(
                            eq(resumes.userId, userId),
                            eq(resumes.isDefault, true)
                        )
                    )
                    .limit(1);

                if (defaultResume) {
                    console.log(
                        `Found default resume ${defaultResume.id}, updating application`
                    );

                    // Update the application to use the default resume
                    await db
                        .update(jobApplications)
                        .set({
                            resumeId: defaultResume.id,
                            updatedAt: new Date(),
                        })
                        .where(eq(jobApplications.id, applicationId));

                    currentResumeId = defaultResume.id;
                } else {
                    console.log(
                        "No default resume found, clearing resumeId from application"
                    );
                    // Clear the resumeId from the application
                    await db
                        .update(jobApplications)
                        .set({
                            resumeId: null,
                            updatedAt: new Date(),
                        })
                        .where(eq(jobApplications.id, applicationId));

                    currentResumeId = null;
                }
            }
        } else {
            // No resumeId set, try to set default resume
            const [defaultResume] = await db
                .select()
                .from(resumes)
                .where(
                    and(eq(resumes.userId, userId), eq(resumes.isDefault, true))
                )
                .limit(1);

            if (defaultResume) {
                console.log(
                    `No resume set, using default resume ${defaultResume.id}`
                );

                // Update the application to use the default resume
                await db
                    .update(jobApplications)
                    .set({
                        resumeId: defaultResume.id,
                        updatedAt: new Date(),
                    })
                    .where(eq(jobApplications.id, applicationId));

                currentResumeId = defaultResume.id;
            }
        }

        return NextResponse.json({
            currentResumeId,
        });
    } catch (error) {
        console.error("Error fetching resumes for application:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function PUT(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const { userId } = getAuth(req);
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    try {
        const { id } = await params;
        if (!id) {
            return NextResponse.json(
                { error: "Missing application ID" },
                { status: 400 }
            );
        }

        const applicationId = parseInt(id);
        if (isNaN(applicationId)) {
            return NextResponse.json(
                { error: "Invalid application ID" },
                { status: 400 }
            );
        }

        // Get the request body
        const body = await req.json();
        const { resumeId } = body;

        if (!resumeId && resumeId !== null) {
            return NextResponse.json(
                { error: "Resume ID is required" },
                { status: 400 }
            );
        }

        // If resumeId is provided, verify it exists and belongs to the user
        if (resumeId) {
            const [resume] = await db
                .select()
                .from(resumes)
                .where(
                    and(eq(resumes.id, resumeId), eq(resumes.userId, userId))
                )
                .limit(1);

            if (!resume) {
                return NextResponse.json(
                    { error: "Resume not found or not authorized" },
                    { status: 404 }
                );
            }
        }

        // Verify the application exists and belongs to this user
        const [existingApplication] = await db
            .select()
            .from(jobApplications)
            .where(
                and(
                    eq(jobApplications.id, applicationId),
                    eq(jobApplications.userId, userId)
                )
            )
            .limit(1);

        if (!existingApplication) {
            return NextResponse.json(
                { error: "Application not found or not authorized" },
                { status: 404 }
            );
        }

        // Update the application with the new resume ID
        const [updatedApplication] = await db
            .update(jobApplications)
            .set({
                resumeId: resumeId,
                updatedAt: new Date(),
            })
            .where(
                and(
                    eq(jobApplications.id, applicationId),
                    eq(jobApplications.userId, userId)
                )
            )
            .returning();

        return NextResponse.json(updatedApplication);
    } catch (error) {
        console.error("Error updating application resume:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
