import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { generatedDocuments } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { ChatMessage, ChatMessageSchema } from "@/lib/db/schema";
import { z } from "zod";

// GET: Fetch chat messages for an application
export async function GET(
    request: Request,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        // Verify authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        const applicationId = parseInt(id);

        // Get generated documents for the application
        const document = await db.query.generatedDocuments.findFirst({
            where: eq(generatedDocuments.applicationId, applicationId),
        });

        if (!document) {
            return NextResponse.json(
                { error: "Document not found" },
                { status: 404 }
            );
        }

        // Return chat messages
        return NextResponse.json(
            { chat: document.chat || [] },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error fetching chat:", error);
        return NextResponse.json(
            { error: "Failed to fetch chat" },
            { status: 500 }
        );
    }
}

// Schema for message request validation
const MessageRequestSchema = z.object({
    message: z.string().min(1),
});

// POST: Add a new user message and generate AI response
export async function POST(
    request: Request,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        // Verify authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        const applicationId = parseInt(id);

        // Parse and validate request body
        const body = await request.json();
        const validatedData = MessageRequestSchema.safeParse(body);

        if (!validatedData.success) {
            return NextResponse.json(
                { error: "Invalid message" },
                { status: 400 }
            );
        }

        const { message } = validatedData.data;

        // Get document for the application
        const document = await db.query.generatedDocuments.findFirst({
            where: eq(generatedDocuments.applicationId, applicationId),
        });

        if (!document) {
            return NextResponse.json(
                { error: "Document not found" },
                { status: 404 }
            );
        }

        // Create new user message
        const userMessage: ChatMessage = {
            speaker: "user",
            time: new Date(),
            message,
        };

        // Add to chat history
        const chatHistory = [...(document.chat || []), userMessage];

        // Update document with new chat history
        await db
            .update(generatedDocuments)
            .set({ chat: chatHistory })
            .where(eq(generatedDocuments.id, document.id));

        // Return updated chat history
        return NextResponse.json(
            {
                success: true,
                message: "Message added successfully",
                chat: chatHistory,
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error adding message:", error);
        return NextResponse.json(
            { error: "Failed to add message" },
            { status: 500 }
        );
    }
}
