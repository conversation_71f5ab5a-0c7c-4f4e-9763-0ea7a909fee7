import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { generatedDocuments, jobApplications, resumes } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { gemini } from "@/lib/ai/gemini";
import { z } from "zod";

// Validation schema for content updates
const updateContentSchema = z.object({
    field: z.enum(["coverLetter", "linkedinMessage", "coldEmail"]),
    value: z.string().max(50000, "Content is too long"),
});

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const contentType = request.nextUrl.searchParams.get("type") || "all";
        const { id } = await params;

        const application = await db.query.jobApplications.findFirst({
            where: and(
                eq(jobApplications.id, parseInt(id)),
                eq(jobApplications.userId, userId)
            ),
        });

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        const applicationId = parseInt(id);
        const document = await db.query.generatedDocuments.findFirst({
            where: eq(generatedDocuments.applicationId, applicationId),
        });

        if (!document) {
            return NextResponse.json({
                coverLetter: null,
                linkedinMessage: null,
                coldEmail: null,
            });
        }

        if (contentType === "all") {
            return NextResponse.json({
                coverLetter: document.coverLetter,
                linkedinMessage: document.linkedinMessage,
                coldEmail: document.coldEmail,
            });
        } else if (contentType === "cover") {
            return NextResponse.json({
                coverLetter: document.coverLetter,
            });
        } else if (contentType === "linkedin") {
            return NextResponse.json({
                linkedinMessage: document.linkedinMessage,
            });
        } else if (contentType === "email") {
            return NextResponse.json({
                coldEmail: document.coldEmail,
            });
        } else {
            return NextResponse.json(
                { error: "Invalid content type" },
                { status: 400 }
            );
        }
    } catch (error) {
        console.error("Error fetching content:", error);
        return NextResponse.json(
            { error: "Failed to fetch content" },
            { status: 500 }
        );
    }
}

export async function POST(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        const applicationId = parseInt(id);

        const application = await db.query.jobApplications.findFirst({
            where: and(
                eq(jobApplications.id, applicationId),
                eq(jobApplications.userId, userId)
            ),
        });

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        const { type, forceRegenerate = false } = await request.json();

        if (!type) {
            return NextResponse.json(
                { error: "Content type is required" },
                { status: 400 }
            );
        }

        if (!["cover", "linkedin", "email"].includes(type)) {
            return NextResponse.json(
                { error: "Invalid content type" },
                { status: 400 }
            );
        }

        let document = await db.query.generatedDocuments.findFirst({
            where: eq(generatedDocuments.applicationId, applicationId),
        });

        if (
            !forceRegenerate &&
            document &&
            ((type === "cover" && document.coverLetter) ||
                (type === "linkedin" && document.linkedinMessage) ||
                (type === "email" && document.coldEmail))
        ) {
            return NextResponse.json({
                message: "Content already exists",
                [type === "cover"
                    ? "coverLetter"
                    : type === "linkedin"
                    ? "linkedinMessage"
                    : "coldEmail"]:
                    type === "cover"
                        ? document.coverLetter
                        : type === "linkedin"
                        ? document.linkedinMessage
                        : document.coldEmail,
            });
        }

        // Get resume content with default fallback
        let resumeData = null;
        const resumeId = application.resumeId;

        if (resumeId) {
            console.log(`Fetching resume data for resumeId: ${resumeId}`);
            try {
                const [resume] = await db
                    .select()
                    .from(resumes)
                    .where(
                        and(
                            eq(resumes.id, resumeId),
                            eq(resumes.userId, userId)
                        )
                    )
                    .limit(1);

                if (!resume) {
                    console.log(
                        `Resume ${resumeId} not found, checking for default resume`
                    );

                    const [defaultResume] = await db
                        .select()
                        .from(resumes)
                        .where(
                            and(
                                eq(resumes.userId, userId),
                                eq(resumes.isDefault, true)
                            )
                        )
                        .limit(1);

                    if (defaultResume) {
                        console.log(
                            `Found default resume ${defaultResume.id}, updating application`
                        );

                        await db
                            .update(jobApplications)
                            .set({
                                resumeId: defaultResume.id,
                                updatedAt: new Date(),
                            })
                            .where(eq(jobApplications.id, applicationId));

                        if (
                            defaultResume.parsedContent &&
                            Object.keys(defaultResume.parsedContent).length > 0
                        ) {
                            console.log(
                                `Using parsed content from default resume ${defaultResume.id}`
                            );
                            resumeData = defaultResume.parsedContent;
                        } else {
                            console.log(
                                `Default resume ${defaultResume.id} needs parsing`
                            );
                            try {
                                const parseUrl = new URL(
                                    `/api/resumes/parse`,
                                    request.url
                                ).toString();
                                console.log(
                                    `Calling parse API for default resume at: ${parseUrl}`
                                );
                                const parseResponse = await fetch(parseUrl, {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json",
                                        ...(request.headers.get("authorization")
                                            ? {
                                                  authorization:
                                                      request.headers.get(
                                                          "authorization"
                                                      )!,
                                              }
                                            : {}),
                                        ...(request.headers.get("cookie")
                                            ? {
                                                  cookie: request.headers.get(
                                                      "cookie"
                                                  )!,
                                              }
                                            : {}),
                                    },
                                    body: JSON.stringify({
                                        resumeId: defaultResume.id,
                                    }),
                                });

                                if (parseResponse.ok) {
                                    const { parsedContent } =
                                        await parseResponse.json();
                                    console.log(
                                        `Successfully parsed default resume ${defaultResume.id}`
                                    );
                                    resumeData = parsedContent;
                                } else {
                                    console.error(
                                        `Failed to parse default resume: ${parseResponse.status}`
                                    );
                                }
                            } catch (parseError) {
                                console.error(
                                    `Error parsing default resume:`,
                                    parseError
                                );
                            }
                        }
                    } else {
                        console.log(
                            `No default resume found, clearing resumeId from application`
                        );
                        await db
                            .update(jobApplications)
                            .set({
                                resumeId: null,
                                updatedAt: new Date(),
                            })
                            .where(eq(jobApplications.id, applicationId));
                    }
                } else {
                    console.log(
                        `Resume ${resumeId} found, checking for parsed content`
                    );
                    if (
                        resume.parsedContent &&
                        Object.keys(resume.parsedContent).length > 0
                    ) {
                        console.log(
                            `Using existing parsed content for resume ${resumeId}`
                        );
                        resumeData = resume.parsedContent;
                    } else {
                        console.log(
                            `No parsed content found, calling parse API for resume ${resumeId}`
                        );
                        try {
                            const parseUrl = new URL(
                                `/api/resumes/parse`,
                                request.url
                            ).toString();
                            console.log(`Calling parse API at: ${parseUrl}`);
                            const parseResponse = await fetch(parseUrl, {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json",
                                    ...(request.headers.get("authorization")
                                        ? {
                                              authorization:
                                                  request.headers.get(
                                                      "authorization"
                                                  )!,
                                          }
                                        : {}),
                                    ...(request.headers.get("cookie")
                                        ? {
                                              cookie: request.headers.get(
                                                  "cookie"
                                              )!,
                                          }
                                        : {}),
                                },
                                body: JSON.stringify({ resumeId }),
                            });

                            if (!parseResponse.ok) {
                                console.error(
                                    `Parse API returned error: ${parseResponse.status} ${parseResponse.statusText}`
                                );
                                throw new Error(
                                    `Failed to parse resume: ${parseResponse.status} ${parseResponse.statusText}`
                                );
                            }

                            const { parsedContent } =
                                await parseResponse.json();
                            console.log(
                                `Successfully parsed resume ${resumeId}`
                            );
                            resumeData = parsedContent;
                        } catch (parseError) {
                            console.error(
                                `Failed to parse resume ${resumeId}:`,
                                parseError
                            );
                            console.log("Continuing without resume data");
                        }
                    }
                }
            } catch (error) {
                console.error(`Error fetching resume ${resumeId}:`, error);
                console.log("Continuing without resume data");
            }
        } else {
            console.log("No resumeId provided, checking for default resume");

            try {
                const [defaultResume] = await db
                    .select()
                    .from(resumes)
                    .where(
                        and(
                            eq(resumes.userId, userId),
                            eq(resumes.isDefault, true)
                        )
                    )
                    .limit(1);

                if (defaultResume) {
                    console.log(
                        `Found default resume ${defaultResume.id}, updating application`
                    );

                    await db
                        .update(jobApplications)
                        .set({
                            resumeId: defaultResume.id,
                            updatedAt: new Date(),
                        })
                        .where(eq(jobApplications.id, applicationId));

                    if (
                        defaultResume.parsedContent &&
                        Object.keys(defaultResume.parsedContent).length > 0
                    ) {
                        console.log(
                            `Using parsed content from default resume ${defaultResume.id}`
                        );
                        resumeData = defaultResume.parsedContent;
                    }
                } else {
                    console.log("No default resume found");
                }
            } catch (error) {
                console.error("Error fetching default resume:", error);
            }
        }

        // Prepare job description - use parsed if available, otherwise raw
        const to_use = application.parsedJobDescription
            ? JSON.stringify(application.parsedJobDescription)
            : application.jobDescription;

        // Build comprehensive prompts
        let prompt = "";
        if (type === "cover") {
            prompt = `
Write a professional cover letter for a ${application.position} position at ${
                application.company
            }.

${
    to_use
        ? `The job description is as follows:
${to_use}`
        : `Please assume a typical job description for a ${application.position} role if no specific job description is provided.`
}
${
    resumeData
        ? `Here's my resume information to use for the cover letter:
${JSON.stringify(resumeData, null, 2)}`
        : `If no resume information is provided, assume a candidate with 5 years of relevant experience and skills typical for a ${application.position} role.`
}

Please write a cover letter that:
1. Starts with a header including ${
                resumeData?.name || "[Your Name]"
            } or name from the resume, email{${
                resumeData?.email || "[Your Email]"
            }} (use the email from the resume), phone number{${
                resumeData?.phone || "[Your Phone]"
            }} (use the phone number from the resume), the date ${new Date().toLocaleDateString()}), and the company's address (use [${
                application.company
            }, 123 Company Street, City, State, ZIP] if not specified).
2. Includes a professional greeting (e.g., 'Dear Hiring Manager' if no name is provided) and an introduction expressing interest in the role.
3. Explains why I'm interested in this specific role at ${
                application.company
            }, incorporating details from the job description or company mission where relevant.
4. Highlights my relevant skills and experiences for a ${
                application.position
            } role, matching them to the job description or assumed requirements, and includes at least one specific achievement (e.g., 'increased sales by 20%' or 'managed a team of 10').
5. Demonstrates enthusiasm for ${
                application.company
            } and its mission, values, or initiatives (research ${
                application.company
            } online if needed to reflect a current initiative as of March 15, 2025).
6. Ends with a confident call to action (e.g., requesting an interview) and a professional closing (e.g., 'Sincerely, [Your Name]').
7. Is 300-400 words in length.

The tone should be professional, confident yet humble, and convey genuine interest in the role and company. Avoid generic phrases and ensure the content is tailored to the provided job description and resume data, or reasonable assumptions if either is missing.
`;
        } else if (type === "linkedin") {
            prompt = `
Write a personalized LinkedIn message to a hiring manager or recruiter for a ${
                application.position
            } position at ${application.company}.

${
    to_use
        ? `The job description is as follows:
${to_use}`
        : ""
}

${
    resumeData
        ? `Here's my resume information to reference in the message:
${JSON.stringify(resumeData, null, 2)}`
        : ""
}

Please write a LinkedIn message that:
1. Has a brief, professional greeting
2. Introduces myself and explains my interest in the ${
                application.position
            } role
3. Mentions 1-2 key qualifications that make me a good fit
4. References something specific about ${
                application.company
            } to show I've done my research
5. Includes a clear call to action (like requesting a conversation)
6. Is concise (around 150 words maximum)
7. Has a professional closing

The tone should be professional but conversational, showing enthusiasm without being overly formal.
`;
        } else if (type === "email") {
            prompt = `
Write a cold email for a ${application.position} position at ${
                application.company
            }.

${
    to_use
        ? `The job description is as follows:
${to_use}`
        : `Assume a typical job description for a ${application.position} role if no specific job description is provided.`
}

${
    resumeData
        ? `Here's my resume information to reference in the email:
${JSON.stringify(resumeData, null, 2)}`
        : `If no resume information is provided, assume a candidate with 5 years of relevant experience and skills typical for a ${application.position} role.`
}

Please write a cold email that:
1. Includes a compelling subject line (e.g., "Exploring ${
                application.position
            } Opportunities at ${
                application.company
            }" or "Bringing [Key Skill] to ${application.company}'s Team").
2. Opens with a professional greeting (e.g., "Dear Hiring Manager" if no recipient name is provided).
3. Introduces myself clearly and concisely, including my name and a brief background (e.g., "I'm [Your Name], a software engineer with 5 years of experience").
4. Explains why I'm interested specifically in ${
                application.company
            } and this role, incorporating details from the job description or a recent company initiative (research ${
                application.company
            } online if needed for a current detail as of March 15, 2025).
5. Highlights 2-3 key qualifications relevant to the ${
                application.position
            } position, matching them to the job description or assumed requirements, and includes at least one specific achievement (e.g., "increased efficiency by 15%" or "led a team of 10").
6. Includes a clear call to action (e.g., "Could we schedule a 15-minute call to discuss?" or "I'd love to hear about any upcoming opportunities").
7. Ends with a professional signature, including my name, email (use the email from the resume), and phone number (use the phone number from the resume).
8. Is approximately 200-250 words in total.

The tone should be professional, confident, and demonstrate genuine interest without being pushy. Avoid generic phrases and ensure the content is tailored to the provided job description and resume data, or reasonable assumptions if either is missing. Include "Subject: " at the beginning with an appropriate subject line.
`;
        }

        console.log("prompt:", prompt);
        const generatedContent = await gemini.generateContent(prompt);

        const now = new Date();
        const contentField =
            type === "cover"
                ? { coverLetter: generatedContent }
                : type === "linkedin"
                ? { linkedinMessage: generatedContent }
                : { coldEmail: generatedContent };

        if (document) {
            await db
                .update(generatedDocuments)
                .set({
                    ...contentField,
                    updatedAt: now,
                })
                .where(eq(generatedDocuments.id, document.id));
        } else {
            await db.insert(generatedDocuments).values({
                userId,
                applicationId,
                ...contentField,
                createdAt: now,
                updatedAt: now,
            });
        }

        return NextResponse.json({
            message: "Content generated successfully",
            [type === "cover"
                ? "coverLetter"
                : type === "linkedin"
                ? "linkedinMessage"
                : "coldEmail"]: generatedContent,
        });
    } catch (error) {
        console.error("Error generating content:", error);
        return NextResponse.json(
            { error: "Failed to generate content" },
            { status: 500 }
        );
    }
}

export async function PATCH(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;

        let body;
        try {
            body = await request.json();
        } catch (parseError) {
            console.error("JSON parse error:", parseError);
            return NextResponse.json(
                { error: "Invalid JSON in request body" },
                { status: 400 }
            );
        }

        const result = updateContentSchema.safeParse(body);
        if (!result.success) {
            console.error("Validation error:", result.error.errors);
            return NextResponse.json(
                { error: "Invalid request data", details: result.error.errors },
                { status: 400 }
            );
        }

        const { field, value } = result.data;

        const applicationId = parseInt(id);
        if (isNaN(applicationId)) {
            return NextResponse.json(
                { error: "Invalid application ID" },
                { status: 400 }
            );
        }

        const application = await db.query.jobApplications.findFirst({
            where: and(
                eq(jobApplications.id, applicationId),
                eq(jobApplications.userId, userId)
            ),
        });

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        const document = await db.query.generatedDocuments.findFirst({
            where: eq(generatedDocuments.applicationId, applicationId),
        });

        const now = new Date();
        const updateData = {
            [field]: value,
            updatedAt: now,
        };

        try {
            if (document) {
                await db
                    .update(generatedDocuments)
                    .set(updateData)
                    .where(eq(generatedDocuments.id, document.id));
            } else {
                await db.insert(generatedDocuments).values({
                    userId,
                    applicationId,
                    ...updateData,
                    createdAt: now,
                });
            }
        } catch (dbError) {
            console.error("Database error:", dbError);
            return NextResponse.json(
                { error: "Failed to save content to database" },
                { status: 500 }
            );
        }

        return NextResponse.json({
            message: "Content updated successfully",
            [field]: value,
        });
    } catch (error) {
        console.error("Error updating content:", error);
        return NextResponse.json(
            { error: "Failed to update content" },
            { status: 500 }
        );
    }
}
