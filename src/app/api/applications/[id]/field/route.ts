import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { jobApplications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";

// Validation schema for field updates
const updateFieldSchema = z.object({
    field: z.enum([
        "company",
        "position",
        "location",
        "jobDescription",
        "notes",
        "applicationUrl",
        "contactName",
        "contactEmail",
        "contactPhone",
        "salary",
        "jobLink",
    ]),
    value: z.string().max(10000, "Value is too long"),
});

// Field-specific validation
const fieldValidation = {
    company: z.string().min(1, "Company name is required").max(255),
    position: z.string().min(1, "Position is required").max(255),
    location: z.string().max(255).optional(),
    jobDescription: z.string().max(10000),
    notes: z.string().max(2000),
    applicationUrl: z.string().url("Invalid URL").or(z.literal("")),
    contactName: z.string().max(255),
    contactEmail: z.string().email("Invalid email").or(z.literal("")),
    contactPhone: z.string().max(50),
    salary: z.string().max(100),
    jobLink: z.string().url("Invalid URL").or(z.literal("")),
};

export async function PATCH(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        const body = await request.json();

        // Validate request body
        const { field, value } = updateFieldSchema.parse(body);

        // Validate field-specific value
        const fieldValidator =
            fieldValidation[field as keyof typeof fieldValidation];
        if (fieldValidator) {
            try {
                fieldValidator.parse(value);
            } catch (error) {
                if (error instanceof z.ZodError) {
                    return NextResponse.json(
                        { error: error.errors[0].message },
                        { status: 400 }
                    );
                }
            }
        }

        // Check if application exists and belongs to user
        const existingApplication = await db.query.jobApplications.findFirst({
            where: and(
                eq(jobApplications.id, parseInt(id)),
                eq(jobApplications.userId, userId)
            ),
        });

        if (!existingApplication) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        // Update the specific field
        const [updatedApplication] = await db
            .update(jobApplications)
            .set({
                [field]: value || null,
                updatedAt: new Date(),
            })
            .where(
                and(
                    eq(jobApplications.id, parseInt(id)),
                    eq(jobApplications.userId, userId)
                )
            )
            .returning();

        if (!updatedApplication) {
            return NextResponse.json(
                { error: "Failed to update application" },
                { status: 500 }
            );
        }

        return NextResponse.json(updatedApplication);
    } catch (error) {
        console.error("Error updating application field:", error);

        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { error: error.errors[0].message },
                { status: 400 }
            );
        }

        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
