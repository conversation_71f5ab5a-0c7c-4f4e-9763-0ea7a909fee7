import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { jobApplications } from "@/lib/db/schema";
import { eq, desc, and, like, or } from "drizzle-orm";
import { getServerAuth } from "@/lib/auth";
import { VALID_STATUSES } from "@/lib/hooks/api-hooks";
import { ApplicationStatus } from "@/lib/hooks/api-hooks";

export async function POST(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const body = await req.json();
        const {
            company,
            position,
            jobDescription,
            parsedJobDescription,
            location,
            salary,
            applicationUrl,
            contactName,
            contactEmail,
            contactPhone,
            status,
            notes,
            resumeId,
        } = body;

        if (!company || !position) {
            return NextResponse.json(
                { error: "Company and position are required" },
                { status: 400 }
            );
        }

        // Insert the new application
        const [newApplication] = await db
            .insert(jobApplications)
            .values({
                userId,
                company,
                position,
                jobDescription: jobDescription || null,
                parsedJobDescription: parsedJobDescription || null,
                location: location || null,
                salary: salary || null,
                applicationUrl: applicationUrl || null,
                contactName: contactName || null,
                contactEmail: contactEmail || null,
                contactPhone: contactPhone || null,
                status: status || "not_applied",
                notes: notes || null,
                resumeId: resumeId || null,
                appliedDate: status === "applied" ? new Date() : null,
            })
            .returning();

        return NextResponse.json(newApplication);
    } catch (error) {
        console.error("Error creating application:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function GET(req: NextRequest) {
    try {
        const auth = await getServerAuth();

        if (!auth.userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Get query parameters
        const url = new URL(req.url);
        const status = url.searchParams.get("status");
        const search = url.searchParams.get("search");

        // Build the query conditions
        const whereConditions = [eq(jobApplications.userId, auth.userId)];

        // Add status filter if provided and valid
        if (status && VALID_STATUSES.includes(status as ApplicationStatus)) {
            whereConditions.push(
                eq(jobApplications.status, status as ApplicationStatus)
            );
        }

        // Get applications with filters
        let applications = await db
            .select()
            .from(jobApplications)
            .where(and(...whereConditions))
            .orderBy(desc(jobApplications.createdAt));

        // Apply search filter in memory if provided
        if (search) {
            const searchTerm = search.toLowerCase();
            applications = applications.filter(
                (app) =>
                    app.company.toLowerCase().includes(searchTerm) ||
                    app.position.toLowerCase().includes(searchTerm) ||
                    (app.location &&
                        app.location.toLowerCase().includes(searchTerm))
            );
        }

        return NextResponse.json(applications);
    } catch (error) {
        console.error("Error getting applications:", error);
        return NextResponse.json(
            { error: "Failed to fetch applications" },
            { status: 500 }
        );
    }
}
