import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import FireCrawlApp, { ScrapeResponse } from "@mendable/firecrawl-js";
import { z } from "zod";

// const RequestSchema = z.object({
//     jobUrl: z.string().trim().url().max(2048),
// });

// const FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY;
// const firecrawl = new FireCrawlApp({ apiKey: FIRECRAWL_API_KEY });

// export async function POST(request: NextRequest) {
//     const schema = z.object({
//         requiredSkills: z.array(z.string()).optional(),
//         requiredExperience: z.string().optional(),
//         potentialChallenges: z.array(z.string()).optional(),
//         keyQualifications: z.array(z.string()).optional(),
//         estimatedSalaryRange: z.string().optional(),
//         companyValues: z.array(z.string()).optional(),
//         jobResponsibilities: z.array(z.string()).optional(),
//         tips: z.array(z.string()).optional(),
//         company: z.string().optional(),
//         position: z.string().optional(),
//         location: z.string().optional(),
//         salary: z.string().optional(),
//         applicationUrl: z.string().optional(),
//     });

//     const body = await request.json();
//     const { jobUrl } = body;

//     console.log("Received jobUrl:", jobUrl);
//     console.log("FIRECRAWL_API_KEY:", FIRECRAWL_API_KEY ? "Set" : "Not set");

//     if (!jobUrl) {
//         return NextResponse.json(
//             { error: "Job URL is required" },
//             { status: 400 }
//         );
//     }

//     const { userId } = await auth();
//     if (!userId) {
//         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
//     }

//     const isValid = RequestSchema.safeParse({ jobUrl });
//     if (!isValid.success) {
//         return NextResponse.json({ error: "Invalid job URL" }, { status: 400 });
//     }

//     try {
//         const result = await firecrawl.extract([jobUrl], {
//             schema,
//             prompt: "Extract the job description from the following URL: ",
//         });

//         console.log("FireCrawl result:", result);

//         if (result.error) {
//             console.error("FireCrawl error:", result.error);
//             return NextResponse.json(
//                 {
//                     error: "Error extracting job description",
//                     details: result.error,
//                 },
//                 { status: 500 }
//             );
//         }

//         if (result.success) {
//             return NextResponse.json(result.data);
//         }

//         return NextResponse.json(
//             { error: "Unexpected response from FireCrawl" },
//             { status: 500 }
//         );
//     } catch (error) {
//         console.error("Exception in API route:", error);
//         return NextResponse.json(
//             {
//                 error: "Internal server error",
//                 details: error instanceof Error ? error.message : String(error),
//             },
//             { status: 500 }
//         );
//     }
// }

const RequestSchema = z.object({
    jobUrl: z.string().trim().url().max(2048),
});

const FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY;
const firecrawl = new FireCrawlApp({ apiKey: FIRECRAWL_API_KEY });

export async function POST(request: NextRequest) {
    try {
        // Validate auth
        const { userId } = await auth();
        if (!userId) {
            return new NextResponse("Unauthorized", { status: 401 });
        }

        // Parse and validate request
        const body = await request.json();
        const validation = RequestSchema.safeParse(body);

        if (!validation.success) {
            return new NextResponse("Invalid job URL", { status: 400 });
        }

        const { jobUrl } = validation.data;

        // Check API key
        if (!FIRECRAWL_API_KEY) {
            return new NextResponse("FireCrawl is not configured", {
                status: 500,
            });
        }

        // Scrape the URL
        const response = (await firecrawl.scrapeUrl(jobUrl, {
            formats: ["markdown"],
        })) as ScrapeResponse<any, never>;

        // Send to analyze-job endpoint
        const analyzeResponse = await fetch(
            new URL("/api/ai/analyze-job", request.url),
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    // Forward authorization
                    Cookie: request.headers.get("cookie") || "",
                },
                body: JSON.stringify({
                    jobDescription: response.markdown,
                    applicationUrl: jobUrl,
                }),
            }
        );

        if (!analyzeResponse.ok) {
            return new NextResponse("Failed to analyze job description", {
                status: analyzeResponse.status,
            });
        }

        // Return the analysis result
        const analysisResult = await analyzeResponse.json();
        return NextResponse.json(analysisResult);
    } catch (error) {
        console.error("Error processing job URL:", error);
        return new NextResponse(
            error instanceof Error ? error.message : "An error occurred",
            { status: 500 }
        );
    }
}
