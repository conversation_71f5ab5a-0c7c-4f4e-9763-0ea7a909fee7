import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

type Message = {
    role: "user" | "assistant";
    content: string;
};

export async function POST(req: NextRequest) {
    try {
        // Check authentication
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Parse request body
        const body = await req.json();
        const {
            applicationId,
            message,
            company,
            position,
            jobDescription,
            resumeId,
            history,
        } = body;

        if (!message || !company || !position) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        console.log(
            `Processing interview chat for application ${applicationId} for user ${userId}`
        );

        // Get resume content if available
        let resumeContent = "";
        if (resumeId) {
            try {
                const [resume] = await db
                    .select()
                    .from(resumes)
                    .where(eq(resumes.id, parseInt(resumeId)))
                    .limit(1);

                if (resume && resume.parsedContent) {
                    resumeContent = JSON.stringify(resume.parsedContent);
                }
            } catch (error) {
                console.error("Error fetching resume:", error);
            }
        }

        // This would be replaced with a real AI model call (e.g., OpenAI, Gemini, etc.)
        // For now, we'll use a simple response generation function
        const response = generateResponse(
            message,
            company,
            position,
            jobDescription,
            history
        );

        return NextResponse.json({ response });
    } catch (error) {
        console.error("Error in interview chat:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

// Simple response generation function - would be replaced with AI model
function generateResponse(
    message: string,
    company: string,
    position: string,
    jobDescription: string,
    history: Message[]
) {
    // Extract skills from job description
    const skills = extractSkills(jobDescription);

    // Check for common question patterns
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes("skills") && lowerMessage.includes("important")) {
        return `Based on the job description for the ${position} position at ${company}, the most important skills appear to be:

${skills
    .slice(0, 5)
    .map((skill) => `• ${skill}`)
    .join("\n")}

When interviewing, be sure to highlight your experience with these technologies and how you've applied them in previous roles. Provide specific examples of projects where you've used these skills successfully.`;
    }

    if (
        lowerMessage.includes("technical interview") &&
        lowerMessage.includes("prepare")
    ) {
        return `To prepare for a technical interview for the ${position} position at ${company}, I recommend:

1. **Review core concepts**: Make sure you're comfortable with fundamentals related to ${skills
            .slice(0, 3)
            .join(", ")}.

2. **Practice coding problems**: Use platforms like LeetCode or HackerRank to practice algorithm and data structure problems.

3. **System design**: For a full stack role, be prepared to discuss how you would design and implement a complete application.

4. **Review your projects**: Be ready to discuss your past work in detail, focusing on your technical decisions and problem-solving approach.

5. **Research ${company}**: Understand their products, technologies, and engineering culture.

6. **Prepare questions**: Have thoughtful questions ready about the team, tech stack, and development processes.`;
    }

    if (lowerMessage.includes("common interview questions")) {
        return `Common interview questions for a ${position} position at companies like ${company} include:

1. **Technical questions**:
   • "Describe your experience with ${skills.slice(0, 3).join(" and ")}"
   • "How would you implement a feature that requires both frontend and backend changes?"
   • "How do you handle state management in a React application?"

2. **Problem-solving questions**:
   • "Tell me about a challenging technical problem you solved recently"
   • "How would you debug a production issue with limited information?"

3. **Behavioral questions**:
   • "Describe a time when you had to meet a tight deadline"
   • "How do you handle feedback on your code?"
   • "Tell me about a time you disagreed with a team member"

4. **Role-specific questions**:
   • "How do you approach testing your code?"
   • "How do you stay updated with the latest technologies?"
   • "How do you balance technical debt with delivering features?"`;
    }

    if (
        lowerMessage.includes("react") &&
        (lowerMessage.includes("experience") ||
            lowerMessage.includes("talk about"))
    ) {
        return `When discussing your React experience in an interview for the ${position} position at ${company}, focus on these key points:

1. **Depth of experience**: Mention how long you've worked with React and in what capacity.

2. **Specific projects**: Describe 1-2 significant projects where you used React, highlighting your role and impact.

3. **Technical knowledge**: Demonstrate understanding of core React concepts like:
   • Component lifecycle and hooks
   • State management (Context API, Redux, etc.)
   • Performance optimization techniques
   • React Router for navigation

4. **Problem-solving**: Share a specific challenge you faced with React and how you overcame it.

5. **Modern practices**: Mention your familiarity with current best practices like functional components, hooks, and code splitting.

Remember to tie your experience back to the requirements of the ${position} role, showing how your React skills would be valuable to ${company}.`;
    }

    if (lowerMessage.includes("questions") && lowerMessage.includes("ask")) {
        return `Great questions to ask the interviewer for the ${position} position at ${company} include:

1. **About the role**:
   • "What would my day-to-day responsibilities look like?"
   • "What are the biggest challenges I might face in this role?"
   • "How is success measured for this position?"

2. **About the team**:
   • "Can you tell me about the team I'd be working with?"
   • "How is the engineering team structured?"
   • "What's the code review process like?"

3. **About the company**:
   • "What's the company's approach to professional development?"
   • "How would you describe the engineering culture at ${company}?"
   • "What are the company's biggest technical challenges right now?"

4. **About the interviewer**:
   • "What do you enjoy most about working at ${company}?"
   • "How long have you been with the company, and how has your role evolved?"

5. **Next steps**:
   • "What are the next steps in the interview process?"
   • "Is there anything else I can provide to help you with your decision?"

These questions show your interest in the role and help you determine if the position is a good fit for you.`;
    }

    // Default response for other questions
    return `That's a great question about the ${position} role at ${company}!

Based on the job description, I can see that this role involves working with technologies like ${skills
        .slice(0, 3)
        .join(", ")}.

To give you the most helpful answer, could you clarify what specific aspect of the role or interview process you'd like to know more about? I can help with:

• Technical preparation
• Company research
• Interview strategies
• Role-specific information
• Questions to ask the interviewer`;
}

function extractSkills(jobDescription: string) {
    const skillsRegex =
        /(?:TypeScript|JavaScript|React|Node\.js|Python|Go|Rust|AWS|GCP|Azure|SQL|NoSQL|MongoDB|PostgreSQL|Docker|Kubernetes|CI\/CD|Git|REST|GraphQL|UI|UX|AI|ML)(?:[,\s]|$)/gi;
    const matches = jobDescription.match(skillsRegex) || [];
    return [...new Set(matches.map((skill) => skill.trim()))];
}
