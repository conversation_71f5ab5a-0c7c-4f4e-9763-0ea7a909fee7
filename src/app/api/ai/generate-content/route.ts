import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes, generatedDocuments, jobApplications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { gemini } from "@/lib/ai/gemini";

export async function GET(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const url = new URL(req.url);
        const applicationId = url.searchParams.get("applicationId");
        const contentType = url.searchParams.get("type");

        if (!applicationId) {
            return NextResponse.json(
                { error: "Missing applicationId parameter" },
                { status: 400 }
            );
        }

        // Verify the application belongs to the user
        const [application] = await db
            .select()
            .from(jobApplications)
            .where(
                and(
                    eq(jobApplications.id, parseInt(applicationId)),
                    eq(jobApplications.userId, userId)
                )
            )
            .limit(1);

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        // Fetch generated content
        const [document] = await db
            .select()
            .from(generatedDocuments)
            .where(
                eq(generatedDocuments.applicationId, parseInt(applicationId))
            )
            .limit(1);

        if (!document) {
            return NextResponse.json({
                coverLetter: null,
                linkedinMessage: null,
                coldEmail: null,
                chat: [],
            });
        }

        // Return specific content type if requested, otherwise return all
        if (contentType === "cover-letter") {
            return NextResponse.json({ content: document.coverLetter });
        } else if (contentType === "linkedin-message") {
            return NextResponse.json({ content: document.linkedinMessage });
        } else if (contentType === "cold-email") {
            return NextResponse.json({ content: document.coldEmail });
        } else if (contentType === "chat") {
            return NextResponse.json({ chat: document.chat || [] });
        } else {
            return NextResponse.json({
                coverLetter: document.coverLetter,
                linkedinMessage: document.linkedinMessage,
                coldEmail: document.coldEmail,
                chat: document.chat || [],
            });
        }
    } catch (error) {
        console.error("Error fetching content:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function POST(req: NextRequest) {
    try {
        // Verify user is authenticated
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Parse the request body
        const requestData = await req.json();
        const {
            type,
            applicationId,
            company,
            position,
            jobDescription,
            resumeId,
            forceRegenerate = false,
            parsedJobDescription,
        } = requestData;
        const to_use = parsedJobDescription
            ? JSON.stringify(parsedJobDescription)
            : jobDescription;
        if (!type || !applicationId) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        // Validate the application belongs to the user
        const [application] = await db
            .select()
            .from(jobApplications)
            .where(
                and(
                    eq(jobApplications.id, parseInt(applicationId)),
                    eq(jobApplications.userId, userId)
                )
            )
            .limit(1);

        if (!application) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        // Find existing generated document
        const [document] = await db
            .select()
            .from(generatedDocuments)
            .where(
                eq(generatedDocuments.applicationId, parseInt(applicationId))
            )
            .limit(1);

        // Map API type parameter to database field names
        let contentField;
        if (type === "cover-letter") {
            contentField = "coverLetter";
        } else if (type === "linkedin-message") {
            contentField = "linkedinMessage";
        } else if (type === "cold-email") {
            contentField = "coldEmail";
        } else {
            return NextResponse.json(
                { error: "Invalid content type" },
                { status: 400 }
            );
        }

        // Check if content already exists and forceRegenerate is false
        if (
            !forceRegenerate &&
            document &&
            document[contentField as keyof typeof document]
        ) {
            return NextResponse.json({
                message: "Content already exists",
                [contentField]: document[contentField as keyof typeof document],
            });
        }
        // Get resume content if available
        let resumeData = null;

        if (resumeId) {
            console.log(`Fetching resume data for resumeId: ${resumeId}`);
            try {
                // Check if the resume exists and belongs to the user
                const [resume] = await db
                    .select()
                    .from(resumes)
                    .where(
                        and(
                            eq(resumes.id, parseInt(resumeId)),
                            eq(resumes.userId, userId)
                        )
                    )
                    .limit(1);

                if (!resume) {
                    console.log(
                        `Resume ${resumeId} not found for user ${userId}`
                    );
                    return NextResponse.json(
                        { error: "Resume not found" },
                        { status: 404 }
                    );
                }

                console.log(
                    `Resume ${resumeId} found, checking for parsed content`
                );
                // Use parsed content directly if available
                if (
                    resume.parsedContent &&
                    Object.keys(resume.parsedContent).length > 0
                ) {
                    console.log(
                        `Using existing parsed content for resume ${resumeId}`
                    );
                    resumeData = resume.parsedContent;
                } else {
                    console.log(
                        `No parsed content found, calling parse API for resume ${resumeId}`
                    );
                    // If not parsed yet, call the parse API
                    try {
                        const parseUrl = new URL(
                            `/api/resumes/parse`,
                            req.url
                        ).toString();
                        console.log(`Calling parse API at: ${parseUrl}`);
                        const parseResponse = await fetch(parseUrl, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                ...Object.fromEntries(
                                    Array.from(req.headers.entries()).filter(
                                        ([key]) =>
                                            key
                                                .toLowerCase()
                                                .includes("auth") ||
                                            key.toLowerCase().includes("cookie")
                                    )
                                ),
                            },
                            body: JSON.stringify({ resumeId }),
                        });

                        if (!parseResponse.ok) {
                            console.error(
                                `Parse API returned error: ${parseResponse.status} ${parseResponse.statusText}`
                            );
                            throw new Error(
                                `Failed to parse resume: ${parseResponse.status} ${parseResponse.statusText}`
                            );
                        }

                        const { parsedContent } = await parseResponse.json();
                        console.log(`Successfully parsed resume ${resumeId}`);
                        resumeData = parsedContent;
                    } catch (parseError) {
                        console.error(
                            `Failed to parse resume ${resumeId}:`,
                            parseError
                        );
                        return NextResponse.json(
                            { error: "Failed to parse resume" },
                            { status: 500 }
                        );
                    }
                }
            } catch (error) {
                console.error(`Error fetching resume ${resumeId}:`, error);
                return NextResponse.json(
                    { error: "Failed to fetch resume" },
                    { status: 500 }
                );
            }
        } else {
            console.log("No resumeId provided, skipping resume data fetch");
        }

        // Generate the appropriate content based on type
        let prompt = "";
        if (type === "cover-letter") {
            prompt = `
Write a professional cover letter for a ${position} position at ${company}.

${
    to_use
        ? `The job description is as follows:
${to_use}`
        : "Please assume a typical job description for a ${position} role if no specific job description is provided."
}
${
    resumeData
        ? `Here's my resume information to use for the cover letter:
${JSON.stringify(resumeData, null, 2)}`
        : "If no resume information is provided, assume a candidate with 5 years of relevant experience and skills typical for a ${position} role."
}

Please write a cover letter that:
1. Starts with a header including ${
                resumeData.name
            } or name from the resume, email{${
                resumeData.email
            }} (use the email from the resume), phone number{${
                resumeData.phone
            }} (use the phone number from the resume), the date ${new Date().toLocaleDateString()}), and the company's address (use [${company}, 123 Company Street, City, State, ZIP] if not specified).
2. Includes a professional greeting (e.g., 'Dear Hiring Manager' if no name is provided) and an introduction expressing interest in the role.
3. Explains why I'm interested in this specific role at ${company}, incorporating details from the job description or company mission where relevant.
4. Highlights my relevant skills and experiences for a ${position} role, matching them to the job description or assumed requirements, and includes at least one specific achievement (e.g., 'increased sales by 20%' or 'managed a team of 10').
5. Demonstrates enthusiasm for ${company} and its mission, values, or initiatives (research ${company} online if needed to reflect a current initiative as of March 15, 2025).
6. Ends with a confident call to action (e.g., requesting an interview) and a professional closing (e.g., 'Sincerely, [Your Name]').
7. Is 300-400 words in length.

The tone should be professional, confident yet humble, and convey genuine interest in the role and company. Avoid generic phrases and ensure the content is tailored to the provided job description and resume data, or reasonable assumptions if either is missing.
`;
        } else if (type === "linkedin-message") {
            prompt = `
Write a personalized LinkedIn message to a hiring manager or recruiter for a ${position} position at ${company}.

${
    to_use
        ? `The job description is as follows:
${to_use}`
        : ""
}

${
    resumeData
        ? `Here's my resume information to reference in the message:
${JSON.stringify(resumeData, null, 2)}`
        : ""
}

Please write a LinkedIn message that:
1. Has a brief, professional greeting
2. Introduces myself and explains my interest in the ${position} role
3. Mentions 1-2 key qualifications that make me a good fit
4. References something specific about ${company} to show I've done my research
5. Includes a clear call to action (like requesting a conversation)
6. Is concise (around 150 words maximum)
7. Has a professional closing

The tone should be professional but conversational, showing enthusiasm without being overly formal.
`;
        } else if (type === "cold-email") {
            prompt = `
Write a cold email for a ${position} position at ${company}.

${
    to_use
        ? `The job description is as follows:
${to_use}`
        : "Assume a typical job description for a ${position} role if no specific job description is provided."
}

${
    resumeData
        ? `Here’s my resume information to reference in the email:
${JSON.stringify(resumeData, null, 2)}`
        : "If no resume information is provided, assume a candidate with 5 years of relevant experience and skills typical for a ${position} role."
}

Please write a cold email that:
1. Includes a compelling subject line (e.g., "Exploring ${position} Opportunities at ${company}" or "Bringing [Key Skill] to ${company}’s Team").
2. Opens with a professional greeting (e.g., "Dear Hiring Manager" if no recipient name is provided).
3. Introduces myself clearly and concisely, including my name and a brief background (e.g., "I’m [Your Name], a software engineer with 5 years of experience").
4. Explains why I’m interested specifically in ${company} and this role, incorporating details from the job description or a recent company initiative (research ${company} online if needed for a current detail as of March 15, 2025).
5. Highlights 2-3 key qualifications relevant to the ${position} position, matching them to the job description or assumed requirements, and includes at least one specific achievement (e.g., "increased efficiency by 15%" or "led a team of 10").
6. Includes a clear call to action (e.g., "Could we schedule a 15-minute call to discuss?" or "I’d love to hear about any upcoming opportunities").
7. Ends with a professional signature, including my name, email (use the email from the resume), and phone number (use the phone number from the resume).
8. Is approximately 200-250 words in total.

The tone should be professional, confident, and demonstrate genuine interest without being pushy. Avoid generic phrases and ensure the content is tailored to the provided job description and resume data, or reasonable assumptions if either is missing. Include "Subject: " at the beginning with an appropriate subject line.
`;
        }
        console.log("prompt:", prompt);
        // Generate content using the Gemini singleton
        const generatedContent = await gemini.generateContent(prompt);

        // Store the generated content
        const now = new Date();

        if (document) {
            // Update existing document
            await db
                .update(generatedDocuments)
                .set({
                    [contentField]: generatedContent,
                    updatedAt: now,
                })
                .where(eq(generatedDocuments.id, document.id));
        } else {
            // Create new document
            await db.insert(generatedDocuments).values({
                userId,
                applicationId: parseInt(applicationId),
                [contentField]: generatedContent,
                chat: [],
                createdAt: now,
                updatedAt: now,
            });
        }

        return NextResponse.json({
            message: "Content generated successfully",
            [contentField]: generatedContent,
        });
    } catch (error) {
        console.error("Error generating content:", error);
        return NextResponse.json(
            { error: "Failed to generate content" },
            { status: 500 }
        );
    }
}
