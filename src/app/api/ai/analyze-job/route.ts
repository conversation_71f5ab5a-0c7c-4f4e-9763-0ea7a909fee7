import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { gemini } from "@/lib/ai/gemini";

export async function POST(request: Request) {
    try {
        console.log("Starting job analysis request");

        // Verify authentication
        const { userId } = await auth();
        if (!userId) {
            console.log("Authentication failed: No userId found");
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }
        console.log(`Authenticated user: ${userId}`);

        // Parse request body
        const body = await request.json();
        const { jobDescription } = body;

        if (!jobDescription) {
            console.log("Missing job description in request");
            return NextResponse.json(
                { error: "Job description is required" },
                { status: 400 }
            );
        }
        console.log(
            `Received job description (${jobDescription.length} chars)`
        );

        const prompt = `
You are an AI assistant helping a job seeker analyze a job posting to identify key skills and qualifications needed.

JOB DESCRIPTION: ${jobDescription}

Please analyze the job description and provide the following information in this JSON format:
{
  "requiredSkills": ["skill1", "skill2", ...],
  "requiredExperience": "Brief summary of experience requirements",
  "potentialChallenges": ["challenge1", "challenge2", ...],
  "keyQualifications": ["qualification1", "qualification2", ...],
  "estimatedSalaryRange": "Estimated salary range if mentioned or inferable",
  "companyValues": ["value1", "value2", ...],
  "jobResponsibilities": ["responsibility1", "responsibility2", ...],
  "tips": ["tip1 for application", "tip2 for application", ...],
  "company": "Company name extracted from job description",
  "position": "Job title/position extracted from job description",
  "location": "Location extracted if available, otherwise empty string",
  "salary": "Salary range extracted if available, otherwise empty string",
  "applicationUrl": "Application URL if available, otherwise empty string"
}

The output should be valid JSON without any additional text or explanation. For any fields you cannot determine, provide an empty string or empty array as appropriate.`;

        console.log("Sending request to Gemini API");

        // Generate content using the singleton
        const response = await gemini.generateContent(prompt);
        console.log("Received response from Gemini API");

        // Extract the JSON from the response
        // The problem is that Gemini might be returning markdown-formatted JSON with code blocks
        let jsonText = response;

        // Handle case where Gemini returns the JSON in a code block
        if (response.includes("```json")) {
            jsonText = response.split("```json")[1].split("```")[0].trim();
        } else if (response.includes("```")) {
            jsonText = response.split("```")[1].split("```")[0].trim();
        }

        let analysis;
        try {
            analysis = JSON.parse(jsonText);
            console.log("Successfully parsed AI response as JSON");
            console.log(analysis);
        } catch (error) {
            console.error("Error parsing AI response:", error);
            // If parsing fails, return the raw text
            return NextResponse.json({
                raw: response,
                error: "Failed to parse analysis",
            });
        }

        // Create a summary for notes
        const notesSummary = [
            analysis.requiredSkills?.length > 0
                ? `Required Skills: ${analysis.requiredSkills.join(", ")}`
                : "",
            analysis.requiredExperience
                ? `Experience: ${analysis.requiredExperience}`
                : "",
            analysis.keyQualifications?.length > 0
                ? `Key Qualifications: ${analysis.keyQualifications.join(", ")}`
                : "",
        ]
            .filter(Boolean)
            .join("\n\n");

        // Add the notes summary to the response
        analysis.notes = notesSummary;

        console.log("Returning job analysis results");
        return NextResponse.json(analysis);
    } catch (error) {
        console.error("Error in job analysis:", error);
        return NextResponse.json(
            { error: "Failed to analyze job description" },
            { status: 500 }
        );
    }
}
