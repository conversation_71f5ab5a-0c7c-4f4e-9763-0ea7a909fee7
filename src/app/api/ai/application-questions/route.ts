import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { eq } from "drizzle-orm";
import {
    resumes,
    generatedDocuments,
    ChatMessageSchema,
} from "@/lib/db/schema";
import { jobApplications } from "@/lib/db/schema";
import { gemini } from "@/lib/ai/gemini";
export async function GET(request: Request) {
    try {
        const { userId } = await auth();

        const url = new URL(request.url);
        const applicationId = url.searchParams.get("applicationId");

        if (!applicationId) {
            return NextResponse.json(
                { error: "Missing applicationId parameter" },
                { status: 400 }
            );
        }

        // Verify the application belongs to the user
        const application = await db.query.jobApplications.findFirst({
            where: eq(jobApplications.id, parseInt(applicationId)),
        });

        if (!application || application.userId !== userId) {
            return NextResponse.json(
                { error: "Application not found" },
                { status: 404 }
            );
        }

        // Fetch generated content
        const document = await db.query.generatedDocuments.findFirst({
            where: eq(
                generatedDocuments.applicationId,
                parseInt(applicationId)
            ),
        });

        return NextResponse.json({ chat: document?.chat || [] });
    } catch (error) {
        console.error("Error fetching chat history:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        // Verify authentication
        const { userId } = await auth();

        // Parse request body
        const { question, applicationId } = await request.json();

        // Get application data from database
        const application = applicationId
            ? await db.query.jobApplications.findFirst({
                  where: eq(jobApplications.id, parseInt(applicationId)),
              })
            : null;

        const jobDescription = application?.jobDescription;
        const resumeId = application?.resumeId;

        // Get resume content if available
        let resume;
        if (resumeId) {
            const resumeData = await db.query.resumes.findFirst({
                where: eq(resumes.id, resumeId),
            });
            resume = resumeData?.parsedContent;
        }

        if (!question) {
            return NextResponse.json(
                { error: "Question is required" },
                { status: 400 }
            );
        }

        // Create context for the AI
        let context = "";

        if (jobDescription) context += `Job Description: ${jobDescription}\n`;
        if (resume) context += `Parsed Resume Summary: ${resume}\n`;

        // Generate prompt for the AI
        const prompt = `
    You are an AI assistant helping a job applicant prepare answers for interview or application questions.

    CONTEXT ABOUT THE JOB:
    ${context}

    USER QUESTION: ${question}

    Please provide a well-structured, professional response that:
    1. Is personalized based on the job and company
    2. Highlights relevant qualifications and experience
    3. Shows enthusiasm and cultural fit
    4. Is concise yet comprehensive (max 3-4 paragraphs)
    5. Uses professional, confident language
    6. Is formatted for the application
    7. Answer in 1-2 sentences and make it sound like a real person
    8. Make sure to use the right tone for the question
    Your response should be ready to use or easily adaptable for the application.
    `;

        // Generate content using the Gemini singleton
        const answer = await gemini.generateContent(prompt);

        // Get or create document record for this application
        let document = await db.query.generatedDocuments.findFirst({
            where: eq(
                generatedDocuments.applicationId,
                parseInt(applicationId)
            ),
        });

        const now = new Date();
        const userMessage = ChatMessageSchema.parse({
            speaker: "user",
            time: now,
            message: question,
        });

        const botMessage = ChatMessageSchema.parse({
            speaker: "bot",
            time: now,
            message: answer,
        });

        if (document) {
            // Update existing document with new chat messages
            const updatedChat = [
                ...(document.chat || []),
                userMessage,
                botMessage,
            ];
            await db
                .update(generatedDocuments)
                .set({
                    chat: updatedChat,
                    updatedAt: now,
                })
                .where(eq(generatedDocuments.id, document.id));
        } else {
            // Create new document with chat history
            await db.insert(generatedDocuments).values({
                userId: userId || "",
                applicationId: parseInt(applicationId),
                chat: [userMessage, botMessage],
                createdAt: now,
                updatedAt: now,
            });
        }

        return NextResponse.json({ answer }, { status: 200 });
    } catch (error) {
        console.error("Error generating answer:", error);
        return NextResponse.json(
            { error: "Failed to generate answer" },
            { status: 500 }
        );
    }
}
