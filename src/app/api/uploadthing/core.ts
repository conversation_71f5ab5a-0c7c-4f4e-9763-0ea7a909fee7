import { createUploadthing, type <PERSON><PERSON>outer } from "uploadthing/next";
import { getServerAuth } from "@/lib/auth";

const f = createUploadthing();

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
    // Define a route for resume uploads
    resumeUploader: f({
        // Accept PDF and DOCX files
        "application/pdf": {
            maxFileSize: "1MB",
            maxFileCount: 1,
        },
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            {
                maxFileSize: "1MB",
                maxFileCount: 1,
            },
    })
        // Set permissions and file types for this FileRoute
        .middleware(async ({ req }) => {
            try {
                // This code runs on your server before upload
                const auth = await getServerAuth();

                // If you throw, the user will not be able to upload
                if (!auth.userId) {
                    console.log("UploadThing: No userId found in auth");
                    return { userId: "anonymous" }; // Allow upload but mark as anonymous
                }

                // Whatever is returned here is accessible in onUploadComplete as `metadata`
                return { userId: auth.userId };
            } catch (error) {
                console.error("UploadThing middleware error:", error);
                return { userId: "anonymous" }; // Fallback for auth errors
            }
        })
        .onUploadComplete(async ({ metadata, file }) => {
            // This code RUNS ON YOUR SERVER after upload
            console.log("Upload complete for userId:", metadata.userId);
            console.log("file url", file.ufsUrl);

            // Return the file information to the client
            return {
                url: file.ufsUrl,
                name: file.name,
                size: file.size,
                key: file.key,
                userId: metadata.userId,
            };
        }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
