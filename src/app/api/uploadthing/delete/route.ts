import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { UTApi } from "uploadthing/server";

const utapi = new UTApi();

export async function DELETE(req: NextRequest) {
    const startTime = Date.now();
    const requestId = `ut_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

    console.log(
        `[UPLOADTHING_DELETE:${requestId}] === Starting file deletion process ===`
    );
    console.log(
        `[UPLOADTHING_DELETE:${requestId}] Request method: ${req.method}`
    );
    console.log(`[UPLOADTHING_DELETE:${requestId}] Request URL: ${req.url}`);
    console.log(
        `[UPLOADTHING_DELETE:${requestId}] User-Agent: ${req.headers.get(
            "user-agent"
        )}`
    );
    console.log(
        `[UPLOADTHING_DELETE:${requestId}] Referer: ${req.headers.get(
            "referer"
        )}`
    );

    try {
        const { userId } = getAuth(req);

        if (!userId) {
            console.warn(
                `[UPLOADTHING_DELETE:${requestId}] Unauthorized access attempt - no userId found`
            );
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Authenticated user: ${userId}`
        );

        const { searchParams } = new URL(req.url);
        const fileKey = searchParams.get("key");

        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Extracted file key from query params: ${fileKey}`
        );
        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Full query params:`,
            Object.fromEntries(searchParams.entries())
        );

        if (!fileKey) {
            console.error(
                `[UPLOADTHING_DELETE:${requestId}] Missing required file key parameter`
            );
            return NextResponse.json(
                { error: "File key is required" },
                { status: 400 }
            );
        }

        // Validate file key format (UploadThing keys typically have a specific pattern)
        if (fileKey.length < 10) {
            console.warn(
                `[UPLOADTHING_DELETE:${requestId}] Suspicious file key format - too short: ${fileKey}`
            );
        }

        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Starting UploadThing file deletion...`
        );
        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Target file key: ${fileKey}`
        );

        const deleteStart = Date.now();

        // Delete the file from UploadThing
        const deleteResult = await utapi.deleteFiles(fileKey);

        const deleteTime = Date.now() - deleteStart;
        const totalRequestTime = Date.now() - startTime;

        console.log(
            `[UPLOADTHING_DELETE:${requestId}] UploadThing deleteFiles() completed in ${deleteTime}ms`
        );
        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Delete operation result:`,
            deleteResult
        );
        console.log(
            `[UPLOADTHING_DELETE:${requestId}] Total request processing time: ${totalRequestTime}ms`
        );

        const response = {
            success: true,
            message: "File deleted successfully",
            metadata: {
                requestId,
                fileKey,
                userId,
                processingTimeMs: totalRequestTime,
                uploadThingDeleteTimeMs: deleteTime,
                deleteResult,
            },
        };

        console.log(
            `[UPLOADTHING_DELETE:${requestId}] === File deletion completed successfully ===`,
            {
                fileKey,
                userId,
                totalTime: totalRequestTime,
                deleteTime,
            }
        );

        return NextResponse.json(response);
    } catch (error) {
        const totalRequestTime = Date.now() - startTime;
        console.error(
            `[UPLOADTHING_DELETE:${requestId}] === File deletion failed after ${totalRequestTime}ms ===`,
            {
                error,
                errorMessage:
                    error instanceof Error ? error.message : "Unknown error",
                errorStack: error instanceof Error ? error.stack : undefined,
                errorName: error instanceof Error ? error.name : "Unknown",
                requestUrl: req.url,
            }
        );

        // Check if it's a specific UploadThing error
        if (error instanceof Error) {
            if (
                error.message.includes("not found") ||
                error.message.includes("404")
            ) {
                console.warn(
                    `[UPLOADTHING_DELETE:${requestId}] File not found in UploadThing - may have been already deleted`
                );
                return NextResponse.json({
                    success: true,
                    message: "File was already deleted or not found",
                    metadata: {
                        requestId,
                        processingTimeMs: totalRequestTime,
                        warning: "File not found in storage",
                    },
                });
            }

            if (
                error.message.includes("unauthorized") ||
                error.message.includes("403")
            ) {
                console.error(
                    `[UPLOADTHING_DELETE:${requestId}] UploadThing authorization failed`
                );
                return NextResponse.json(
                    {
                        error: "Storage authorization failed",
                        details: "Unable to delete file from storage",
                    },
                    { status: 403 }
                );
            }
        }

        return NextResponse.json(
            {
                error: "Failed to delete file",
                metadata: {
                    requestId,
                    processingTimeMs: totalRequestTime,
                },
                details:
                    error instanceof Error ? error.message : "Unknown error",
            },
            { status: 500 }
        );
    }
}
