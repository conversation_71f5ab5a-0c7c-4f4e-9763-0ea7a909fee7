import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { shortenedUrls, jobApplications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { z } from "zod";

// Define validation schema for request
const LinkApplicationSchema = z.object({
    shortCode: z
        .string({
            required_error: "Short code is required",
            invalid_type_error: "Short code must be a string",
        })
        .trim()
        .min(1, "Short code cannot be empty")
        .max(10, "Short code is too long"),
    applicationId: z
        .number({
            required_error: "Application ID is required",
            invalid_type_error: "Application ID must be a number",
        })
        .int("Application ID must be an integer")
        .positive("Application ID must be positive"),
});

export async function POST(request: NextRequest) {
    try {
        // Authenticate user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Parse and validate request body
        let validatedBody;
        try {
            const body = await request.json().catch(() => null);

            if (!body) {
                return NextResponse.json(
                    { error: "Invalid JSON in request body" },
                    { status: 400 }
                );
            }

            // Validate with Zod schema
            const result = LinkApplicationSchema.safeParse(body);

            if (!result.success) {
                const errorMessage = result.error.issues
                    .map((issue) => issue.message)
                    .join(", ");

                return NextResponse.json(
                    { error: errorMessage },
                    { status: 400 }
                );
            }

            validatedBody = result.data;
        } catch (e) {
            console.error("Error parsing request body:", e);
            return NextResponse.json(
                { error: "Invalid request body" },
                { status: 400 }
            );
        }

        const { shortCode, applicationId } = validatedBody;

        // Verify the URL exists and belongs to this user
        const urls = await db
            .select()
            .from(shortenedUrls)
            .where(
                and(
                    eq(shortenedUrls.shortCode, shortCode),
                    eq(shortenedUrls.userId, userId)
                )
            )
            .limit(1);

        if (urls.length === 0) {
            return NextResponse.json(
                { error: "URL not found or doesn't belong to you" },
                { status: 404 }
            );
        }

        // Verify the application exists and belongs to this user
        const applications = await db
            .select()
            .from(jobApplications)
            .where(
                and(
                    eq(jobApplications.id, applicationId),
                    eq(jobApplications.userId, userId)
                )
            )
            .limit(1);

        if (applications.length === 0) {
            return NextResponse.json(
                { error: "Application not found or doesn't belong to you" },
                { status: 404 }
            );
        }

        // Update the URL record with the application ID
        await db
            .update(shortenedUrls)
            .set({ applicationId })
            .where(eq(shortenedUrls.shortCode, shortCode));

        // Update the application with the shortened URL
        const shortenedUrl = `${
            process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
        }/s/${shortCode}`;
        await db
            .update(jobApplications)
            .set({ jobLink: shortenedUrl })
            .where(eq(jobApplications.id, applicationId));

        return NextResponse.json({
            success: true,
            message: "URL linked to application successfully",
        });
    } catch (error) {
        console.error("Error linking URL to application:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
