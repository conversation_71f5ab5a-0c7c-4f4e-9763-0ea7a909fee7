import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { shortenedUrls } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

// Define validation schema for query parameters
const QueryParamsSchema = z.object({
    code: z
        .string()
        .trim()
        .min(1, "Short code cannot be empty")
        .max(10, "Short code is too long")
        .optional(),
});

export async function GET(request: NextRequest) {
    try {
        // Get and validate query parameters
        const searchParams = request.nextUrl.searchParams;
        const shortCode = searchParams.get("code");

        // Validate the code parameter if provided
        if (shortCode) {
            const result = QueryParamsSchema.safeParse({ code: shortCode });

            if (!result.success) {
                const errorMessage = result.error.issues
                    .map((issue) => issue.message)
                    .join(", ");

                return NextResponse.json(
                    { error: errorMessage },
                    { status: 400 }
                );
            }
        }

        // Authenticate user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // If short code is provided, get stats for that URL
        if (shortCode) {
            const urls = await db
                .select()
                .from(shortenedUrls)
                .where(eq(shortenedUrls.shortCode, shortCode))
                .limit(1);

            if (urls.length === 0) {
                return NextResponse.json(
                    { error: "URL not found" },
                    { status: 404 }
                );
            }

            const url = urls[0];

            // Check if the URL belongs to this user
            if (url.userId !== userId) {
                return NextResponse.json(
                    { error: "Unauthorized to access this URL" },
                    { status: 403 }
                );
            }

            return NextResponse.json({
                shortCode: url.shortCode,
                originalUrl: url.originalUrl,
                clickCount: url.clickCount,
                createdAt: url.createdAt,
                expiresAt: url.expiresAt,
            });
        }

        // Otherwise, get stats for all URLs belonging to this user
        const userUrls = await db
            .select({
                id: shortenedUrls.id,
                shortCode: shortenedUrls.shortCode,
                originalUrl: shortenedUrls.originalUrl,
                clickCount: shortenedUrls.clickCount,
                createdAt: shortenedUrls.createdAt,
                expiresAt: shortenedUrls.expiresAt,
                applicationId: shortenedUrls.applicationId,
            })
            .from(shortenedUrls)
            .where(eq(shortenedUrls.userId, userId))
            .orderBy(shortenedUrls.createdAt);

        return NextResponse.json({
            totalUrls: userUrls.length,
            totalClicks: userUrls.reduce(
                (sum, url) => sum + (url.clickCount || 0),
                0
            ),
            urls: userUrls,
        });
    } catch (error) {
        console.error("URL stats API error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
