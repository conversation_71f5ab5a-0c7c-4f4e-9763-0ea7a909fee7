// src/app/api/resumes/parse/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { resumes } from "@/lib/db/schema";
import mammoth from "mammoth";
import { db } from "@/lib/db";
import { gemini } from "@/lib/ai/gemini";

// Constants
const MAX_CONTENT_CHARS = 60000;
const SUPPORTED_FILE_TYPES = ["pdf", "docx", "doc", "txt"];

// --- Updated ResumeData Interface ---
interface ResumeData {
    name: string;
    email: string;
    phone: string;
    location?: string; // Added
    links?: {
        // Added structured links
        portfolio?: string;
        linkedIn?: string;
        github?: string;
        other?: string[]; // For personal websites, other profiles
    };
    summary: string;
    skills: string[];
    experience: Array<{
        title: string;
        company: string;
        dates: string;
        description: string;
        location?: string; // Optional location per job
    }>;
    education: Array<{
        institution: string;
        degree: string;
        field: string;
        dates: string;
        gpa?: string; // Added optional
        relevantCoursework?: string[]; // Added optional
        honors?: string; // Added optional (e.g., Cum Laude)
    }>;
    projects: Array<{
        name: string;
        description: string;
        dates?: string;
        technologies?: string[]; // Optional tech stack per project
        url?: string; // Optional link per project
    }>;
    certifications?: Array<{
        // Added
        name: string;
        authority?: string; // Issuing organization
        licenseNumber?: string;
        dates?: string; // Issue/Expiry date or valid range
    }>;
    awards?: Array<{
        // Added
        title: string;
        issuer?: string; // Who gave the award
        date?: string;
        description?: string;
    }>;
    publications?: Array<{
        // Added
        title: string;
        source?: string; // e.g., Journal Name, Conference Name
        date?: string;
        url?: string;
        description?: string;
    }>;
    languages?: Array<{
        // Added
        language: string;
        proficiency?: string; // e.g., Native, Fluent, Conversational
    }>;
    volunteerExperience?: Array<{
        // Added
        organization: string;
        role: string;
        dates?: string;
        description?: string;
    }>;
}
// --- End Updated ResumeData Interface ---

// Helper to ensure a value is an array
const ensureArray = (field: any): any[] => (Array.isArray(field) ? field : []);

// Extract file content based on file type (No changes needed here)
async function parseFileContent(
    fileBuffer: Buffer,
    fileType: string,
    fileUrl?: string
): Promise<string> {
    console.log(`Parsing file content of type: ${fileType}`);
    if (
        !fileType ||
        !SUPPORTED_FILE_TYPES.some((type) => fileType.includes(type))
    ) {
        console.log(`Unsupported file type detected: ${fileType}`);
        throw new Error(`Unsupported file type: ${fileType}`);
    }

    if (fileType.includes("pdf")) {
        console.log("Processing PDF content with PDF.co API");
        const apiKey = process.env.PDF_CO_KEY;
        if (!apiKey) {
            console.log("PDF.co API key missing in environment variables");
            throw new Error("PDF.co API key not configured");
        }
        if (!fileUrl) {
            console.log("File URL missing for PDF processing");
            throw new Error("File URL required for PDF.co");
        }

        console.log(`Sending PDF conversion request to PDF.co for: ${fileUrl}`);
        const convertResponse = await fetch(
            "https://api.pdf.co/v1/pdf/convert/to/text-simple",
            {
                method: "POST",
                headers: {
                    "x-api-key": apiKey,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    url: fileUrl,
                    inline: true,
                    async: false,
                }),
            }
        );

        console.log(`PDF.co response status: ${convertResponse.status}`);
        if (!convertResponse.ok) {
            const errorText = await convertResponse.text();
            console.log(
                `PDF.co conversion failed: ${convertResponse.statusText}, Response: ${errorText}`
            );
            throw new Error(`PDF.co text conversion failed: ${errorText}`);
        }
        const responseData = await convertResponse.json();
        console.log(
            `PDF text extracted, approximate length: ${
                responseData.body?.length || 0
            } chars`
        );
        const { body: extractedText } = responseData;
        return extractedText;
    } else if (fileType.includes("docx") || fileType.includes("doc")) {
        console.log("Processing Word document content");
        console.log(`Word document buffer size: ${fileBuffer.length} bytes`);
        console.log("Using mammoth to extract text");
        const result = await mammoth.extractRawText({ buffer: fileBuffer });
        console.log(
            `Word text extracted, length: ${result.value.length} chars`
        );
        return result.value;
    } else {
        // Assuming 'txt' or similar plain text
        console.log("Processing plain text content");
        console.log(`Text file buffer size: ${fileBuffer.length} bytes`);
        const text = fileBuffer.toString("utf-8");
        console.log(`Plain text extracted, length: ${text.length} chars`);
        return text;
    }
}

// --- Updated generatePrompt Function ---
function generatePrompt(content: string): string {
    console.log("Generating AI prompt with expanded fields");
    const prompt = `
    You are a highly skilled resume parser and senior recruiter AI. Your task is to meticulously extract information from the provided resume content and structure it into a valid JSON object.

    Extract the following fields. Be thorough and capture as much detail as possible. If a field or sub-field is genuinely absent in the resume, use null or omit it where appropriate within the JSON structure, but always attempt to provide the main fields.

    JSON Structure Fields:
    - name: Full name (string)
    - email: Email address (string)
    - phone: Phone number (string)
    - location: City, State, Country (string, e.g., "San Francisco, CA")
    - links: Object containing URLs for:
        - portfolio: URL (string, optional)
        - linkedIn: URL (string, optional)
        - github: URL (string, optional)
        - other: Array of other relevant URLs (string[], optional)
    - summary: Professional summary or objective (string)
    - skills: Array of skills (string[])
    - experience: Array of work experiences. Each object should contain:
        - title: Job title (string)
        - company: Company name (string)
        - dates: Employment dates (string)
        - description: Responsibilities and achievements (string)
        - location: Job location (string, optional)
    - education: Array of education entries. Each object should contain:
        - institution: School/University name (string)
        - degree: Degree obtained (string)
        - field: Field of study (string)
        - dates: Attendance dates (string)
        - gpa: GPA (string, optional)
        - relevantCoursework: Array of relevant courses (string[], optional)
        - honors: Academic honors like 'Cum Laude' (string, optional)
    - projects: Array of projects. Each object should contain:
        - name: Project name (string)
        - description: Project details (string)
        - dates: Project dates (string, optional)
        - technologies: Array of technologies used (string[], optional)
        - url: Link to the project (string, optional)
    - certifications: Array of certifications/licenses. Each object should contain:
        - name: Name of certification/license (string)
        - authority: Issuing organization (string, optional)
        - licenseNumber: License number (string, optional)
        - dates: Issue/expiry dates (string, optional)
    - awards: Array of awards/honors. Each object should contain:
        - title: Name of the award (string)
        - issuer: Organization that gave the award (string, optional)
        - date: Date received (string, optional)
        - description: Brief description (string, optional)
    - publications: Array of publications/presentations. Each object should contain:
        - title: Title of publication/presentation (string)
        - source: Journal, conference, etc. (string, optional)
        - date: Publication/presentation date (string, optional)
        - url: Link to the work (string, optional)
        - description: Brief description (string, optional)
    - languages: Array of languages spoken. Each object should contain:
        - language: Name of the language (string)
        - proficiency: Level of proficiency (string, e.g., "Native", "Fluent", "Conversational", optional)
    - volunteerExperience: Array of volunteer roles. Each object should contain:
        - organization: Name of the organization (string)
        - role: Your role/title (string)
        - dates: Dates of volunteering (string, optional)
        - description: Responsibilities/activities (string, optional)

    Resume content to parse:
    ${content}

    IMPORTANT INSTRUCTIONS:
    1. Return ONLY the raw, valid JSON object starting with '{' and ending with '}'.
    2. Do NOT include any markdown formatting like \`\`\`json or \`\`\`.
    3. Do NOT include any introductory or explanatory text before or after the JSON object.
    4. The entire response must be ONLY the JSON object itself.
    `.trim();
    console.log(`Generated prompt: ${prompt.substring(0, 150)}...`);
    return prompt;
}
// --- End Updated generatePrompt Function ---

// Parse resume using AI (No changes needed in the function logic itself, but it uses the updated prompt)
async function parseWithAI(
    fileContent: string
): Promise<Partial<ResumeData> | null> {
    // Return Partial<> as AI might miss fields
    console.log("Starting AI parsing");

    const truncatedContent = fileContent.slice(0, MAX_CONTENT_CHARS);
    console.log(`Truncated content length: ${truncatedContent.length} chars`);
    const prompt = generatePrompt(truncatedContent); // Uses the updated prompt generator

    console.log("Sending prompt to Gemini API");
    try {
        const responseText = await gemini.generateContent(prompt);
        console.log(
            `Received response from Gemini, length: ${responseText.length} chars`
        );

        let jsonString = responseText.trim();

        const firstBrace = jsonString.indexOf("{");
        const lastBrace = jsonString.lastIndexOf("}");

        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            jsonString = jsonString.substring(firstBrace, lastBrace + 1);
            console.log("Extracted potential JSON string between braces.");
        } else {
            console.log(
                "Could not reliably find JSON object braces. Attempting to parse trimmed response directly."
            );
        }

        try {
            const parsedData = JSON.parse(jsonString);
            console.log("Successfully parsed JSON from Gemini response.");

            if (!parsedData || typeof parsedData !== "object") {
                console.log(
                    "Gemini returned invalid data format after parsing."
                );
                return null;
            }
            // Return the partially parsed data, normalization will handle defaults
            return parsedData as Partial<ResumeData>;
        } catch (parseError) {
            console.error("Error parsing Gemini response as JSON:", parseError);
            console.log("Attempted to parse string:", jsonString);
            console.log("Original raw response:", responseText);
            return null;
        }
    } catch (error) {
        console.error("Error generating content with Gemini:", error);
        return null;
    }
}

// Basic resume parser (Fallback - kept simple, does not extract new fields)
async function basicResumeParser(
    content: string
): Promise<Partial<ResumeData>> {
    // Return Partial<>
    console.log("Using basic resume parser as fallback");
    const lines = content.split("\n").map((line) => line.trim());
    console.log(`Resume has ${lines.length} lines`);
    const EMAIL_REGEX = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
    const PHONE_REGEX =
        /\b(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b/;

    const emailMatch = content.match(EMAIL_REGEX);
    const phoneMatch = content.match(PHONE_REGEX);

    const email = emailMatch?.[0] || null; // Use null if not found
    const phone = phoneMatch?.[0] || null; // Use null if not found
    console.log(`Extracted email: ${email}, phone: ${phone}`);

    let name: string | null = null;
    for (const line of lines) {
        if (
            line &&
            (!email || !line.includes(email)) &&
            (!phone || !line.includes(phone))
        ) {
            if (
                line.length < 50 &&
                line.length > 2 && // Basic name length check
                !/\d/.test(line) && // No digits usually in names
                !line.toLowerCase().includes("experience") &&
                !line.toLowerCase().includes("education") &&
                !line.toLowerCase().includes("skills") &&
                !line.toLowerCase().includes("summary") &&
                !line.toLowerCase().includes("project") &&
                !line.toLowerCase().includes("profile") &&
                !line.toLowerCase().includes("linkedin") &&
                !line.toLowerCase().includes("github") &&
                !line.toLowerCase().includes("portfolio") &&
                line.split(" ").length < 5 // Avoid longer sentences
            ) {
                name = line;
                console.log(`Extracted name (basic): ${name}`);
                break;
            }
        }
    }

    // Return partial data, normalization will handle the rest
    return {
        name: name ?? undefined, // Use undefined if null
        email: email ?? undefined,
        phone: phone ?? undefined,
    };
}

// --- Updated normalizeResumeData Function ---
function normalizeResumeData(data: Partial<ResumeData> | null): ResumeData {
    console.log("Normalizing resume data with expanded fields");
    const inputData = data || {}; // Handle null input gracefully
    console.log(`Input data keys: ${Object.keys(inputData).join(", ")}`);

    // Normalize experience array items
    const normalizedExperience = ensureArray(inputData.experience)
        .map((exp: any) => ({
            title: exp?.title || "",
            company: exp?.company || "",
            dates: exp?.dates || "",
            description: exp?.description || "",
            location: exp?.location || undefined, // Keep optional string fields as undefined if empty/null
        }))
        .filter((exp) => exp.title || exp.company); // Keep only if title or company exists

    // Normalize education array items
    const normalizedEducation = ensureArray(inputData.education)
        .map((edu: any) => ({
            institution: edu?.institution || "",
            degree: edu?.degree || "",
            field: edu?.field || "",
            dates: edu?.dates || "",
            gpa: edu?.gpa || undefined,
            relevantCoursework: ensureArray(edu?.relevantCoursework).filter(
                (c) => typeof c === "string" && c.trim()
            ), // Ensure strings and filter empty
            honors: edu?.honors || undefined,
        }))
        .filter((edu) => edu.institution || edu.degree); // Keep only if institution or degree exists

    // Normalize projects array items
    const normalizedProjects = ensureArray(inputData.projects)
        .map((proj: any) => ({
            name: proj?.name || "",
            description: proj?.description || "",
            dates: proj?.dates || undefined,
            technologies: ensureArray(proj?.technologies).filter(
                (t) => typeof t === "string" && t.trim()
            ), // Ensure strings and filter empty
            url: proj?.url || undefined,
        }))
        .filter((proj) => proj.name || proj.description); // Keep only if name or description exists

    // Normalize certifications array items
    const normalizedCertifications = ensureArray(inputData.certifications)
        .map((cert: any) => ({
            name: cert?.name || "",
            authority: cert?.authority || undefined,
            licenseNumber: cert?.licenseNumber || undefined,
            dates: cert?.dates || undefined,
        }))
        .filter((cert) => cert.name); // Keep only if name exists

    // Normalize awards array items
    const normalizedAwards = ensureArray(inputData.awards)
        .map((award: any) => ({
            title: award?.title || "",
            issuer: award?.issuer || undefined,
            date: award?.date || undefined,
            description: award?.description || undefined,
        }))
        .filter((award) => award.title); // Keep only if title exists

    // Normalize publications array items
    const normalizedPublications = ensureArray(inputData.publications)
        .map((pub: any) => ({
            title: pub?.title || "",
            source: pub?.source || undefined,
            date: pub?.date || undefined,
            url: pub?.url || undefined,
            description: pub?.description || undefined,
        }))
        .filter((pub) => pub.title); // Keep only if title exists

    // Normalize languages array items
    const normalizedLanguages = ensureArray(inputData.languages)
        .map((lang: any) => ({
            language: lang?.language || "",
            proficiency: lang?.proficiency || undefined,
        }))
        .filter((lang) => lang.language); // Keep only if language exists

    // Normalize volunteer experience array items
    const normalizedVolunteerExperience = ensureArray(
        inputData.volunteerExperience
    )
        .map((vol: any) => ({
            organization: vol?.organization || "",
            role: vol?.role || "",
            dates: vol?.dates || undefined,
            description: vol?.description || undefined,
        }))
        .filter((vol) => vol.organization || vol.role); // Keep only if org or role exists

    // Normalize skills array (ensure strings and filter empty)
    const normalizedSkills = ensureArray(inputData.skills).filter(
        (skill) => typeof skill === "string" && skill.trim() !== ""
    );

    // Normalize links object
    const normalizedLinks = {
        portfolio: inputData.links?.portfolio || undefined,
        linkedIn: inputData.links?.linkedIn || undefined,
        github: inputData.links?.github || undefined,
        other: ensureArray(inputData.links?.other).filter(
            (l) => typeof l === "string" && l.trim()
        ), // Ensure strings and filter empty
    };
    // If the whole links object is effectively empty, make it undefined
    const isEmptyLinks =
        !normalizedLinks.portfolio &&
        !normalizedLinks.linkedIn &&
        !normalizedLinks.github &&
        normalizedLinks.other.length === 0;

    // Construct the final, normalized object according to the ResumeData interface
    const finalData: ResumeData = {
        name: inputData.name || "",
        email: inputData.email || "",
        phone: inputData.phone || "",
        location: inputData.location || undefined,
        links: isEmptyLinks ? undefined : normalizedLinks,
        summary: inputData.summary || "",
        skills: normalizedSkills,
        experience: normalizedExperience,
        education: normalizedEducation,
        projects: normalizedProjects,
        certifications:
            normalizedCertifications.length > 0
                ? normalizedCertifications
                : undefined,
        awards: normalizedAwards.length > 0 ? normalizedAwards : undefined,
        publications:
            normalizedPublications.length > 0
                ? normalizedPublications
                : undefined,
        languages:
            normalizedLanguages.length > 0 ? normalizedLanguages : undefined,
        volunteerExperience:
            normalizedVolunteerExperience.length > 0
                ? normalizedVolunteerExperience
                : undefined,
    };

    console.log("Normalization complete.");
    // console.log("Normalized Data:", JSON.stringify(finalData, null, 2)); // Optional: Log normalized data for debugging
    return finalData;
}
// --- End Updated normalizeResumeData Function ---

// Validate resumeId (No changes needed)
function validateResumeId(resumeId: string): number {
    console.log(`Validating resumeId: ${resumeId}`);
    const id = parseInt(resumeId, 10);
    if (isNaN(id) || id <= 0) {
        console.log("Invalid resumeId detected");
        throw new Error("Invalid resumeId: must be a positive integer");
    }
    return id;
}

// API Handler (Main logic remains similar, uses updated normalization)
export async function POST(req: NextRequest) {
    try {
        const { userId } = await auth();
        if (!userId) {
            console.log("Unauthorized access attempt: No userId");
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }
        console.log(`POST request to parse resume from user: ${userId}`);

        let body: { resumeId: string };
        try {
            body = await req.json();
            if (!body.resumeId) {
                console.log("Missing resumeId in request body");
                throw new Error("Missing resumeId");
            }
            console.log(`Request body received: ${JSON.stringify(body)}`);
        } catch (error) {
            console.log("Invalid request body:", error);
            return NextResponse.json(
                { error: "Invalid request body or missing resumeId" },
                { status: 400 }
            );
        }

        const resumeId = validateResumeId(body.resumeId);
        console.log(`Processing resume ID: ${resumeId}`);

        // --- Inner try-catch for DB/File/Parsing operations ---
        try {
            console.log("Fetching resume from database");
            const [resume] = await db
                .select()
                .from(resumes)
                .where(eq(resumes.id, resumeId))
                .limit(1);

            if (!resume) {
                console.log(`Resume ID ${resumeId} not found in DB`);
                return NextResponse.json(
                    { error: "Resume not found" },
                    { status: 404 }
                );
            }
            console.log(
                `Resume fetched: ID=${resume.id}, UserID=${resume.userId}, FileType=${resume.fileType}`
            );

            // Authorization check
            if (resume.userId !== userId) {
                console.log(
                    `Forbidden: User ${userId} accessing resume ${resumeId} owned by ${resume.userId}`
                );
                return NextResponse.json(
                    { error: "Forbidden" },
                    { status: 403 }
                );
            }

            // Check if already parsed and valid
            if (
                resume.parsedContent &&
                typeof resume.parsedContent === "object" &&
                Object.keys(resume.parsedContent).length > 0 &&
                (resume.parsedContent as ResumeData).name // Basic validity check
            ) {
                console.log(
                    `Returning cached parsed content for resume ${resumeId}`
                );
                // Ensure cached data conforms to the LATEST schema via normalization before returning
                const normalizedCachedData = normalizeResumeData(
                    resume.parsedContent as Partial<ResumeData>
                );
                return NextResponse.json({
                    parsedContent: normalizedCachedData,
                });
            }

            if (!resume.fileUrl) {
                console.error(`Resume ${resumeId} is missing fileUrl.`);
                throw new Error(`File URL is missing for resume ${resumeId}`);
            }

            console.log(`Downloading resume file from: ${resume.fileUrl}`);
            const response = await fetch(resume.fileUrl);
            if (!response.ok) {
                console.log(
                    `Failed to fetch file: ${response.status} ${response.statusText}`
                );
                throw new Error(`Failed to fetch file: ${response.statusText}`);
            }

            const fileBuffer = Buffer.from(await response.arrayBuffer());
            console.log("File downloaded, parsing content...");
            const fileContent = await parseFileContent(
                fileBuffer,
                resume.fileType || "",
                resume.fileUrl
            );

            // Handle empty extracted content
            if (!fileContent || fileContent.trim().length === 0) {
                console.warn(
                    `File content for resume ${resumeId} is empty after parsing.`
                );
                const basicData = await basicResumeParser(""); // Get basic structure
                const normalizedEmptyData = normalizeResumeData(basicData); // Normalize even basic data
                await db
                    .update(resumes)
                    .set({
                        parsedContent: normalizedEmptyData,
                        updatedAt: new Date(),
                    })
                    .where(eq(resumes.id, resumeId));
                console.log(
                    `DB updated with basic parsed data for empty content, resume ID: ${resumeId}`
                );
                return NextResponse.json({
                    parsedContent: normalizedEmptyData,
                });
            }

            console.log("Attempting AI parsing...");
            let parsedData: Partial<ResumeData> | null = await parseWithAI(
                fileContent
            );

            if (!parsedData) {
                console.log(
                    "AI parsing failed/returned null, falling back to basic parser"
                );
                parsedData = await basicResumeParser(fileContent); // Use fallback
            }

            console.log("Normalizing parsed data before saving...");
            // Always normalize before saving, whether from AI or basic parser
            const normalizedData = normalizeResumeData(parsedData);

            console.log("Updating database with normalized parsed content...");
            await db
                .update(resumes)
                .set({ parsedContent: normalizedData, updatedAt: new Date() })
                .where(eq(resumes.id, resumeId));

            console.log(`Database update complete for resume ID: ${resumeId}`);
            return NextResponse.json({ parsedContent: normalizedData });
        } catch (error: any) {
            // Catch errors from DB/File/Parsing steps
            console.error(`Error processing resume ID ${resumeId}:`, error);
            let status = 500;
            let message = "Internal server error during resume processing";
            if (error.message?.includes("not found")) status = 404;
            if (error.message?.includes("Forbidden")) status = 403;
            if (error.message?.includes("Unsupported file type")) status = 400;
            if (
                error.message?.includes("fetch file") ||
                error.message?.includes("PDF.co") ||
                error.message?.includes("mammoth")
            )
                status = 502; // Bad Gateway or dependency failure

            return NextResponse.json(
                { error: message, details: error.message || "Unknown error" },
                { status: status }
            );
        }
    } catch (error: any) {
        // Catch broader errors (auth, request parsing, unhandled)
        console.error("Unhandled error in resume parsing endpoint:", error);
        return NextResponse.json(
            { error: "An unexpected error occurred." },
            { status: 500 }
        );
    }
}
