import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes, users } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { getOrCreateUser } from "@/lib/auth";
import { currentUser } from "@clerk/nextjs/server";

export async function POST(req: NextRequest) {
    try {
        // Get auth info from both sources to be robust
        const { userId } = getAuth(req);
        const user = await currentUser();
        const authUserId = userId || user?.id;

        console.log("Auth userId:", userId);
        console.log("Current user id:", user?.id);

        // More detailed logging of headers for debugging
        console.log(
            "Request headers:",
            Object.fromEntries(req.headers.entries())
        );

        if (!authUserId) {
            console.log("Unauthorized: No userId found in any source");
            return NextResponse.json(
                { error: "Unauthorized - Please sign in again" },
                { status: 401 }
            );
        }

        const body = await req.json();
        console.log("Request body:", body);

        const { name, fileUrl, filePath, fileType, isDefault } = body;

        if (!name || !fileUrl || !fileType) {
            console.log("Missing required fields:", {
                name,
                fileUrl,
                fileType,
            });
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        // Check if user exists in the database
        const existingUser = await db
            .select()
            .from(users)
            .where(eq(users.id, authUserId))
            .limit(1);

        // If user doesn't exist, create a new user record
        if (existingUser.length === 0) {
            console.log("User not found in database, creating new user record");

            // Get user from Clerk
            // Use the already fetched user object if available
            try {
                if (user) {
                    await getOrCreateUser(
                        authUserId,
                        user.emailAddresses[0]?.emailAddress ||
                            `${authUserId}@temporary.com`,
                        user.firstName || "",
                        user.lastName || "",
                        user.imageUrl || ""
                    );
                } else {
                    // Fallback if user details weren't fetched yet
                    await db.insert(users).values({
                        id: authUserId,
                        email: `${authUserId}@temporary.com`, // Temporary email
                        firstName: null,
                        lastName: null,
                        profileImage: null,
                    });
                }
            } catch (error) {
                console.error("Error creating user:", error);
                return NextResponse.json(
                    { error: "Failed to create user" },
                    { status: 500 }
                );
            }
        } else {
            console.log("User found in database:", existingUser[0]);
        }

        console.log("Creating resume:", {
            name,
            fileUrl,
            filePath,
            fileType,
            isDefault,
        });

        // If this resume is set as default, update all other resumes to not be default
        if (isDefault) {
            await db
                .update(resumes)
                .set({ isDefault: false })
                .where(eq(resumes.userId, authUserId));
        }

        // Insert the new resume
        try {
            const [newResume] = await db
                .insert(resumes)
                .values({
                    userId: authUserId,
                    name,
                    fileUrl,
                    filePath,
                    fileType,
                    isDefault: isDefault || false,
                })
                .returning();

            console.log("Resume created successfully:", newResume);
            return NextResponse.json(newResume);
        } catch (dbError) {
            console.error("Database error:", dbError);
            return NextResponse.json(
                { error: "Database error", details: String(dbError) },
                { status: 500 }
            );
        }
    } catch (error) {
        console.error("Error creating resume:", error);
        return NextResponse.json(
            { error: "Internal server error", details: String(error) },
            { status: 500 }
        );
    }
}

export async function GET(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        console.log("GET resumes - Auth userId:", userId);

        if (!userId) {
            console.log("Unauthorized: No userId found");
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Check if user exists in the database
        const existingUser = await db
            .select()
            .from(users)
            .where(eq(users.id, userId))
            .limit(1);

        // If user doesn't exist, create a new user record
        if (existingUser.length === 0) {
            console.log(
                "User not found in database for GET request, creating new user record"
            );

            // Get user from Clerk
            const user = await currentUser();

            if (
                !user ||
                !user.emailAddresses ||
                user.emailAddresses.length === 0
            ) {
                console.log(
                    "Failed to get user details from Clerk for GET request"
                );

                // Create a temporary user record with minimal information
                try {
                    await db.insert(users).values({
                        id: userId,
                        email: `${userId}@temporary.com`, // Temporary email
                        firstName: null,
                        lastName: null,
                        profileImage: null,
                    });
                    console.log(
                        "Created temporary user record for GET request"
                    );
                } catch (userError) {
                    console.error(
                        "Error creating temporary user for GET request:",
                        userError
                    );
                    // Continue anyway to try to get resumes
                }
            } else {
                // Create user with Clerk information
                const primaryEmail = user.emailAddresses[0].emailAddress;
                try {
                    await db.insert(users).values({
                        id: userId,
                        email: primaryEmail,
                        firstName: user.firstName || null,
                        lastName: user.lastName || null,
                        profileImage: user.imageUrl || null,
                    });
                    console.log(
                        "Created user record with Clerk information for GET request"
                    );
                } catch (userError) {
                    console.error(
                        "Error creating user with Clerk info for GET request:",
                        userError
                    );
                    // Continue anyway to try to get resumes
                }
            }
        } else {
            console.log(
                "User found in database for GET request:",
                existingUser[0]
            );
        }

        // Get all resumes for the user
        const userResumes = await db
            .select()
            .from(resumes)
            .where(eq(resumes.userId, userId))
            .orderBy(desc(resumes.createdAt));

        console.log(`Found ${userResumes.length} resumes for user ${userId}`);
        return NextResponse.json(userResumes);
    } catch (error) {
        console.error("Error fetching resumes:", error);
        return NextResponse.json(
            { error: "Internal server error", details: String(error) },
            { status: 500 }
        );
    }
}
