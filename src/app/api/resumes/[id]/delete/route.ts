import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes, jobApplications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { UTApi } from "uploadthing/server";

// Initialize UploadThing API
const utapi = new UTApi();

// Helper function to extract file key from UploadThing URL
function extractFileKey(url: string): string | null {
    try {
        // UploadThing URLs typically follow this pattern:
        // https://utfs.io/f/[fileKey]
        const urlParts = url.split("/");
        const fileKey = urlParts[urlParts.length - 1];
        console.log(
            `[DELETE_RESUME] Extracted file key: ${fileKey} from URL: ${url}`
        );
        return fileKey || null;
    } catch (error) {
        console.error(
            "[DELETE_RESUME] Error extracting file key from URL:",
            error
        );
        return null;
    }
}

export async function DELETE(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

    console.log(
        `[DELETE_RESUME:${requestId}] === Starting resume deletion process ===`
    );
    console.log(`[DELETE_RESUME:${requestId}] Request method: ${req.method}`);
    console.log(`[DELETE_RESUME:${requestId}] Request URL: ${req.url}`);
    console.log(
        `[DELETE_RESUME:${requestId}] User-Agent: ${req.headers.get(
            "user-agent"
        )}`
    );

    const { userId } = getAuth(req);

    if (!userId) {
        console.warn(
            `[DELETE_RESUME:${requestId}] Unauthorized access attempt - no userId found`
        );
        return NextResponse.json(
            {
                error: "Unauthorized",
                message: "You must be logged in to delete a resume",
            },
            { status: 401 }
        );
    }

    console.log(`[DELETE_RESUME:${requestId}] Authenticated user: ${userId}`);

    try {
        const { id } = await params;
        const resumeId = parseInt(id);

        console.log(`[DELETE_RESUME:${requestId}] Received resume ID: ${id}`);
        console.log(
            `[DELETE_RESUME:${requestId}] Parsed resume ID: ${resumeId}`
        );

        if (isNaN(resumeId)) {
            console.error(
                `[DELETE_RESUME:${requestId}] Invalid resume ID provided: ${id}`
            );
            return NextResponse.json(
                {
                    error: "Invalid resume ID",
                    message: "Resume ID must be a valid number",
                },
                { status: 400 }
            );
        }

        console.log(
            `[DELETE_RESUME:${requestId}] Starting database query to fetch resume...`
        );
        const dbQueryStart = Date.now();

        // Get the resume to check ownership and get file information
        const [resume] = await db
            .select()
            .from(resumes)
            .where(and(eq(resumes.id, resumeId), eq(resumes.userId, userId)));

        const dbQueryTime = Date.now() - dbQueryStart;
        console.log(
            `[DELETE_RESUME:${requestId}] Database query completed in ${dbQueryTime}ms`
        );

        if (!resume) {
            console.warn(
                `[DELETE_RESUME:${requestId}] Resume not found or access denied - resumeId: ${resumeId}, userId: ${userId}`
            );
            return NextResponse.json(
                {
                    error: "Resume not found",
                    message:
                        "Resume not found or you don't have permission to delete it",
                },
                { status: 404 }
            );
        }

        console.log(`[DELETE_RESUME:${requestId}] Resume found:`, {
            id: resume.id,
            name: resume.name,
            fileUrl: resume.fileUrl,
            filePath: resume.filePath,
            fileType: resume.fileType,
            isDefault: resume.isDefault,
            createdAt: resume.createdAt,
        });

        // Handle database operations sequentially (since neon-http doesn't support transactions)
        console.log(
            `[DELETE_RESUME:${requestId}] Starting database cleanup operations...`
        );
        const dbOperationsStart = Date.now();

        try {
            // First, update job applications to remove the resume reference
            console.log(
                `[DELETE_RESUME:${requestId}] Step 1: Updating job applications to remove resume reference...`
            );
            const jobAppsUpdateStart = Date.now();

            const jobAppsUpdateResult = await db
                .update(jobApplications)
                .set({ resumeId: null })
                .where(eq(jobApplications.resumeId, resumeId));

            const jobAppsUpdateTime = Date.now() - jobAppsUpdateStart;
            console.log(
                `[DELETE_RESUME:${requestId}] Job applications updated in ${jobAppsUpdateTime}ms`
            );

            // Then delete the resume from database
            console.log(
                `[DELETE_RESUME:${requestId}] Step 2: Deleting resume from database...`
            );
            const resumeDeleteStart = Date.now();

            await db.delete(resumes).where(eq(resumes.id, resumeId));

            const resumeDeleteTime = Date.now() - resumeDeleteStart;
            const totalDbTime = Date.now() - dbOperationsStart;
            console.log(
                `[DELETE_RESUME:${requestId}] Resume deleted from database in ${resumeDeleteTime}ms`
            );
            console.log(
                `[DELETE_RESUME:${requestId}] Total database operations completed in ${totalDbTime}ms`
            );
        } catch (dbError) {
            console.error(
                `[DELETE_RESUME:${requestId}] Database operation failed:`,
                {
                    error: dbError,
                    errorMessage:
                        dbError instanceof Error
                            ? dbError.message
                            : "Unknown error",
                    errorStack:
                        dbError instanceof Error ? dbError.stack : undefined,
                    resumeId,
                    userId,
                }
            );
            throw new Error(
                `Database operation failed: ${
                    dbError instanceof Error
                        ? dbError.message
                        : "Unknown database error"
                }`
            );
        }

        // Delete the file from UploadThing storage (after successful DB operations)
        console.log(
            `[DELETE_RESUME:${requestId}] Starting file deletion from UploadThing storage...`
        );
        const fileDeleteStart = Date.now();
        let fileDeleted = false;
        const fileDeletionErrors: string[] = [];

        try {
            // Try using the stored file path first
            if (resume.filePath) {
                console.log(
                    `[DELETE_RESUME:${requestId}] Attempting deletion using stored filePath: ${resume.filePath}`
                );
                const filePathDeleteStart = Date.now();

                await utapi.deleteFiles(resume.filePath);

                const filePathDeleteTime = Date.now() - filePathDeleteStart;
                fileDeleted = true;
                console.log(
                    `[DELETE_RESUME:${requestId}] Successfully deleted file using filePath in ${filePathDeleteTime}ms: ${resume.filePath}`
                );
            } else if (resume.fileUrl) {
                console.log(
                    `[DELETE_RESUME:${requestId}] No filePath found, attempting to extract key from fileUrl: ${resume.fileUrl}`
                );

                // Extract file key from URL as fallback
                const fileKey = extractFileKey(resume.fileUrl);
                if (fileKey) {
                    console.log(
                        `[DELETE_RESUME:${requestId}] Attempting deletion using extracted key: ${fileKey}`
                    );
                    const keyDeleteStart = Date.now();

                    await utapi.deleteFiles(fileKey);

                    const keyDeleteTime = Date.now() - keyDeleteStart;
                    fileDeleted = true;
                    console.log(
                        `[DELETE_RESUME:${requestId}] Successfully deleted file using extracted key in ${keyDeleteTime}ms: ${fileKey}`
                    );
                } else {
                    const errorMsg = "Could not extract file key from URL";
                    console.error(
                        `[DELETE_RESUME:${requestId}] ${errorMsg}: ${resume.fileUrl}`
                    );
                    fileDeletionErrors.push(errorMsg);
                }
            } else {
                const errorMsg = "No file path or URL found for resume";
                console.error(`[DELETE_RESUME:${requestId}] ${errorMsg}`, {
                    resumeId,
                    resumeData: resume,
                });
                fileDeletionErrors.push(errorMsg);
            }
        } catch (deleteError) {
            const fileDeleteTime = Date.now() - fileDeleteStart;
            console.error(
                `[DELETE_RESUME:${requestId}] Error deleting file from UploadThing after ${fileDeleteTime}ms:`,
                {
                    error: deleteError,
                    errorMessage:
                        deleteError instanceof Error
                            ? deleteError.message
                            : "Unknown error",
                    errorStack:
                        deleteError instanceof Error
                            ? deleteError.stack
                            : undefined,
                    filePath: resume.filePath,
                    fileUrl: resume.fileUrl,
                    resumeId,
                }
            );
            fileDeletionErrors.push(
                `Storage deletion failed: ${
                    deleteError instanceof Error
                        ? deleteError.message
                        : "Unknown error"
                }`
            );
        }

        const totalFileDeleteTime = Date.now() - fileDeleteStart;
        const totalRequestTime = Date.now() - startTime;

        console.log(
            `[DELETE_RESUME:${requestId}] File deletion process completed in ${totalFileDeleteTime}ms`
        );
        console.log(
            `[DELETE_RESUME:${requestId}] Total request processing time: ${totalRequestTime}ms`
        );

        // Return success response with file deletion status
        const response = {
            success: true,
            message: "Resume deleted successfully",
            fileDeleted,
            metadata: {
                requestId,
                processingTimeMs: totalRequestTime,
                resumeId,
                userId,
            },
            ...(fileDeletionErrors.length > 0 && {
                warnings: fileDeletionErrors,
                note: "Resume data was deleted but file cleanup may have failed",
            }),
        };

        console.log(
            `[DELETE_RESUME:${requestId}] === Resume deletion completed successfully ===`,
            {
                fileDeleted,
                warnings:
                    fileDeletionErrors.length > 0 ? fileDeletionErrors : "none",
                totalTime: totalRequestTime,
            }
        );

        return NextResponse.json(response, { status: 200 });
    } catch (error) {
        const totalRequestTime = Date.now() - startTime;
        console.error(
            `[DELETE_RESUME:${requestId}] === Resume deletion failed after ${totalRequestTime}ms ===`,
            {
                error,
                errorMessage:
                    error instanceof Error ? error.message : "Unknown error",
                errorStack: error instanceof Error ? error.stack : undefined,
                userId,
                requestUrl: req.url,
            }
        );

        // If it's a database error, provide more specific messaging
        const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";

        return NextResponse.json(
            {
                error: "Internal server error",
                message: "Failed to delete resume. Please try again later.",
                metadata: {
                    requestId,
                    processingTimeMs: totalRequestTime,
                },
                details:
                    process.env.NODE_ENV === "development"
                        ? errorMessage
                        : undefined,
            },
            { status: 500 }
        );
    }
}

// For backward compatibility, keep the POST method
export async function POST(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    console.log(
        "[DELETE_RESUME] POST method called - redirecting to DELETE handler"
    );
    return DELETE(req, { params });
}
