import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        const { id } = await params;
        if (!id) {
            return NextResponse.json(
                { error: "Missing resume ID" },
                { status: 400 }
            );
        }

        const resumeId = parseInt(id);
        if (isNaN(resumeId)) {
            return NextResponse.json(
                { error: "Invalid resume ID" },
                { status: 400 }
            );
        }

        const [resume] = await db
            .select()
            .from(resumes)
            .where(eq(resumes.id, resumeId))
            .limit(1);

        if (!resume) {
            return NextResponse.json(
                { error: "Resume not found" },
                { status: 404 }
            );
        }

        if (resume.userId !== userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        return NextResponse.json(resume);
    } catch (error) {
        console.error("Error fetching resume:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
