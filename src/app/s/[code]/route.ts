import { NextRequest, NextResponse } from "next/server";
import { getOriginalUrl } from "@/lib/url-shortener";
import { z } from "zod";

// Define validation schema for route parameter
const RouteParamSchema = z.object({
    code: z
        .string()
        .trim()
        .min(1, "Invalid URL code")
        .max(10, "Invalid URL code"),
});

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ code: string }> }
) {
    try {
        // Get code from params
        const code = (await params).code;

        // Validate the code parameter
        const result = RouteParamSchema.safeParse({ code });

        if (!result.success) {
            console.error("Invalid URL code format:", code);
            return NextResponse.redirect(new URL("/404", request.url));
        }

        // Get the sanitized code
        const validatedCode = result.data.code;

        // Get original URL
        const originalUrl = await getOriginalUrl(validatedCode);

        if (!originalUrl) {
            return NextResponse.redirect(new URL("/404", request.url));
        }

        // Validate the original URL is actually a valid URL
        try {
            new URL(originalUrl);
        } catch (e) {
            console.error(
                "Invalid original URL stored in database:",
                originalUrl
            );
            return NextResponse.redirect(new URL("/404", request.url));
        }

        return NextResponse.redirect(originalUrl);
    } catch (error) {
        console.error("Error redirecting from short URL:", error);
        return NextResponse.redirect(new URL("/404", request.url));
    }
}
