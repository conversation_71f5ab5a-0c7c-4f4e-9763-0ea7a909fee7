import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
    return {
        rules: {
            userAgent: "*",
            allow: "/",
            disallow: [
                "/dashboard/", // Protect user data
                "/api/", // Protect API routes
                "/admin/", // Protect admin routes
                "/*.json$", // Protect JSON files
                "/*.xml$", // Protect XML files
            ],
        },
        sitemap: "https://hirerizz.xyz/sitemap.xml",
    };
}
