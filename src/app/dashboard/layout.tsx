import { redirect } from "next/navigation";
import { Suspense } from "react";
import { getServerAuth } from "@/lib/auth";
import { Navigation } from "@/components/navigation";
import { DashboardHeader } from "@/components/dashboard-header";
import { CyberpunkBackground } from "@/components/ui/cyberpunk-background";
import { DashboardLoadingState } from "@/components/dashboard-loading-state";

function NavSkeleton() {
    return (
        <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-3/4 mb-4"></div>
            <div className="h-8 bg-muted rounded w-1/2 mb-4"></div>
            <div className="h-8 bg-muted rounded w-2/3 mb-4"></div>
        </div>
    );
}

export default async function DashboardLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const auth = await getServerAuth();

    if (!auth.userId) {
        redirect("/sign-in");
    }
    return (
        <div className="flex min-h-screen flex-col bg-[hsl(var(--background))] text-foreground relative">
            <Suspense fallback={null}>
                <CyberpunkBackground variant="subtle" showGlow={true} />
            </Suspense>
            <DashboardHeader />
            <div className="container flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10 relative z-10">
                <aside className="fixed top-14 z-30 -ml-2 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 md:sticky md:block">
                    <div className="h-full py-6 pr-6 lg:py-8">
                        <Suspense fallback={<NavSkeleton />}>
                            <Navigation />
                        </Suspense>
                    </div>
                </aside>
                <main className="flex w-full flex-col overflow-hidden py-6">
                    <Suspense fallback={<DashboardLoadingState />}>
                        {children}
                    </Suspense>
                </main>
            </div>
        </div>
    );
}
