import { Suspense } from "react";
import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { eq } from "drizzle-orm";
import { jobApplications } from "@/lib/db/schema";
import { count, desc } from "drizzle-orm";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
    BarChart3,
    FileText,
    Briefcase,
    Calendar,
    PlusCircle,
    ArrowRight,
} from "lucide-react";
import { getServerAuth } from "@/lib/auth";
import { formatDate } from "date-fns";
import dynamic from "next/dynamic";
import { Skeleton } from "@/components/ui/skeleton";
import { ExportApplicationsButton } from "@/components/export-applications-button";
import ApplicationFlow from "@/components/dashboard/ApplicationFlow";
import ApplicationFlowWithNavigation from "@/components/dashboard/ApplicationFlowWithNavigation";

// Dynamically import components that are below the fold
const ApplicationStatusVisual = dynamic(
    () => import("@/components/application-status-visual"),
    {
        ssr: true,
        loading: () => <Skeleton className="h-40 w-full" />,
    }
);

// Define the application status type to fix type errors
type ApplicationStatus =
    | "not_applied"
    | "applied"
    | "interviewing"
    | "offer"
    | "rejected"
    | "accepted";

// Define application types
interface Application {
    id: number;
    status: string | null;
}

// This allows for independent data loading
async function getApplicationStats(userId: string) {
    return db
        .select({
            status: jobApplications.status,
            count: count(),
        })
        .from(jobApplications)
        .where(eq(jobApplications.userId, userId))
        .groupBy(jobApplications.status);
}

async function getRecentApplications(userId: string) {
    return db
        .select({
            id: jobApplications.id,
            company: jobApplications.company,
            position: jobApplications.position,
            status: jobApplications.status,
            appliedDate: jobApplications.appliedDate,
            createdAt: jobApplications.createdAt,
        })
        .from(jobApplications)
        .where(eq(jobApplications.userId, userId))
        .orderBy(desc(jobApplications.createdAt))
        .limit(5);
}

function StatsOverview({ applicationStats }: { applicationStats: any[] }) {
    // Calculate total applications
    const totalApplications = applicationStats.reduce(
        (sum, stat) => sum + Number(stat.count),
        0
    );

    // Prepare data for status counts
    const statusCounts: Record<ApplicationStatus, number> = {
        not_applied: 0,
        applied: 0,
        interviewing: 0,
        offer: 0,
        rejected: 0,
        accepted: 0,
    };

    applicationStats.forEach((stat) => {
        if (stat.status && Object.keys(statusCounts).includes(stat.status)) {
            statusCounts[stat.status as ApplicationStatus] = Number(stat.count);
        }
    });

    return (
        <div className="mb-6 sm:mb-8 grid gap-3 sm:gap-6 grid-cols-2 sm:grid-cols-2 lg:grid-cols-4">
            <div className="rounded-xl border bg-card p-3 sm:p-6 shadow-md transition-all hover:shadow-lg hover:border-primary/30">
                <div className="flex items-center gap-2">
                    <div className="rounded-full bg-primary/10 p-1.5 sm:p-2">
                        <Briefcase className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                    </div>
                    <h3 className="text-xs sm:text-sm font-medium">
                        Total Applications
                    </h3>
                </div>
                <p className="mt-2 text-xl sm:text-3xl font-bold text-gradient-purple-blue">
                    {totalApplications}
                </p>
            </div>
            <div className="rounded-xl border bg-card p-3 sm:p-6 shadow-md transition-all hover:shadow-lg hover:border-secondary/30">
                <div className="flex items-center gap-2">
                    <div className="rounded-full bg-secondary/10 p-1.5 sm:p-2">
                        <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-secondary" />
                    </div>
                    <h3 className="text-xs sm:text-sm font-medium">Applied</h3>
                </div>
                <p className="mt-2 text-xl sm:text-3xl font-bold text-gradient-pink-cyan">
                    {statusCounts.applied}
                </p>
            </div>
            <div className="rounded-xl border bg-card p-3 sm:p-6 shadow-md transition-all hover:shadow-lg hover:border-accent/30">
                <div className="flex items-center gap-2">
                    <div className="rounded-full bg-accent/10 p-1.5 sm:p-2">
                        <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-accent" />
                    </div>
                    <h3 className="text-xs sm:text-sm font-medium">
                        Interviewing
                    </h3>
                </div>
                <p className="mt-2 text-xl sm:text-3xl font-bold text-gradient-cyan-purple">
                    {statusCounts.interviewing}
                </p>
            </div>
            <div className="rounded-xl border bg-card p-3 sm:p-6 shadow-md transition-all hover:shadow-lg hover:border-primary/30">
                <div className="flex items-center gap-2">
                    <div className="rounded-full bg-primary/10 p-1.5 sm:p-2">
                        <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                    </div>
                    <h3 className="text-xs sm:text-sm font-medium">Offers</h3>
                </div>
                <p className="mt-2 text-xl sm:text-3xl font-bold text-gradient-purple-blue">
                    {statusCounts.offer}
                </p>
            </div>
        </div>
    );
}

// ... existing code ...

// The rest of your component implementation
export default async function DashboardPage() {
    const auth = await getServerAuth();
    const user = await currentUser();

    if (!auth.userId || !user) {
        redirect("/sign-in");
    }

    const userId = auth.userId;

    return (
        <div className="container px-4 sm:px-6 py-4 sm:py-6">
            <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gradient-purple-blue tracking-tight">
                    Dashboard
                </h1>
                <div className="flex gap-2 self-start sm:self-auto">
                    <ExportApplicationsButton />
                    <Link href="/dashboard/applications/new">
                        <Button className="w-full sm:w-auto bg-primary/90 hover:bg-primary transition-all shadow-md hover:shadow-lg h-9 sm:h-10 text-sm">
                            <PlusCircle className="mr-2 h-4 w-4" />
                            New Application
                        </Button>
                    </Link>
                </div>
            </div>

            {/* Stats Overview - Load with Suspense */}
            <Suspense fallback={<StatsOverviewSkeleton />}>
                <StatsOverviewWrapper userId={userId} />
            </Suspense>

            {/* Application Status - Loaded on demand */}
            <div className="mb-6 sm:mb-8">
                <h2 className="mb-3 sm:mb-4 text-lg sm:text-xl font-semibold">
                    Application Status
                </h2>
                <Suspense
                    fallback={
                        <Skeleton className="h-32 sm:h-40 w-full rounded-lg" />
                    }
                >
                    <StatusWrapper userId={userId} />
                </Suspense>
            </div>

            {/* Recent Applications */}
            <div className="mb-6 sm:mb-8">
                <div className="mb-3 sm:mb-4 flex items-center justify-between">
                    <h2 className="text-lg sm:text-xl font-semibold">
                        Recent Applications
                    </h2>
                    <Link
                        href="/dashboard/applications"
                        className="text-primary hover:text-gradient-cyan-purple hover:underline flex items-center transition-colors duration-200 text-sm"
                    >
                        View All{" "}
                        <ArrowRight className="ml-1 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    </Link>
                </div>
                <Suspense fallback={<RecentApplicationsSkeleton />}>
                    <RecentApplicationsWrapper userId={userId} />
                </Suspense>
            </div>
        </div>
    );
}

// Wrapper components for Suspense
async function StatsOverviewWrapper({ userId }: { userId: string }) {
    const stats = await getApplicationStats(userId);
    return <StatsOverview applicationStats={stats} />;
}

async function StatusWrapper({ userId }: { userId: string }) {
    const stats = await getApplicationStats(userId);

    // Get the raw application data that includes status information
    let applications: Application[] = [];
    try {
        applications =
            (await db.query.jobApplications.findMany({
                where: eq(jobApplications.userId, userId),
                columns: {
                    id: true,
                    status: true,
                },
            })) || [];
    } catch (error) {
        console.error("Error fetching applications:", error);
        applications = []; // Fallback to empty array on error
    }

    const hasApplications = applications.length > 0;

    // Use both visualizations - the original bar and our new flow diagram
    return (
        <div className="space-y-8">
            {/* Status bar chart */}
            <div className="bg-card/30 rounded-lg p-4 shadow-sm">
                <h3 className="mb-4 text-md font-semibold flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    <span>Application Status Overview</span>
                </h3>
                <ApplicationStatusVisual stats={stats} />
            </div>

            {/* Application flow diagram */}
            <div className="bg-card/30 rounded-lg p-4 shadow-sm">
                <h3 className="mb-4 text-md font-semibold flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Application Journey Flow</span>
                </h3>
                {hasApplications ? (
                    <ApplicationFlowWithNavigation
                        applications={applications}
                    />
                ) : (
                    <div className="w-full h-[200px] border rounded-lg bg-card/30 flex flex-col items-center justify-center text-center p-4 space-y-2">
                        <p className="text-muted-foreground">
                            Add applications to see your journey flow
                        </p>
                        <Link href="/dashboard/applications/new">
                            <Button
                                variant="outline"
                                size="sm"
                                className="mt-2"
                            >
                                <PlusCircle className="mr-2 h-4 w-4" />
                                Add Application
                            </Button>
                        </Link>
                    </div>
                )}
            </div>
        </div>
    );
}

async function RecentApplicationsWrapper({ userId }: { userId: string }) {
    const applications = await getRecentApplications(userId);

    if (applications.length === 0) {
        return (
            <div className="rounded-xl border bg-card/50 p-4 sm:p-8 text-center shadow-sm">
                <p className="text-sm text-muted-foreground mb-2">
                    No applications yet
                </p>
                <Link href="/dashboard/applications/new">
                    <Button className="mt-2 bg-primary/90 hover:bg-primary shadow-md hover:shadow-lg transition-all h-9 sm:h-10 text-sm">
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Add Your First Application
                    </Button>
                </Link>
            </div>
        );
    }

    return (
        <div className="rounded-xl border bg-card divide-y shadow-md overflow-hidden">
            {applications.map((app) => (
                <Link
                    key={app.id}
                    href={`/dashboard/applications/${app.id}`}
                    className="flex items-center justify-between p-3 sm:p-4 hover:bg-primary/5 transition-colors"
                >
                    <div>
                        <h3 className="font-medium text-sm sm:text-base">
                            {app.position}
                        </h3>
                        <p className="text-xs sm:text-sm text-muted-foreground">
                            {app.company}
                        </p>
                    </div>
                    <div className="text-right">
                        <div className="capitalize text-xs sm:text-sm font-medium">
                            {app.status}
                        </div>
                        <p className="text-xs text-muted-foreground">
                            {formatDate(new Date(app.createdAt), "MMM d, yyyy")}
                        </p>
                    </div>
                </Link>
            ))}
        </div>
    );
}

// Skeleton loaders for Suspense fallbacks
function StatsOverviewSkeleton() {
    return (
        <div className="mb-6 sm:mb-8 grid gap-3 sm:gap-6 grid-cols-2 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
                <div
                    key={i}
                    className="rounded-xl border bg-card p-3 sm:p-6 shadow-md"
                >
                    <div className="flex items-center gap-2">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="mt-2 h-8 w-16" />
                </div>
            ))}
        </div>
    );
}

function RecentApplicationsSkeleton() {
    return (
        <div className="rounded-xl border bg-card divide-y shadow-md">
            {[...Array(5)].map((_, i) => (
                <div
                    key={i}
                    className="p-3 sm:p-4 flex items-center justify-between"
                >
                    <div>
                        <Skeleton className="h-4 w-32 mb-2" />
                        <Skeleton className="h-3 w-24" />
                    </div>
                    <div className="text-right">
                        <Skeleton className="h-4 w-16 mb-2" />
                        <Skeleton className="h-3 w-20" />
                    </div>
                </div>
            ))}
        </div>
    );
}
