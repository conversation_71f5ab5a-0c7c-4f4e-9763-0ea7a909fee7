"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useUser } from "@clerk/nextjs";
import { toast } from "sonner";

export default function TestApiComponent() {
    const { user, isLoaded } = useUser();
    const [result, setResult] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const testGetResumes = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await fetch("/api/resumes", {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            console.log("Response status:", response.status);

            const responseText = await response.text();
            console.log("Response text:", responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error("Failed to parse response as JSON:", e);
                throw new Error("Invalid response from server");
            }

            if (!response.ok) {
                throw new Error(data.error || "Failed to fetch resumes");
            }

            setResult(data);
            toast.success("API call successful");
        } catch (err: any) {
            console.error("API test error:", err);
            setError(err.message || "An error occurred");
            toast.error(err.message || "An error occurred");
        } finally {
            setLoading(false);
        }
    };

    const testPostResume = async () => {
        setLoading(true);
        setError(null);
        try {
            // Create a test resume object
            const testResume = {
                name: "Test Resume " + new Date().toISOString(),
                fileUrl: "https://example.com/test-resume.pdf",
                filePath: "test-key-" + Date.now(),
                fileType: "pdf",
                isDefault: false,
            };

            console.log("Sending test resume:", testResume);

            const response = await fetch("/api/resumes", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(testResume),
            });

            console.log("Response status:", response.status);

            const responseText = await response.text();
            console.log("Response text:", responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error("Failed to parse response as JSON:", e);
                throw new Error("Invalid response from server");
            }

            if (!response.ok) {
                throw new Error(data.error || "Failed to create resume");
            }

            setResult(data);
            toast.success("Resume created successfully");
        } catch (err: any) {
            console.error("API test error:", err);
            setError(err.message || "An error occurred");
            toast.error(err.message || "An error occurred");
        } finally {
            setLoading(false);
        }
    };

    if (!isLoaded) {
        return <div>Loading...</div>;
    }

    return (
        <div className="p-8 space-y-6">
            <h1 className="text-2xl font-bold">API Test Page</h1>

            <div className="space-y-2">
                <p>
                    User:{" "}
                    {user
                        ? user.fullName || user.emailAddresses[0].emailAddress
                        : "Not logged in"}
                </p>
                <div className="flex space-x-4">
                    <Button
                        onClick={testGetResumes}
                        disabled={loading || !user}
                    >
                        {loading ? "Testing..." : "Test GET /api/resumes"}
                    </Button>
                    <Button
                        onClick={testPostResume}
                        disabled={loading || !user}
                        variant="outline"
                    >
                        {loading ? "Testing..." : "Test POST /api/resumes"}
                    </Button>
                </div>
            </div>

            {error && (
                <div className="p-4 bg-red-50 text-red-700 rounded-md">
                    <p className="font-medium">Error:</p>
                    <p>{error}</p>
                </div>
            )}

            {result && (
                <div className="p-4 bg-green-50 text-green-700 rounded-md">
                    <p className="font-medium">Result:</p>
                    <pre className="mt-2 overflow-auto max-h-96 p-2 bg-white rounded border">
                        {JSON.stringify(result, null, 2)}
                    </pre>
                </div>
            )}
        </div>
    );
}
