"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import {
    FileText,
    Upload,
    ArrowLeft,
    FileUp,
    Trash2,
    AlertCircle,
    CheckCircle,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import { useUploadThing } from "@/utils/uploadthing";
import { queryClient } from "@/lib/query-client";
import { LoadingAnimation } from "@/components/ui/loading-animation";
import { useResumes } from "@/lib/hooks/api-hooks";

export type FileDataType = {
    url: string;
    name: string;
    size: number;
    key: string;
};

export default function UploadResumePage() {
    const router = useRouter();
    const { user } = useUser();
    const [name, setName] = useState("");
    const [isDefault, setIsDefault] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [error, setError] = useState("");
    const [uploadedFile, setUploadedFile] = useState<FileDataType | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { data: resumes } = useResumes();
    const { startUpload } = useUploadThing("resumeUploader", {
        onClientUploadComplete: (res) => {
            if (res && res[0]) {
                setUploadProgress(100);
                setUploadedFile({
                    url: res[0].url,
                    name: res[0].name,
                    size: res[0].size,
                    key: res[0].key,
                });
                toast.success("File uploaded successfully");
                setIsUploading(false);
            }
        },
        onUploadError: (error) => {
            setError(error.message || "Failed to upload file");
            toast.error("Upload failed");
            setIsUploading(false);
        },
        onUploadProgress: (progress) => {
            setUploadProgress(progress);
        },
    });

    // Clear error when uploadedFile changes
    useEffect(() => {
        if (uploadedFile) {
            setError("");
        }
    }, [uploadedFile]);

    // Set isDefault to true if this is the first resume
    useEffect(() => {
        if (resumes && resumes.length === 0) {
            setIsDefault(true);
        }
    }, [resumes]);

    const formatFileSize = (bytes: number) => {
        if (bytes < 1024) return bytes + " bytes";
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
        else return (bytes / 1048576).toFixed(2) + " MB";
    };

    const getFileIcon = (fileName: string) => {
        const extension = fileName.split(".").pop()?.toLowerCase();
        switch (extension) {
            case "pdf":
                return <FileText className="h-6 w-6 text-red-500" />;
            case "docx":
            case "doc":
                return <FileText className="h-6 w-6 text-blue-500" />;
            default:
                return <FileText className="h-6 w-6 text-gray-500" />;
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (!files || files.length === 0) return;

        const file = files[0];

        // Validate file type
        const fileType = file.name.split(".").pop()?.toLowerCase();
        if (!["pdf", "doc", "docx"].includes(fileType || "")) {
            setError(
                "Invalid file type. Please upload PDF, DOC, or DOCX files only."
            );
            toast.error("Invalid file type");
            return;
        }

        // Validate file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
            setError("File is too large. Maximum size is 10MB.");
            toast.error("File is too large");
            return;
        }

        setSelectedFile(file);

        // Set default name from filename if not already set
        if (!name) {
            setName(file.name.split(".")[0]);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = () => {
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);

        const files = e.dataTransfer.files;
        if (!files || files.length === 0) return;

        const file = files[0];

        // Validate file type
        const fileType = file.name.split(".").pop()?.toLowerCase();
        if (!["pdf", "doc", "docx"].includes(fileType || "")) {
            setError(
                "Invalid file type. Please upload PDF, DOC, or DOCX files only."
            );
            toast.error("Invalid file type");
            return;
        }

        // Validate file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
            setError("File is too large. Maximum size is 10MB.");
            toast.error("File is too large");
            return;
        }

        setSelectedFile(file);

        // Set default name from filename if not already set
        if (!name) {
            setName(file.name.split(".")[0]);
        }
    };

    const handleRemoveFile = async () => {
        // Remove from UploadThing if file was uploaded
        if (uploadedFile?.key) {
            try {
                await fetch(`/api/uploadthing/delete?key=${uploadedFile.key}`, {
                    method: "DELETE",
                });
            } catch (err) {
                console.error("Failed to delete file from storage:", err);
            }
        }

        setSelectedFile(null);
        setUploadedFile(null);
        setUploadProgress(0);
        setName("");
        setError("");
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    const handleUploadFile = async () => {
        if (!selectedFile) return;

        try {
            setIsUploading(true);
            setUploadProgress(0);

            // Upload to UploadThing
            await startUpload([selectedFile]);
        } catch (err: any) {
            console.error("Upload error:", err);
            setError(
                err instanceof Error ? err.message : "Failed to upload file"
            );
            toast.error("Upload failed");
            setIsUploading(false);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!uploadedFile) {
            setError("Please upload a resume file");
            toast.error("Please upload a resume file");
            return;
        }

        if (!name) {
            setError("Please provide a name for your resume");
            toast.error("Please provide a name for your resume");
            return;
        }

        if (!user) {
            setError("You must be logged in to upload a resume");
            toast.error("You must be logged in to upload a resume");
            return;
        }

        try {
            setIsUploading(true);
            setError("");

            // Prepare the resume data
            const resumeData = {
                name,
                fileUrl: uploadedFile.url,
                filePath: uploadedFile.key,
                fileType: uploadedFile.name.split(".").pop(),
                isDefault,
            };

            console.log("Saving resume:", resumeData);

            // Save resume to database
            const response = await fetch("/api/resumes", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(resumeData),
                credentials: "include",
            });

            if (!response.ok) {
                const responseData = await response.json();
                throw new Error(responseData.error || "Failed to save resume");
            }

            const responseData = await response.json();
            console.log("Resume saved:", responseData);

            // Trigger resume parsing immediately after saving
            if (responseData.id) {
                try {
                    console.log("Parsing resume:", responseData.id);
                    const parseResponse = await fetch("/api/resumes/parse", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ resumeId: responseData.id }),
                        credentials: "include",
                    });

                    if (parseResponse.ok) {
                        toast.success(
                            "Resume uploaded and parsed successfully"
                        );
                    } else {
                        toast.success("Resume uploaded successfully");
                    }
                } catch (parseError) {
                    console.error("Parse error:", parseError);
                    toast.success("Resume uploaded successfully");
                }
            } else {
                toast.success("Resume uploaded successfully");
            }
            //invalidate resumes query key
            queryClient.invalidateQueries({ queryKey: ["resumes"] });
            // Navigate to the resumes page
            router.push("/dashboard/resumes");
            router.refresh();
        } catch (err: any) {
            console.error("Save error:", err);
            setError(
                err instanceof Error ? err.message : "Failed to upload resume"
            );
            toast.error("Failed to save resume");
        } finally {
            setIsUploading(false);
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="container py-8"
        >
            <div className="mb-10">
                <Link
                    href="/dashboard/resumes"
                    className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Resumes
                </Link>
                <h1 className="mt-3 text-3xl font-bold tracking-tight">
                    Upload Resume
                </h1>
                <p className="mt-2 text-muted-foreground">
                    Add a new resume to your collection for job applications
                </p>
            </div>

            <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1, duration: 0.4 }}
                className="mx-auto max-w-2xl rounded-xl border bg-card p-8 shadow-sm"
            >
                <form onSubmit={handleSubmit} className="space-y-8">
                    <div className="border rounded-lg p-6 bg-card">
                        <div className="flex items-center gap-2 mb-4">
                            <FileText className="h-5 w-5 text-primary" />
                            <h3 className="text-lg font-medium">
                                Resume Upload
                            </h3>
                        </div>
                        <p className="text-sm text-muted-foreground mb-6">
                            Upload your resume in PDF, DOCX, or DOC format (max
                            10MB)
                        </p>

                        <AnimatePresence mode="wait">
                            {!selectedFile ? (
                                <motion.div
                                    key="upload-area"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200 cursor-pointer ${
                                        isDragging
                                            ? "border-primary bg-primary/5"
                                            : "border-muted-foreground/20"
                                    }`}
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={handleDrop}
                                    onClick={() =>
                                        fileInputRef.current?.click()
                                    }
                                >
                                    <input
                                        ref={fileInputRef}
                                        type="file"
                                        accept=".pdf,.doc,.docx"
                                        onChange={handleFileChange}
                                        className="hidden"
                                    />
                                    <div className="flex flex-col items-center gap-4">
                                        <Upload className="h-12 w-12 text-primary" />
                                        <div className="space-y-2">
                                            <p className="font-medium">
                                                Drag & drop your resume here
                                            </p>
                                            <p className="text-sm text-muted-foreground">
                                                <span className="font-medium">
                                                    Click to browse
                                                </span>{" "}
                                                or drag and drop
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                PDF, DOCX, DOC (Max 10MB)
                                            </p>
                                        </div>
                                        <Button
                                            type="button"
                                            className="mt-2"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                fileInputRef.current?.click();
                                            }}
                                        >
                                            <FileUp className="mr-2 h-4 w-4" />
                                            <span>Select File</span>
                                        </Button>
                                    </div>
                                </motion.div>
                            ) : !uploadedFile ? (
                                <motion.div
                                    key="file-selected"
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    className="space-y-4"
                                >
                                    <div className="flex items-center gap-3 p-4 rounded-lg border bg-muted/40">
                                        <div className="rounded-full bg-primary/10 p-3">
                                            {getFileIcon(selectedFile.name)}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="font-medium text-base truncate">
                                                {selectedFile.name}
                                            </p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatFileSize(
                                                    selectedFile.size
                                                )}
                                            </p>
                                        </div>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="icon"
                                            onClick={handleRemoveFile}
                                            className="hover:bg-destructive/10 hover:text-destructive"
                                        >
                                            <Trash2 className="h-5 w-5" />
                                            <span className="sr-only">
                                                Remove file
                                            </span>
                                        </Button>
                                    </div>

                                    {isUploading ? (
                                        <div className="space-y-2">
                                            <Progress
                                                value={uploadProgress}
                                                className="h-2"
                                            />
                                            <p className="text-xs text-center text-muted-foreground">
                                                Uploading... {uploadProgress}%
                                            </p>
                                        </div>
                                    ) : (
                                        <Button
                                            type="button"
                                            className="w-full"
                                            onClick={handleUploadFile}
                                        >
                                            <Upload className="mr-2 h-4 w-4" />
                                            Upload File
                                        </Button>
                                    )}
                                </motion.div>
                            ) : (
                                <motion.div
                                    key="file-uploaded"
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    className="space-y-4"
                                >
                                    <div className="flex items-center gap-3 p-4 rounded-lg border bg-muted/40">
                                        <div className="rounded-full bg-primary/10 p-3">
                                            {getFileIcon(uploadedFile.name)}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="font-medium text-base truncate">
                                                {uploadedFile.name}
                                            </p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatFileSize(
                                                    uploadedFile.size
                                                )}
                                            </p>
                                        </div>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="icon"
                                            onClick={handleRemoveFile}
                                            className="hover:bg-destructive/10 hover:text-destructive"
                                        >
                                            <Trash2 className="h-5 w-5" />
                                            <span className="sr-only">
                                                Remove file
                                            </span>
                                        </Button>
                                    </div>

                                    <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-3 rounded-md border border-green-200">
                                        <CheckCircle className="h-5 w-5 flex-shrink-0" />
                                        <span>File uploaded successfully</span>
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="name" className="text-base">
                            Resume Name
                        </Label>
                        <Input
                            id="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="e.g., Software Engineer Resume"
                            required
                            className="h-11"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                            Give your resume a descriptive name to easily
                            identify it later
                        </p>
                    </div>

                    <div className="flex items-center space-x-2 bg-muted/40 p-4 rounded-lg">
                        <Checkbox
                            id="isDefault"
                            checked={isDefault}
                            onCheckedChange={(checked) =>
                                setIsDefault(checked as boolean)
                            }
                            className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                        />
                        <div>
                            <Label htmlFor="isDefault" className="font-medium">
                                Set as default resume
                            </Label>
                            <p className="text-xs text-muted-foreground">
                                This resume will be used as your default for job
                                applications
                            </p>
                        </div>
                    </div>

                    <AnimatePresence>
                        {error && (
                            <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className="rounded-md bg-destructive/10 border border-destructive/20 p-4 text-sm text-destructive"
                            >
                                <div className="flex">
                                    <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                                    <span>{error}</span>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>

                    <div className="flex justify-end gap-4 pt-2">
                        <Link href="/dashboard/resumes">
                            <Button
                                variant="outline"
                                type="button"
                                className="h-11"
                            >
                                Cancel
                            </Button>
                        </Link>
                        <Button
                            type="submit"
                            disabled={isUploading || !uploadedFile}
                            className="h-11 px-6 font-medium"
                        >
                            {isUploading ? (
                                <>
                                    <LoadingAnimation
                                        variant="pulse"
                                        size="sm"
                                        className="mr-2 inline-block"
                                    />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Upload className="mr-2 h-5 w-5" />
                                    Save Resume
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </motion.div>
        </motion.div>
    );
}
