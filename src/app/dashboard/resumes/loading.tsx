import * as React from "react";
import dynamic from "next/dynamic";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";

// Dynamically import the client component with no SSR
const LoadingAnimation = dynamic(
    () => import("@/components/ui/loading-animation"),
    {
        loading: () => (
            <LoadingSkeleton
                variant="resume"
                size="lg"
                text="Loading Resumes"
                subText="Preparing your resume collection..."
                className="py-12"
            />
        ),
    }
);

export default function ResumesLoading() {
    return (
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <LoadingSkeleton
                variant="list"
                size="lg"
                className="py-12 w-full"
            />
        </div>
    );
}
