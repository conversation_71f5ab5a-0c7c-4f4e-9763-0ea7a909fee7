"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription,
    CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { FileText, ArrowLeft, Download, Trash2, RefreshCw } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useResume } from "@/lib/hooks/api-hooks";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";

// Aceternity UI inspired components
const SectionTitle = ({ children }: { children: React.ReactNode }) => (
    <motion.h3
        className="text-lg font-semibold mb-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
    >
        {children}
    </motion.h3>
);

const CardItem = ({
    children,
    className,
}: {
    children: React.ReactNode;
    className?: string;
}) => (
    <motion.div
        className={cn(
            "rounded-lg border bg-card shadow-xs overflow-hidden",
            className
        )}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -2, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
    >
        <div className="p-4">{children}</div>
    </motion.div>
);

type ParsingStatus = "idle" | "parsing" | "parsed" | "error";

interface ResumeData {
    name?: string;
    email?: string;
    phone?: string;
    location?: string;
    summary?: string;
    links?: {
        portfolio?: string;
        linkedIn?: string;
        github?: string;
        other?: string[];
        [key: string]: any;
    };
    skills?: string[] | string;
    projects?:
        | Array<{
              name?: string;
              description?: string;
              dates?: string;
              technologies?: string[];
              url?: string;
          }>
        | any;
    experience?:
        | Array<{
              title?: string;
              company?: string;
              dates?: string;
              description?: string;
              location?: string;
          }>
        | any;
    education?:
        | Array<{
              institution?: string;
              school?: string;
              degree?: string;
              field?: string;
              graduationDate?: string;
              dates?: string;
              gpa?: string;
              relevantCoursework?: string[];
          }>
        | any;
    certifications?:
        | Array<{
              name?: string;
              authority?: string;
              date?: string;
              url?: string;
          }>
        | any;
    publications?:
        | Array<{
              title?: string;
              source?: string;
              date?: string;
              description?: string;
              url?: string;
          }>
        | any;
    [key: string]: any;
}

export default function ResumeDetailPage() {
    const params = useParams();
    const router = useRouter();
    const resumeId = params.id as string;

    const { data: resume, isLoading: loading } = useResume(resumeId);
    const [parsedContent, setParsedContent] = useState<ResumeData | null>(null);
    const [parsingStatus, setParsingStatus] = useState<ParsingStatus>("idle");
    const [parsingError, setParsingError] = useState<string>("");
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    // Set parsed content when resume data is loaded
    useEffect(() => {
        if (
            resume &&
            resume.parsedContent &&
            Object.keys(resume.parsedContent).length > 0
        ) {
            setParsedContent(resume.parsedContent);
            setParsingStatus("parsed");
        }
    }, [resume]);

    // Function to trigger resume parsing
    const parseResume = async () => {
        try {
            setParsingStatus("parsing");
            setParsingError(""); // Clear any previous errors

            const response = await fetch("/api/resumes/parse", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ resumeId }),
            });

            const data = await response.json();

            if (!response.ok) {
                // Get more detailed error message if available
                const errorMessage = data.error || "Failed to parse resume";
                throw new Error(errorMessage);
            }

            if (data.parsedContent) {
                setParsedContent(data.parsedContent);
                setParsingStatus("parsed");
                toast.success("Resume parsed successfully");
            } else {
                throw new Error(
                    "No parsed content returned. The AI may have had trouble analyzing your resume."
                );
            }
        } catch (err: any) {
            console.error("Error parsing resume:", err);
            setParsingStatus("error");

            // Set a more descriptive error message
            const errorMessage = err.message || "Failed to parse resume";
            setParsingError(errorMessage);

            // Show a toast with a more helpful message
            toast.error(
                "Failed to parse resume. Please try again or upload a different file format."
            );
        }
    };

    // Function to delete resume
    const deleteResume = async () => {
        setIsDeleting(true);
        try {
            const response = await fetch(`/api/resumes/${resumeId}/delete`, {
                method: "DELETE",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || "Failed to delete resume");
            }

            toast.success("Resume deleted successfully");
            router.push("/dashboard/resumes");
        } catch (err) {
            console.error("Error deleting resume:", err);
            const errorMessage =
                err instanceof Error ? err.message : "Failed to delete resume";
            toast.error(errorMessage);
        } finally {
            setIsDeleting(false);
            setShowDeleteDialog(false);
        }
    };

    if (loading) {
        return (
            <div className="container py-4 md:py-8">
                <div className="mb-6 md:mb-10">
                    <Link
                        href="/dashboard/resumes"
                        className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Resumes
                    </Link>
                    <Skeleton className="mt-3 h-8 md:h-10 w-full md:w-1/3" />
                    <Skeleton className="mt-2 h-4 md:h-5 w-full md:w-1/2" />
                </div>

                <div className="grid gap-4 md:gap-6">
                    <Skeleton className="h-[300px] md:h-[400px] w-full rounded-xl" />
                </div>
            </div>
        );
    }

    if (!resume) {
        return (
            <div className="container py-4 md:py-8">
                <div className="mb-6 md:mb-10">
                    <Link
                        href="/dashboard/resumes"
                        className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Resumes
                    </Link>
                    <h1 className="mt-3 text-2xl md:text-3xl font-bold tracking-tight">
                        Resume Not Found
                    </h1>
                </div>

                <Card>
                    <CardContent className="pt-6">
                        <p className="text-center text-muted-foreground">
                            The requested resume could not be found.
                        </p>
                    </CardContent>
                    <CardFooter className="flex justify-center pb-6">
                        <Button asChild>
                            <Link href="/dashboard/resumes">
                                Go Back to Resumes
                            </Link>
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        );
    }

    return (
        <>
            <div className="container py-4 md:py-8">
                <div className="mb-6 md:mb-10">
                    <Link
                        href="/dashboard/resumes"
                        className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Resumes
                    </Link>
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-3 gap-4">
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
                            {resume.name}
                        </h1>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                asChild
                                className="text-xs md:text-sm"
                            >
                                <a
                                    href={resume.fileUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <Download className="mr-1 md:mr-2 h-3 w-3 md:h-4 md:w-4" />
                                    Download
                                </a>
                            </Button>
                            <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => setShowDeleteDialog(true)}
                                className="text-xs md:text-sm"
                                disabled={isDeleting}
                            >
                                <Trash2 className="mr-1 md:mr-2 h-3 w-3 md:h-4 md:w-4" />
                                {isDeleting ? "Deleting..." : "Delete"}
                            </Button>
                        </div>
                    </div>
                    <div className="flex flex-wrap items-center gap-2 mt-2">
                        <Badge
                            variant={resume.isDefault ? "default" : "outline"}
                            className="text-xs"
                        >
                            {resume.isDefault ? "Default Resume" : "Resume"}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                            {resume.fileType?.toUpperCase()}
                        </span>
                        <span className="text-xs text-muted-foreground">
                            Uploaded on{" "}
                            {new Date(resume.createdAt).toLocaleDateString()}
                        </span>
                    </div>
                </div>

                <Tabs defaultValue="parsed" className="w-full">
                    <TabsList className="mb-4 md:mb-6 w-full md:w-auto">
                        <TabsTrigger
                            value="parsed"
                            className="flex-1 md:flex-none"
                        >
                            Parsed Content
                        </TabsTrigger>
                        <TabsTrigger
                            value="preview"
                            className="flex-1 md:flex-none"
                        >
                            Preview
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent
                        value="parsed"
                        className="space-y-4 md:space-y-6"
                    >
                        <Card>
                            <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 pb-2 md:pb-6">
                                <div>
                                    <CardTitle className="text-xl md:text-2xl">
                                        Resume Data
                                    </CardTitle>
                                    <CardDescription className="text-xs md:text-sm">
                                        Extracted information from your resume
                                    </CardDescription>
                                </div>
                                {parsingStatus !== "parsing" && (
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={parseResume}
                                        disabled={
                                            parsingStatus ===
                                            ("parsing" as ParsingStatus)
                                        }
                                        className="w-full md:w-auto text-xs md:text-sm"
                                    >
                                        <RefreshCw
                                            className={`mr-1 md:mr-2 h-3 w-3 md:h-4 md:w-4 ${
                                                parsingStatus ===
                                                ("parsing" as ParsingStatus)
                                                    ? "animate-spin"
                                                    : ""
                                            }`}
                                        />
                                        {parsingStatus === "parsed"
                                            ? "Reparse"
                                            : "Parse Resume"}
                                    </Button>
                                )}
                            </CardHeader>
                            <CardContent>
                                {parsingStatus === "parsing" ? (
                                    <div className="flex flex-col items-center justify-center py-8 md:py-10">
                                        <RefreshCw className="h-8 w-8 md:h-10 md:w-10 animate-spin text-primary mb-4" />
                                        <p className="text-center text-sm md:text-base font-medium mb-1">
                                            Parsing your resume...
                                        </p>
                                        <p className="text-center text-xs md:text-sm text-muted-foreground max-w-md">
                                            Our AI is analyzing your resume to
                                            extract key information. This may
                                            take a moment, especially for
                                            complex documents.
                                        </p>
                                    </div>
                                ) : parsingStatus === "error" ? (
                                    <div className="text-center py-8 md:py-10">
                                        <p className="text-destructive mb-2 text-sm md:text-base">
                                            Failed to parse resume
                                        </p>
                                        <p className="text-muted-foreground mb-4 text-xs md:text-sm max-w-md mx-auto">
                                            {parsingError ||
                                                "The AI had trouble analyzing your resume. This can happen with complex formatting or unusual file types."}
                                        </p>
                                        <div className="flex flex-col sm:flex-row gap-2 justify-center">
                                            <Button
                                                onClick={parseResume}
                                                size="sm"
                                                className="text-xs md:text-sm"
                                            >
                                                Try Again
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="text-xs md:text-sm"
                                                asChild
                                            >
                                                <Link href="/dashboard/resumes/new">
                                                    Upload Different Resume
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ) : !parsedContent ? (
                                    <div className="text-center py-8 md:py-10">
                                        <p className="text-muted-foreground mb-2 text-sm md:text-base">
                                            This resume hasn't been parsed yet
                                        </p>
                                        <p className="text-muted-foreground mb-4 text-xs md:text-sm max-w-md mx-auto">
                                            Parsing uses AI to extract
                                            structured information from your
                                            resume. This helps with job
                                            applications and content generation.
                                        </p>
                                        <Button
                                            onClick={parseResume}
                                            size="sm"
                                            className="text-xs md:text-sm"
                                        >
                                            Parse Now
                                        </Button>
                                    </div>
                                ) : (
                                    <motion.div
                                        className="space-y-6"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        transition={{ duration: 0.5 }}
                                    >
                                        {/* Personal Information */}
                                        <div>
                                            <SectionTitle>
                                                Personal Information
                                            </SectionTitle>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <p className="text-xs md:text-sm text-muted-foreground">
                                                        Name
                                                    </p>
                                                    <p className="text-sm md:text-base">
                                                        {parsedContent.name ||
                                                            "Not available"}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-xs md:text-sm text-muted-foreground">
                                                        Email
                                                    </p>
                                                    <p className="text-sm md:text-base break-all">
                                                        {parsedContent.email ||
                                                            "Not available"}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-xs md:text-sm text-muted-foreground">
                                                        Phone
                                                    </p>
                                                    <p className="text-sm md:text-base">
                                                        {parsedContent.phone ||
                                                            "Not available"}
                                                    </p>
                                                </div>
                                                {parsedContent.location && (
                                                    <div>
                                                        <p className="text-xs md:text-sm text-muted-foreground">
                                                            Location
                                                        </p>
                                                        <p className="text-sm md:text-base">
                                                            {
                                                                parsedContent.location
                                                            }
                                                        </p>
                                                    </div>
                                                )}
                                            </div>

                                            {/* Links */}
                                            {parsedContent.links && (
                                                <div className="mt-4">
                                                    <p className="text-xs md:text-sm text-muted-foreground mb-2">
                                                        Links
                                                    </p>
                                                    <div className="flex flex-wrap gap-2">
                                                        {parsedContent.links
                                                            .portfolio && (
                                                            <Badge
                                                                variant="outline"
                                                                className="text-xs flex items-center gap-1"
                                                            >
                                                                <span>
                                                                    Portfolio:
                                                                </span>
                                                                <a
                                                                    href={`https://${parsedContent.links.portfolio.replace(
                                                                        /^https?:\/\//,
                                                                        ""
                                                                    )}`}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="text-primary hover:underline"
                                                                >
                                                                    {parsedContent.links.portfolio.replace(
                                                                        /^https?:\/\//,
                                                                        ""
                                                                    )}
                                                                </a>
                                                            </Badge>
                                                        )}
                                                        {parsedContent.links
                                                            .linkedIn && (
                                                            <Badge
                                                                variant="outline"
                                                                className="text-xs flex items-center gap-1"
                                                            >
                                                                <span>
                                                                    LinkedIn:
                                                                </span>
                                                                <a
                                                                    href={`https://${parsedContent.links.linkedIn.replace(
                                                                        /^https?:\/\//,
                                                                        ""
                                                                    )}`}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="text-primary hover:underline"
                                                                >
                                                                    {parsedContent.links.linkedIn.replace(
                                                                        /^https?:\/\//,
                                                                        ""
                                                                    )}
                                                                </a>
                                                            </Badge>
                                                        )}
                                                        {parsedContent.links
                                                            .github && (
                                                            <Badge
                                                                variant="outline"
                                                                className="text-xs flex items-center gap-1"
                                                            >
                                                                <span>
                                                                    GitHub:
                                                                </span>
                                                                <a
                                                                    href={`https://${parsedContent.links.github.replace(
                                                                        /^https?:\/\//,
                                                                        ""
                                                                    )}`}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="text-primary hover:underline"
                                                                >
                                                                    {parsedContent.links.github.replace(
                                                                        /^https?:\/\//,
                                                                        ""
                                                                    )}
                                                                </a>
                                                            </Badge>
                                                        )}
                                                        {parsedContent.links
                                                            .other &&
                                                            parsedContent.links.other.map(
                                                                (
                                                                    link: string,
                                                                    index: number
                                                                ) => (
                                                                    <Badge
                                                                        key={
                                                                            index
                                                                        }
                                                                        variant="outline"
                                                                        className="text-xs flex items-center gap-1"
                                                                    >
                                                                        <span>
                                                                            Link{" "}
                                                                            {index +
                                                                                1}
                                                                            :
                                                                        </span>
                                                                        <a
                                                                            href={
                                                                                link.startsWith(
                                                                                    "http"
                                                                                )
                                                                                    ? link
                                                                                    : `https://${link}`
                                                                            }
                                                                            target="_blank"
                                                                            rel="noopener noreferrer"
                                                                            className="text-primary hover:underline"
                                                                        >
                                                                            {link
                                                                                .replace(
                                                                                    /^https?:\/\//,
                                                                                    ""
                                                                                )
                                                                                .substring(
                                                                                    0,
                                                                                    20
                                                                                )}
                                                                            {link.replace(
                                                                                /^https?:\/\//,
                                                                                ""
                                                                            )
                                                                                .length >
                                                                            20
                                                                                ? "..."
                                                                                : ""}
                                                                        </a>
                                                                    </Badge>
                                                                )
                                                            )}
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Summary */}
                                        {parsedContent.summary && (
                                            <div>
                                                <SectionTitle>
                                                    Summary
                                                </SectionTitle>
                                                <p className="text-sm md:text-base">
                                                    {parsedContent.summary}
                                                </p>
                                            </div>
                                        )}

                                        {/* Skills */}
                                        {parsedContent.skills &&
                                            Array.isArray(
                                                parsedContent.skills
                                            ) &&
                                            parsedContent.skills.length > 0 && (
                                                <div>
                                                    <SectionTitle>
                                                        Skills
                                                    </SectionTitle>
                                                    <div className="flex flex-wrap gap-2">
                                                        {parsedContent.skills.map(
                                                            (skill, index) => (
                                                                <Badge
                                                                    key={index}
                                                                    variant="secondary"
                                                                    className="text-xs"
                                                                >
                                                                    {skill}
                                                                </Badge>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                        {/* Experience */}
                                        {parsedContent.experience &&
                                            Array.isArray(
                                                parsedContent.experience
                                            ) &&
                                            parsedContent.experience.length >
                                                0 && (
                                                <div>
                                                    <SectionTitle>
                                                        Experience
                                                    </SectionTitle>
                                                    <div className="space-y-3 md:space-y-4">
                                                        {parsedContent.experience.map(
                                                            (exp, index) => (
                                                                <CardItem
                                                                    key={index}
                                                                >
                                                                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                                                        <h4 className="font-medium text-sm md:text-base">
                                                                            {exp.title ||
                                                                                "Position"}
                                                                        </h4>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            {exp.dates ||
                                                                                ""}
                                                                        </span>
                                                                    </div>
                                                                    <div className="flex flex-col md:flex-row md:items-center gap-1 md:gap-2 mb-2">
                                                                        <p className="text-xs md:text-sm text-muted-foreground">
                                                                            {exp.company ||
                                                                                "Company"}
                                                                        </p>
                                                                        {exp.location && (
                                                                            <>
                                                                                <span className="hidden md:inline text-muted-foreground">
                                                                                    •
                                                                                </span>
                                                                                <p className="text-xs md:text-sm text-muted-foreground">
                                                                                    {
                                                                                        exp.location
                                                                                    }
                                                                                </p>
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                    {exp.description && (
                                                                        <p className="text-xs md:text-sm whitespace-pre-line">
                                                                            {
                                                                                exp.description
                                                                            }
                                                                        </p>
                                                                    )}
                                                                </CardItem>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                        {/* Education */}
                                        {parsedContent.education &&
                                            Array.isArray(
                                                parsedContent.education
                                            ) &&
                                            parsedContent.education.length >
                                                0 && (
                                                <div>
                                                    <SectionTitle>
                                                        Education
                                                    </SectionTitle>
                                                    <div className="space-y-3 md:space-y-4">
                                                        {parsedContent.education.map(
                                                            (edu, index) => (
                                                                <CardItem
                                                                    key={index}
                                                                >
                                                                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                                                        <h4 className="font-medium text-sm md:text-base">
                                                                            {edu.degree ||
                                                                                ""}{" "}
                                                                            {edu.field
                                                                                ? `in ${edu.field}`
                                                                                : ""}
                                                                        </h4>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            {edu.dates ||
                                                                                edu.graduationDate ||
                                                                                ""}
                                                                        </span>
                                                                    </div>
                                                                    <div className="flex flex-col md:flex-row md:items-center gap-1 md:gap-2 mb-2">
                                                                        <p className="text-xs md:text-sm text-muted-foreground">
                                                                            {edu.institution ||
                                                                                edu.school ||
                                                                                "Institution"}
                                                                        </p>
                                                                        {edu.gpa && (
                                                                            <>
                                                                                <span className="hidden md:inline text-muted-foreground">
                                                                                    •
                                                                                </span>
                                                                                <p className="text-xs md:text-sm text-muted-foreground">
                                                                                    GPA:{" "}
                                                                                    {
                                                                                        edu.gpa
                                                                                    }
                                                                                </p>
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                    {edu.relevantCoursework &&
                                                                        edu
                                                                            .relevantCoursework
                                                                            .length >
                                                                            0 && (
                                                                            <div className="mt-1">
                                                                                <p className="text-xs text-muted-foreground mb-1">
                                                                                    Relevant
                                                                                    Coursework:
                                                                                </p>
                                                                                <div className="flex flex-wrap gap-1">
                                                                                    {edu.relevantCoursework.map(
                                                                                        (
                                                                                            course: string,
                                                                                            courseIndex: number
                                                                                        ) => (
                                                                                            <Badge
                                                                                                key={
                                                                                                    courseIndex
                                                                                                }
                                                                                                variant="outline"
                                                                                                className="text-xs"
                                                                                            >
                                                                                                {
                                                                                                    course
                                                                                                }
                                                                                            </Badge>
                                                                                        )
                                                                                    )}
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                </CardItem>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                        {/* Projects */}
                                        {parsedContent.projects &&
                                            Array.isArray(
                                                parsedContent.projects
                                            ) &&
                                            parsedContent.projects.length >
                                                0 && (
                                                <div>
                                                    <SectionTitle>
                                                        Projects
                                                    </SectionTitle>
                                                    <div className="space-y-3 md:space-y-4">
                                                        {parsedContent.projects.map(
                                                            (
                                                                project,
                                                                index
                                                            ) => (
                                                                <CardItem
                                                                    key={index}
                                                                >
                                                                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                                                        <h4 className="font-medium text-sm md:text-base flex items-center gap-2">
                                                                            {project.name ||
                                                                                "Project"}
                                                                            {project.url && (
                                                                                <a
                                                                                    href={
                                                                                        project.url.startsWith(
                                                                                            "http"
                                                                                        )
                                                                                            ? project.url
                                                                                            : `https://${project.url}`
                                                                                    }
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                    className="text-primary hover:underline text-xs"
                                                                                >
                                                                                    (View
                                                                                    Project)
                                                                                </a>
                                                                            )}
                                                                        </h4>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            {project.dates ||
                                                                                ""}
                                                                        </span>
                                                                    </div>
                                                                    {project.description && (
                                                                        <p className="text-xs md:text-sm mb-2">
                                                                            {
                                                                                project.description
                                                                            }
                                                                        </p>
                                                                    )}
                                                                    {project.technologies &&
                                                                        project
                                                                            .technologies
                                                                            .length >
                                                                            0 && (
                                                                            <div className="flex flex-wrap gap-1 mt-1">
                                                                                {project.technologies.map(
                                                                                    (
                                                                                        tech: string,
                                                                                        techIndex: number
                                                                                    ) => (
                                                                                        <Badge
                                                                                            key={
                                                                                                techIndex
                                                                                            }
                                                                                            variant="secondary"
                                                                                            className="text-xs"
                                                                                        >
                                                                                            {
                                                                                                tech
                                                                                            }
                                                                                        </Badge>
                                                                                    )
                                                                                )}
                                                                            </div>
                                                                        )}
                                                                </CardItem>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                        {/* Certifications */}
                                        {parsedContent.certifications &&
                                            Array.isArray(
                                                parsedContent.certifications
                                            ) &&
                                            parsedContent.certifications
                                                .length > 0 && (
                                                <div>
                                                    <SectionTitle>
                                                        Certifications
                                                    </SectionTitle>
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                        {parsedContent.certifications.map(
                                                            (cert, index) => (
                                                                <div
                                                                    key={index}
                                                                    className="border rounded-md p-3 bg-muted/30"
                                                                >
                                                                    <div className="flex flex-col">
                                                                        <h4 className="font-medium text-sm md:text-base">
                                                                            {
                                                                                cert.name
                                                                            }
                                                                        </h4>
                                                                        <div className="flex items-center justify-between mt-1">
                                                                            <p className="text-xs text-muted-foreground">
                                                                                {cert.authority ||
                                                                                    ""}
                                                                            </p>
                                                                            {cert.date && (
                                                                                <span className="text-xs text-muted-foreground">
                                                                                    {
                                                                                        cert.date
                                                                                    }
                                                                                </span>
                                                                            )}
                                                                        </div>
                                                                        {cert.url && (
                                                                            <a
                                                                                href={
                                                                                    cert.url.startsWith(
                                                                                        "http"
                                                                                    )
                                                                                        ? cert.url
                                                                                        : `https://${cert.url}`
                                                                                }
                                                                                target="_blank"
                                                                                rel="noopener noreferrer"
                                                                                className="text-primary hover:underline text-xs mt-1"
                                                                            >
                                                                                View
                                                                                Certificate
                                                                            </a>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                        {/* Publications */}
                                        {parsedContent.publications &&
                                            Array.isArray(
                                                parsedContent.publications
                                            ) &&
                                            parsedContent.publications.length >
                                                0 && (
                                                <div>
                                                    <SectionTitle>
                                                        Publications
                                                    </SectionTitle>
                                                    <div className="space-y-3 md:space-y-4">
                                                        {parsedContent.publications.map(
                                                            (pub, index) => (
                                                                <CardItem
                                                                    key={index}
                                                                >
                                                                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                                                        <h4 className="font-medium text-sm md:text-base">
                                                                            {
                                                                                pub.title
                                                                            }
                                                                        </h4>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            {pub.date ||
                                                                                ""}
                                                                        </span>
                                                                    </div>
                                                                    <p className="text-xs md:text-sm text-muted-foreground mb-2">
                                                                        {pub.source ||
                                                                            ""}
                                                                    </p>
                                                                    {pub.description && (
                                                                        <p className="text-xs md:text-sm">
                                                                            {
                                                                                pub.description
                                                                            }
                                                                        </p>
                                                                    )}
                                                                    {pub.url && (
                                                                        <a
                                                                            href={
                                                                                pub.url.startsWith(
                                                                                    "http"
                                                                                )
                                                                                    ? pub.url
                                                                                    : `https://${pub.url}`
                                                                            }
                                                                            target="_blank"
                                                                            rel="noopener noreferrer"
                                                                            className="text-primary hover:underline text-xs mt-2 inline-block"
                                                                        >
                                                                            View
                                                                            Publication
                                                                        </a>
                                                                    )}
                                                                </CardItem>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                    </motion.div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="preview">
                        <Card>
                            <CardHeader className="pb-2 md:pb-6">
                                <CardTitle className="text-xl md:text-2xl">
                                    Resume Preview
                                </CardTitle>
                                <CardDescription className="text-xs md:text-sm">
                                    View your resume file
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="flex justify-center">
                                    {resume.fileType
                                        ?.toLowerCase()
                                        .includes("pdf") ? (
                                        <iframe
                                            src={`${resume.fileUrl}#toolbar=0`}
                                            className="w-full h-[400px] md:h-[600px] border rounded-lg"
                                            title="Resume Preview"
                                        />
                                    ) : (
                                        <div className="text-center py-8 md:py-10">
                                            <FileText className="h-12 w-12 md:h-16 md:w-16 mx-auto text-muted-foreground mb-4" />
                                            <p className="text-muted-foreground mb-4 text-sm md:text-base">
                                                Preview not available for this
                                                file type
                                            </p>
                                            <Button
                                                asChild
                                                size="sm"
                                                className="text-xs md:text-sm"
                                            >
                                                <a
                                                    href={resume.fileUrl}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                >
                                                    <Download className="mr-1 md:mr-2 h-3 w-3 md:h-4 md:w-4" />
                                                    Download to View
                                                </a>
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>

            <DeleteConfirmationDialog
                open={showDeleteDialog}
                onOpenChange={setShowDeleteDialog}
                onConfirm={deleteResume}
                isLoading={isDeleting}
                title="Delete Resume"
                description={`Are you sure you want to delete "${resume?.name}"? This will permanently remove the resume from your account and our servers. You will be redirected to the resumes page.`}
            />
        </>
    );
}
