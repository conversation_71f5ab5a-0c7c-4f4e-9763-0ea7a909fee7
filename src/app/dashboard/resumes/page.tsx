"use client";

import { useRouter } from "next/navigation";
import { formatDate } from "date-fns";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
    PlusCircle,
    FileText,
    Download,
    Trash2,
    Upload,
    MoreVertical,
    CheckCircle,
    Eye,
} from "lucide-react";
import { toast } from "sonner";
import { useResumes } from "@/lib/hooks/api-hooks";

import { motion } from "framer-motion";
import { queryClient } from "@/lib/query-client";
import { useUser } from "@clerk/nextjs";
import { useEffect, useState, useCallback } from "react";
import { LoadingAnimation } from "@/components/ui/loading-animation";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ResumeCard } from "@/components/dashboard/resume-card";
import { ResumesLoadingSkeleton } from "@/components/dashboard/resumes-loading-skeleton";
import { ResumesEmptyState } from "@/components/dashboard/resumes-empty-state";

// Local Resume type definition to avoid import issues
interface Resume {
    id: number;
    name: string;
    fileType: string;
    fileUrl: string;
    createdAt: string;
    isDefault: boolean;
    parsedContent?: {
        summary?: string;
        [key: string]: any;
    };
    [key: string]: any;
}

const formatResumeName = (name: string) => {
    return name
        .replace(/\.(pdf|docx|doc)$/i, "") // Remove file extensions
        .replace(/_/g, " ") // Replace underscores with spaces
        .replace(/-/g, " ") // Replace hyphens with spaces
        .replace(/\s+/g, " ") // Replace multiple spaces with a single space
        .trim();
};

export default function ResumesPage() {
    const client = queryClient;
    const router = useRouter();
    const { data: resumes, isLoading, isError } = useResumes();
    const { user, isLoaded } = useUser();
    const [isNavigating, setIsNavigating] = useState(false);
    const [deletingResumeIds, setDeletingResumeIds] = useState<Set<number>>(
        new Set()
    );

    useEffect(() => {
        if (isLoaded && !user) {
            router.replace("/sign-in");
        }
    }, [isLoaded, user, router]);

    const handleDelete = useCallback(
        async (resumeId: number) => {
            // Optimistic update - immediately mark as deleting
            setDeletingResumeIds((prev) => new Set(prev).add(resumeId));

            // Optimistically remove from UI
            const currentResumes = (resumes as Resume[]) || [];
            const optimisticResumes = currentResumes.filter(
                (resume: Resume) => resume.id !== resumeId
            );
            client.setQueryData(["resumes"], optimisticResumes);

            try {
                const response = await fetch(
                    `/api/resumes/${resumeId}/delete`,
                    {
                        method: "DELETE",
                        credentials: "include",
                        headers: {
                            "Content-Type": "application/json",
                        },
                    }
                );

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(
                        errorData.message || "Failed to delete resume"
                    );
                }

                // Success - show toast and refresh data
                toast.success("Resume deleted successfully");

                // Refresh the data to ensure consistency
                client.invalidateQueries({ queryKey: ["resumes"] });
            } catch (error) {
                console.error("Error deleting resume:", error);

                // Rollback optimistic update on error
                client.setQueryData(["resumes"], currentResumes);

                const errorMessage =
                    error instanceof Error
                        ? error.message
                        : "Failed to delete resume";
                toast.error(errorMessage);
            } finally {
                // Remove from deleting set
                setDeletingResumeIds((prev) => {
                    const newSet = new Set(prev);
                    newSet.delete(resumeId);
                    return newSet;
                });
            }
        },
        [resumes, client]
    );

    const handleSetDefault = useCallback(
        async (resumeId: number) => {
            try {
                // Optimistic update
                const currentResumes = (resumes as Resume[]) || [];
                const optimisticResumes = currentResumes.map(
                    (resume: Resume) => ({
                        ...resume,
                        isDefault: resume.id === resumeId,
                    })
                );
                client.setQueryData(["resumes"], optimisticResumes);

                const response = await fetch(
                    `/api/resumes/${resumeId}/set-default`,
                    {
                        method: "PATCH",
                        credentials: "include",
                        headers: {
                            "Content-Type": "application/json",
                        },
                    }
                );

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(
                        errorData.message || "Failed to set default resume"
                    );
                }

                toast.success("Resume set as default");
                client.invalidateQueries({ queryKey: ["resumes"] });
            } catch (error) {
                console.error("Error setting default resume:", error);

                // Rollback optimistic update
                client.setQueryData(["resumes"], resumes);

                const errorMessage =
                    error instanceof Error
                        ? error.message
                        : "Failed to set default resume";
                toast.error(errorMessage);
            }
        },
        [resumes, client]
    );

    const handleUploadClick = useCallback(() => {
        setIsNavigating(true);
        router.push("/dashboard/resumes/upload");
    }, [router]);

    if (!isLoaded || isLoading) {
        return <ResumesLoadingSkeleton />;
    }

    if (isError) {
        return (
            <div className="container py-8 text-center">
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-6">
                    <h2 className="text-lg font-semibold text-destructive mb-2">
                        Failed to load resumes
                    </h2>
                    <p className="text-sm text-muted-foreground mb-4">
                        There was an error loading your resumes. Please try
                        again.
                    </p>
                    <Button
                        onClick={() =>
                            client.invalidateQueries({ queryKey: ["resumes"] })
                        }
                        variant="outline"
                    >
                        Try Again
                    </Button>
                </div>
            </div>
        );
    }

    const resumeList = (resumes as Resume[]) || [];

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="container py-8"
        >
            <div className="mb-8 flex items-center justify-between">
                <div className="space-y-1">
                    <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                        Your Resumes
                    </h1>
                    <p className="text-muted-foreground">
                        Manage and organize your resumes for every opportunity.
                    </p>
                </div>
                <Button
                    onClick={handleUploadClick}
                    className="h-11"
                    disabled={isNavigating}
                >
                    {isNavigating ? (
                        <div className="flex items-center gap-2">
                            <LoadingAnimation
                                variant="pulse"
                                size="sm"
                                className="text-current"
                            />
                            <span>Loading...</span>
                        </div>
                    ) : (
                        <>
                            <Upload className="mr-2 h-5 w-5" />
                            Upload Resume
                        </>
                    )}
                </Button>
            </div>

            {resumeList.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {resumeList.map((resume: Resume) => (
                        <ResumeCard
                            key={resume.id}
                            resume={resume}
                            onSetDefault={handleSetDefault}
                            onDelete={handleDelete}
                            isDeleting={deletingResumeIds.has(resume.id)}
                        />
                    ))}
                </div>
            ) : (
                <ResumesEmptyState
                    onUploadClick={handleUploadClick}
                    isNavigating={isNavigating}
                />
            )}
        </motion.div>
    );
}
