import Link from "next/link";
import { redirect } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle, Upload } from "lucide-react";
import { Breadcrumb } from "@/components/breadcrumb";
import { ApplicationsList } from "./applications-list";
import { SearchFilters } from "./search-filters";
import { Suspense } from "react";
import { getServerAuth } from "@/lib/auth";
import { currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { resumes } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
// Make the page dynamic to ensure it re-renders with new search params
export const dynamic = "force-dynamic";

export default async function ApplicationsPage() {
    const auth = await getServerAuth();
    const user = await currentUser();
    if (!auth.userId || !user) {
        redirect("/sign-in");
    }

    // Check if user has any resumes
    const userResumes = await db
        .select()
        .from(resumes)
        .where(eq(resumes.userId, auth.userId));

    const hasResumes = userResumes.length > 0;

    return (
        <div className="space-y-6">
            <div>
                <Breadcrumb
                    items={[
                        {
                            label: "Applications",
                            href: "/dashboard/applications",
                            active: true,
                        },
                    ]}
                />
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <h1 className="text-3xl font-bold tracking-tight">
                        Applications
                    </h1>
                    <div className="flex items-center gap-2">
                        <Link href="/dashboard/applications/new">
                            <Button size="sm" className="gap-1">
                                <PlusCircle className="h-4 w-4" />
                                <span>New Application</span>
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
            {!hasResumes && (
                <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4 flex flex-row gap-2 justify-between items-center">
                    <p className="text-amber-800 text-sm">
                        You haven't uploaded a resume yet. Adding a resume will
                        help you apply to jobs more efficiently.
                    </p>
                    <Link href="/dashboard/resumes/upload">
                        <Button size="sm" variant="outline">
                            <Upload className="h-4 w-4 mr-1" />
                            <span>Upload Resume</span>
                        </Button>
                    </Link>
                </div>
            )}

            {/* Status filter badges */}
            <Suspense
                fallback={
                    <div className="grid gap-4">
                        {[1, 2, 3].map((i) => (
                            <div
                                key={i}
                                className="h-12 w-full bg-muted animate-pulse rounded-md"
                            />
                        ))}
                    </div>
                }
            >
                <SearchFilters />
            </Suspense>

            {/* Search and Applications List */}
            <Suspense
                fallback={
                    <div className="grid gap-4">
                        {[1, 2, 3].map((i) => (
                            <div
                                key={i}
                                className="h-32 w-full bg-muted animate-pulse rounded-md"
                            />
                        ))}
                    </div>
                }
            >
                <ApplicationsList />
            </Suspense>
        </div>
    );
}
