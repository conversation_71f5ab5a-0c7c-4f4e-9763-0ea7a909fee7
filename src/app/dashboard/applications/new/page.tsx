"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Wand2,
    Eye,
    EyeOff,
    ExternalLink,
    FileText,
    Link as LinkIcon,
    Copy,
    AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { LoadingAnimation } from "@/components/ui/loading-animation";
import { useResumes } from "@/lib/hooks/api-hooks";
import { queryClient } from "@/lib/query-client";
import { Switch } from "@/components/ui/switch";
import { z } from "zod";
import { sanitizeHtml } from "@/lib/utils";
// Define FormData interface for type safety
interface FormData {
    company: string;
    position: string;
    jobDescription: string;
    jobUrl: string;
    location: string;
    salary: string;
    applicationUrl: string;
    contactName: string;
    contactEmail: string;
    contactPhone: string;
    status: string;
    notes: string;
    resumeId: string;
}

// URL validation schema
const urlSchema = z
    .string()
    .trim()
    .url("Please enter a valid URL with http:// or https://")
    .max(2048, "URL is too long");

export default function NewApplicationPage() {
    const router = useRouter();
    const { user } = useUser();
    const { data: resumes, isLoading: isLoadingResumes } = useResumes();
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [error, setError] = useState("");
    const [jobAnalysisResults, setJobAnalysisResults] = useState<any>(null);
    const [showRawJD, setShowRawJD] = useState(false);
    const [expandedSection, setExpandedSection] = useState<string | null>(null);
    const [useJobUrl, setUseJobUrl] = useState(false);
    const [urlValidationError, setUrlValidationError] = useState<string | null>(
        null
    );

    const [formData, setFormData] = useState<FormData>({
        company: "",
        position: "",
        jobDescription: "",
        jobUrl: "",
        location: "",
        salary: "",
        applicationUrl: "",
        contactName: "",
        contactEmail: "",
        contactPhone: "",
        status: "not_applied",
        notes: "",
        resumeId: "",
    });

    // Set default resume when resumes load
    useEffect(() => {
        if (resumes?.length && !formData.resumeId) {
            const defaultResume = resumes.find(
                (resume: any) => resume.isDefault
            );
            const resumeId = defaultResume?.id || resumes[0]?.id;
            if (resumeId) {
                setFormData((prev) => ({
                    ...prev,
                    resumeId: resumeId.toString(),
                }));
            }
        }
    }, [resumes, formData.resumeId]);

    // Handle input changes
    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));

        // Clear URL validation error when input changes
        if (name === "jobUrl") {
            setUrlValidationError(null);
        }
    };

    // Handle select changes
    const handleSelectChange = (name: string, value: string) => {
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    // Handle toggle switch change
    const handleToggleChange = (checked: boolean) => {
        setUseJobUrl(checked);
        // Reset analysis results when switching modes
        setJobAnalysisResults(null);
    };

    // Validate URL
    const validateJobUrl = (url: string): boolean => {
        try {
            urlSchema.parse(url);
            setUrlValidationError(null);
            return true;
        } catch (err) {
            if (err instanceof z.ZodError) {
                setUrlValidationError(err.errors[0].message);
            }
            return false;
        }
    };

    // Analyze job description or URL
    const analyzeJob = async () => {
        if (useJobUrl) {
            // Validate URL before proceeding
            if (!validateJobUrl(formData.jobUrl)) {
                return;
            }

            await analyzeJobUrl();
        } else {
            // Use existing job description analysis
            await analyzeJobDescription();
        }
    };

    // Analyze job URL
    const analyzeJobUrl = async () => {
        if (!formData.jobUrl.trim()) {
            toast.error("Please enter a job URL to analyze");
            return;
        }

        try {
            setIsAnalyzing(true);
            setError("");

            const response = await fetch("/api/ai/analyze-job-link", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    jobUrl: formData.jobUrl.trim(),
                }),
                credentials: "include",
            });

            const data = await response.json();
            setJobAnalysisResults(data);

            // Show appropriate toast based on success
            if (data.success) {
                toast.success("Job URL analyzed successfully");
            } else if (data.userMessage) {
                toast.warning(data.userMessage);
            } else if (data.error) {
                toast.error(data.error);
            } else if (!response.ok) {
                toast.error(
                    "Failed to analyze job URL. Please try again or enter details manually."
                );
            }

            // Update form data with the results
            setFormData((prev) => ({
                ...prev,
                company: data.company || prev.company,
                position: data.position || prev.position,
                location: data.location || prev.location,
                salary: data.salary || prev.salary,
                applicationUrl:
                    data.applicationUrl ||
                    formData.jobUrl ||
                    prev.applicationUrl,
                notes: data.notes
                    ? `${prev.notes}\n\nSkills Required:\n${data.notes}`
                    : prev.notes,
                jobDescription: data.originalContent || prev.jobDescription,
            }));
        } catch (err) {
            console.error("Error analyzing job URL:", err);
            setError(
                "Failed to analyze job URL. Please try again or enter details manually."
            );
            toast.error(
                "Error analyzing job URL. Please try again or enter details manually."
            );
        } finally {
            setIsAnalyzing(false);
        }
    };

    // Analyze job description
    const analyzeJobDescription = async () => {
        if (!formData.jobDescription.trim()) {
            toast.error("Please enter a job description to analyze");
            return;
        }

        try {
            setIsAnalyzing(true);
            setError("");

            const response = await fetch("/api/ai/analyze-job", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    jobDescription: formData.jobDescription,
                }),
                credentials: "include",
            });

            if (!response.ok)
                throw new Error("Failed to analyze job description");

            const data = await response.json();
            setJobAnalysisResults(data);

            setFormData((prev) => ({
                ...prev,
                company: data.company || prev.company,
                position: data.position || prev.position,
                location: data.location || prev.location,
                salary: data.salary || prev.salary,
                applicationUrl: data.applicationUrl || prev.applicationUrl,
                notes: data.notes
                    ? `${prev.notes}\n\nSkills Required:\n${data.notes}`
                    : prev.notes,
            }));

            toast.success("Job description analyzed successfully");
        } catch (err) {
            console.error(err);
            toast.error("Failed to analyze job description");
        } finally {
            setIsAnalyzing(false);
        }
    };

    // Create application API call
    const createApplication = async (applicationData: any) => {
        const response = await fetch("/api/applications", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(applicationData),
            credentials: "include",
        });

        if (!response.ok) throw new Error("Failed to create application");
        return response.json();
    };

    // TanStack Query mutation
    const mutation = useMutation({
        mutationFn: createApplication,
        onSuccess: (data) => {
            if (formData.jobDescription) {
                router.push(`/dashboard/applications/${data.id}/content`);
            } else {
                router.push("/dashboard/applications");
            }
            queryClient.invalidateQueries({ queryKey: ["applications"] });
        },
        onError: (error) => {
            setError(error.message || "Failed to create application");
        },
    });

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.company || !formData.position) {
            setError("Company and position are required");
            return;
        }

        if (!user) {
            setError("You must be logged in to create an application");
            return;
        }

        const applicationData = {
            ...formData,
            resumeId: formData.resumeId ? parseInt(formData.resumeId) : null,
            parsedJobDescription: jobAnalysisResults,
            // Include jobLink if we used a URL
            jobLink: useJobUrl ? formData.jobUrl : undefined,
        };

        mutation.mutate(applicationData);
    };

    // Utility to format values
    const formatValue = (value: any): string => {
        if (Array.isArray(value)) return value.join(", ");
        return value?.toString() || "Not specified";
    };

    // Render URL as a clickable link
    const renderLink = (url: string) => {
        if (!url) return null;
        return (
            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 inline-flex items-center gap-1"
            >
                {url} <ExternalLink className="h-3 w-3" />
            </a>
        );
    };

    return (
        <div className="container py-6">
            <div className="mb-8">
                <Link
                    href="/dashboard/applications"
                    className="text-sm text-primary"
                >
                    ← Back to Applications
                </Link>
                <h1 className="mt-2 text-3xl font-bold">Add New Application</h1>
            </div>

            <div className="mx-auto max-w-3xl rounded-lg border bg-card p-6 shadow-xs">
                <form onSubmit={handleSubmit}>
                    {/* Job Description Section */}
                    <div className="mb-6 space-y-4">
                        <div className="flex items-center justify-between">
                            <Label
                                htmlFor={
                                    useJobUrl ? "jobUrl" : "jobDescription"
                                }
                                className="text-lg font-medium"
                            >
                                {useJobUrl ? "Job URL" : "Job Description"}
                            </Label>

                            <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-2">
                                    <span className="text-sm">Description</span>
                                    <Switch
                                        checked={useJobUrl}
                                        onCheckedChange={handleToggleChange}
                                        id="input-mode-toggle"
                                    />
                                    <span className="text-sm">URL</span>
                                </div>

                                {jobAnalysisResults && !useJobUrl && (
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setShowRawJD(!showRawJD)}
                                        className="text-sm"
                                    >
                                        {showRawJD ? (
                                            <>
                                                <EyeOff className="mr-2 h-4 w-4" />
                                                Hide Raw JD
                                            </>
                                        ) : (
                                            <>
                                                <Eye className="mr-2 h-4 w-4" />
                                                Show Raw JD
                                            </>
                                        )}
                                    </Button>
                                )}
                            </div>
                        </div>

                        {useJobUrl ? (
                            <div className="relative">
                                <Input
                                    id="jobUrl"
                                    name="jobUrl"
                                    type="url"
                                    value={formData.jobUrl}
                                    onChange={handleChange}
                                    placeholder="https://example.com/jobs/software-engineer"
                                    className={`w-full ${
                                        urlValidationError
                                            ? "border-red-500"
                                            : ""
                                    }`}
                                />
                                {urlValidationError && (
                                    <p className="text-red-500 text-sm mt-1">
                                        {urlValidationError}
                                    </p>
                                )}
                                <Button
                                    type="button"
                                    onClick={analyzeJob}
                                    disabled={isAnalyzing || !formData.jobUrl}
                                    className="mt-2 w-full"
                                    variant="secondary"
                                >
                                    {isAnalyzing ? (
                                        <LoadingAnimation
                                            variant="ripple"
                                            size="sm"
                                            text="Analyzing..."
                                            className="mr-2 inline-block"
                                        />
                                    ) : (
                                        <>
                                            <LinkIcon className="mr-2 h-4 w-4" />
                                            Analyze Job URL
                                        </>
                                    )}
                                </Button>
                            </div>
                        ) : showRawJD ? (
                            <Textarea
                                id="jobDescription"
                                name="jobDescription"
                                value={formData.jobDescription}
                                onChange={handleChange}
                                placeholder="Paste the job description here to auto-fill application details"
                                className="min-h-[200px]"
                            />
                        ) : (
                            <div className="relative">
                                <Textarea
                                    id="jobDescription"
                                    name="jobDescription"
                                    value={formData.jobDescription}
                                    onChange={handleChange}
                                    placeholder="Paste the job description here to auto-fill application details"
                                    className="min-h-[200px]"
                                />
                                <Button
                                    type="button"
                                    onClick={analyzeJob}
                                    disabled={
                                        isAnalyzing || !formData.jobDescription
                                    }
                                    className="mt-2 w-full"
                                    variant="secondary"
                                >
                                    {isAnalyzing ? (
                                        <LoadingAnimation
                                            variant="ripple"
                                            size="sm"
                                            text="Analyzing..."
                                            className="mr-2 inline-block"
                                        />
                                    ) : (
                                        <>
                                            <Wand2 className="mr-2 h-4 w-4" />
                                            Analyze Job Description
                                        </>
                                    )}
                                </Button>
                            </div>
                        )}

                        <p className="text-sm text-muted-foreground">
                            {useJobUrl
                                ? "Enter the job posting URL to automatically extract job details. We'll also create a shortened URL for your convenience. Some job sites like LinkedIn may have limited data extraction."
                                : "Paste the job description to automatically extract company, position, location, and other details."}
                        </p>

                        {jobAnalysisResults && (useJobUrl || !showRawJD) && (
                            <div className="mt-4 space-y-4">
                                <div className="rounded-lg border bg-muted/20 divide-y">
                                    {/* Add error message display if analysis failed */}
                                    {!jobAnalysisResults.success &&
                                        jobAnalysisResults.userMessage && (
                                            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800">
                                                <div className="flex items-start">
                                                    <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-500 mr-2 mt-0.5" />
                                                    <p className="text-sm text-yellow-800 dark:text-yellow-200">
                                                        {
                                                            jobAnalysisResults.userMessage
                                                        }
                                                    </p>
                                                </div>
                                            </div>
                                        )}

                                    {/* Company & Position */}
                                    <div className="p-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <h4 className="font-semibold mb-1">
                                                    Company
                                                </h4>
                                                <p>
                                                    {jobAnalysisResults.company ||
                                                        "Not specified"}
                                                </p>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold mb-1">
                                                    Position
                                                </h4>
                                                <p>
                                                    {jobAnalysisResults.position ||
                                                        "Not specified"}
                                                </p>
                                            </div>
                                        </div>

                                        {/* Original URL */}
                                        {jobAnalysisResults.originalUrl && (
                                            <div className="mt-2">
                                                <h4 className="font-semibold mb-1">
                                                    Original URL
                                                </h4>
                                                {renderLink(
                                                    jobAnalysisResults.originalUrl
                                                )}
                                            </div>
                                        )}

                                        {/* Application URL */}
                                        {(jobAnalysisResults.applicationUrl ||
                                            formData.jobUrl) && (
                                            <div className="mt-2">
                                                <h4 className="font-semibold mb-1">
                                                    Application URL
                                                </h4>
                                                {renderLink(
                                                    jobAnalysisResults.applicationUrl ||
                                                        formData.jobUrl
                                                )}
                                            </div>
                                        )}
                                    </div>

                                    {/* Skills */}
                                    <div className="p-4">
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setExpandedSection(
                                                    expandedSection === "skills"
                                                        ? null
                                                        : "skills"
                                                )
                                            }
                                            className="flex justify-between items-center w-full text-left"
                                        >
                                            <h4 className="font-semibold">
                                                Required Skills
                                            </h4>
                                            <span>
                                                {expandedSection === "skills"
                                                    ? "−"
                                                    : "+"}
                                            </span>
                                        </button>
                                        {expandedSection === "skills" && (
                                            <div className="mt-2 grid grid-cols-2 gap-2">
                                                {jobAnalysisResults.requiredSkills?.map(
                                                    (
                                                        skill: string,
                                                        i: number
                                                    ) => (
                                                        <div
                                                            key={i}
                                                            className="bg-muted/30 px-2 py-1 rounded text-sm"
                                                        >
                                                            {skill}
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        )}
                                    </div>

                                    {/* Experience */}
                                    <div className="p-4">
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setExpandedSection(
                                                    expandedSection ===
                                                        "experience"
                                                        ? null
                                                        : "experience"
                                                )
                                            }
                                            className="flex justify-between items-center w-full text-left"
                                        >
                                            <h4 className="font-semibold">
                                                Experience & Qualifications
                                            </h4>
                                            <span>
                                                {expandedSection ===
                                                "experience"
                                                    ? "−"
                                                    : "+"}
                                            </span>
                                        </button>
                                        {expandedSection === "experience" && (
                                            <div className="mt-2 space-y-2">
                                                <div>
                                                    <p className="font-medium text-sm">
                                                        Experience Required:
                                                    </p>
                                                    <p className="text-sm">
                                                        {jobAnalysisResults.requiredExperience ||
                                                            "Not specified"}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="font-medium text-sm">
                                                        Key Qualifications:
                                                    </p>
                                                    <ul className="list-disc list-inside text-sm">
                                                        {jobAnalysisResults.keyQualifications?.map(
                                                            (
                                                                qual: string,
                                                                i: number
                                                            ) => (
                                                                <li key={i}>
                                                                    {qual}
                                                                </li>
                                                            )
                                                        )}
                                                    </ul>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Responsibilities */}
                                    <div className="p-4">
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setExpandedSection(
                                                    expandedSection ===
                                                        "responsibilities"
                                                        ? null
                                                        : "responsibilities"
                                                )
                                            }
                                            className="flex justify-between items-center w-full text-left"
                                        >
                                            <h4 className="font-semibold">
                                                Job Responsibilities
                                            </h4>
                                            <span>
                                                {expandedSection ===
                                                "responsibilities"
                                                    ? "−"
                                                    : "+"}
                                            </span>
                                        </button>
                                        {expandedSection ===
                                            "responsibilities" && (
                                            <ul className="mt-2 list-disc list-inside text-sm">
                                                {jobAnalysisResults.jobResponsibilities?.map(
                                                    (
                                                        resp: string,
                                                        i: number
                                                    ) => (
                                                        <li key={i}>{resp}</li>
                                                    )
                                                )}
                                            </ul>
                                        )}
                                    </div>

                                    {/* Additional Info */}
                                    <div className="p-4">
                                        <button
                                            type="button"
                                            onClick={() =>
                                                setExpandedSection(
                                                    expandedSection ===
                                                        "additional"
                                                        ? null
                                                        : "additional"
                                                )
                                            }
                                            className="flex justify-between items-center w-full text-left"
                                        >
                                            <h4 className="font-semibold">
                                                Additional Information
                                            </h4>
                                            <span>
                                                {expandedSection ===
                                                "additional"
                                                    ? "−"
                                                    : "+"}
                                            </span>
                                        </button>
                                        {expandedSection === "additional" && (
                                            <div className="mt-2 space-y-3">
                                                {jobAnalysisResults.salary && (
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Salary Range:
                                                        </p>
                                                        <p className="text-sm">
                                                            {
                                                                jobAnalysisResults.salary
                                                            }
                                                        </p>
                                                    </div>
                                                )}
                                                {jobAnalysisResults.location && (
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Location:
                                                        </p>
                                                        <p className="text-sm">
                                                            {
                                                                jobAnalysisResults.location
                                                            }
                                                        </p>
                                                    </div>
                                                )}
                                                {jobAnalysisResults
                                                    .companyValues?.length >
                                                    0 && (
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Company Values:
                                                        </p>
                                                        <p className="text-sm">
                                                            {jobAnalysisResults.companyValues.join(
                                                                ", "
                                                            )}
                                                        </p>
                                                    </div>
                                                )}
                                                {jobAnalysisResults.tips
                                                    ?.length > 0 && (
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Application Tips:
                                                        </p>
                                                        <ul className="list-disc list-inside text-sm">
                                                            {jobAnalysisResults.tips.map(
                                                                (
                                                                    tip: string,
                                                                    i: number
                                                                ) => (
                                                                    <li key={i}>
                                                                        {tip}
                                                                    </li>
                                                                )
                                                            )}
                                                        </ul>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Form Fields */}
                    <div className="mb-6 grid gap-6 md:grid-cols-2">
                        <div className="space-y-2">
                            <Label htmlFor="company">Company Name *</Label>
                            <Input
                                id="company"
                                name="company"
                                value={formData.company}
                                onChange={handleChange}
                                placeholder="e.g., Garden"
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="position">Position *</Label>
                            <Input
                                id="position"
                                name="position"
                                value={formData.position}
                                onChange={handleChange}
                                placeholder="e.g., Full Stack Product Engineer"
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="location">Location</Label>
                            <Input
                                id="location"
                                name="location"
                                value={formData.location}
                                onChange={handleChange}
                                placeholder="e.g., New York, NY"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="salary">Salary Range</Label>
                            <Input
                                id="salary"
                                name="salary"
                                value={formData.salary}
                                onChange={handleChange}
                                placeholder="e.g., $100,000 - $120,000"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="applicationUrl">
                                Application URL
                            </Label>
                            <Input
                                id="applicationUrl"
                                name="applicationUrl"
                                value={formData.applicationUrl}
                                onChange={handleChange}
                                placeholder="e.g., https://company.com/careers/job123"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="status">Application Status</Label>
                            <Select
                                value={formData.status}
                                onValueChange={(value) =>
                                    handleSelectChange("status", value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    {[
                                        "not_applied",
                                        "applied",
                                        "interviewing",
                                        "offer",
                                        "rejected",
                                        "accepted",
                                    ].map((status) => (
                                        <SelectItem key={status} value={status}>
                                            {status.replace("_", " ")}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Contact Information */}
                    <div className="mb-6 grid gap-6 md:grid-cols-3">
                        <div className="space-y-2">
                            <Label htmlFor="contactName">Contact Name</Label>
                            <Input
                                id="contactName"
                                name="contactName"
                                value={formData.contactName}
                                onChange={handleChange}
                                placeholder="e.g., John Smith"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="contactEmail">Contact Email</Label>
                            <Input
                                id="contactEmail"
                                name="contactEmail"
                                value={formData.contactEmail}
                                onChange={handleChange}
                                placeholder="e.g., <EMAIL>"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="contactPhone">Contact Phone</Label>
                            <Input
                                id="contactPhone"
                                name="contactPhone"
                                value={formData.contactPhone}
                                onChange={handleChange}
                                placeholder="e.g., (*************"
                            />
                        </div>
                    </div>

                    {/* Notes */}
                    <div className="space-y-6 mb-6">
                        <div className="space-y-2">
                            <Label htmlFor="notes">Notes</Label>
                            <Textarea
                                id="notes"
                                name="notes"
                                value={formData.notes}
                                onChange={handleChange}
                                placeholder="Add any notes about this application"
                                className="min-h-[100px]"
                            />
                        </div>
                    </div>

                    {/* Error Message */}
                    {error && (
                        <div className="mb-4 rounded-md bg-destructive/10 p-3 text-sm text-destructive">
                            {error}
                        </div>
                    )}

                    {/* Resume Selection and Buttons */}
                    <div className="flex flex-col gap-4">
                        <div className="flex items-center gap-3 rounded-lg border bg-card p-3">
                            <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                    <Label className="text-sm font-medium">
                                        Select Resume
                                    </Label>
                                    <Link
                                        href="/dashboard/resumes/new"
                                        className="text-xs font-medium text-primary hover:text-primary/80 transition-colors"
                                    >
                                        + Upload New
                                    </Link>
                                </div>
                                {isLoadingResumes ? (
                                    <div className="h-8 flex items-center">
                                        <LoadingAnimation
                                            variant="ripple"
                                            size="sm"
                                            text="Loading resumes..."
                                        />
                                    </div>
                                ) : (
                                    <Select
                                        value={formData.resumeId}
                                        onValueChange={(value) =>
                                            handleSelectChange(
                                                "resumeId",
                                                value
                                            )
                                        }
                                    >
                                        <SelectTrigger className="border-0 bg-transparent p-0 h-auto hover:bg-transparent focus:ring-0 [&>span]:p-0">
                                            <SelectValue>
                                                {formData.resumeId ? (
                                                    <div className="flex items-center gap-2">
                                                        <FileText className="h-4 w-4 text-primary" />
                                                        <div>
                                                            <span className="text-sm font-medium">
                                                                {
                                                                    resumes?.find(
                                                                        (
                                                                            r: any
                                                                        ) =>
                                                                            r.id.toString() ===
                                                                            formData.resumeId
                                                                    )?.name
                                                                }
                                                            </span>
                                                            <span className="ml-2 text-xs text-muted-foreground">
                                                                Updated{" "}
                                                                {new Date(
                                                                    resumes?.find(
                                                                        (
                                                                            r: any
                                                                        ) =>
                                                                            r.id.toString() ===
                                                                            formData.resumeId
                                                                    )?.updatedAt
                                                                ).toLocaleDateString()}
                                                            </span>
                                                            {resumes?.find(
                                                                (r: any) =>
                                                                    r.id.toString() ===
                                                                    formData.resumeId
                                                            )?.isDefault && (
                                                                <span className="ml-2 text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                                                                    Default
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <span className="text-sm text-muted-foreground">
                                                        Choose a resume
                                                    </span>
                                                )}
                                            </SelectValue>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {resumes?.map((resume: any) => (
                                                <SelectItem
                                                    key={resume.id}
                                                    value={resume.id.toString()}
                                                    className="py-1.5"
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <FileText className="h-4 w-4 text-primary" />
                                                        <div>
                                                            <span className="text-sm font-medium">
                                                                {resume.name}
                                                                {resume.isDefault && (
                                                                    <span className="ml-2 text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                                                                        Default
                                                                    </span>
                                                                )}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )}
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center justify-end gap-3">
                            <Link href="/dashboard/applications">
                                <Button
                                    variant="outline"
                                    type="button"
                                    className="min-w-[100px]"
                                >
                                    Cancel
                                </Button>
                            </Link>
                            <Button
                                type="submit"
                                disabled={
                                    mutation.isPending ||
                                    isAnalyzing ||
                                    isLoadingResumes
                                }
                                className="min-w-[140px]"
                            >
                                {mutation.isPending ? (
                                    <LoadingAnimation
                                        variant="ripple"
                                        size="sm"
                                        text="Saving..."
                                        className="mr-2 inline-block"
                                    />
                                ) : (
                                    "Save Application"
                                )}
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
