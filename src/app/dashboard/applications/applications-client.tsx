"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import Link from "next/link";
import {
    useFilteredApplications,
    VALID_STATUSES,
    type ApplicationStatus,
    useDeleteApplication,
} from "@/lib/hooks/api-hooks";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    CheckCircle,
    XCircle,
    Clock,
    Calendar,
    PhoneCall,
    Award,
    Loader2,
    MoreVertical,
    Trash2,
    ExternalLink,
    Edit,
    PlusCircle,
} from "lucide-react";
import { format } from "date-fns";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertD<PERSON>ogFooter,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>,
    <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

// Status display configuration
const statusConfig = {
    not_applied: {
        icon: Calendar,
        color: "text-blue-700 bg-blue-100 border border-blue-200 dark:text-blue-500 dark:bg-blue-950/40 dark:border-blue-500/20",
        label: "Not Applied",
    },
    applied: {
        icon: Clock,
        color: "text-amber-700 bg-amber-100 border border-amber-200 dark:text-yellow-500 dark:bg-yellow-950/40 dark:border-yellow-500/20",
        label: "Applied",
    },
    interviewing: {
        icon: PhoneCall,
        color: "text-purple-700 bg-purple-100 border border-purple-200 dark:text-purple-500 dark:bg-purple-950/40 dark:border-purple-500/20",
        label: "Interviewing",
    },
    offer: {
        icon: Award,
        color: "text-orange-700 bg-orange-100 border border-orange-200 dark:text-orange-500 dark:bg-orange-950/40 dark:border-orange-500/20",
        label: "Offer",
    },
    accepted: {
        icon: CheckCircle,
        color: "text-green-700 bg-green-100 border border-green-200 dark:text-green-500 dark:bg-green-950/40 dark:border-green-500/20",
        label: "Accepted",
    },
    rejected: {
        icon: XCircle,
        color: "text-red-700 bg-red-100 border border-red-200 dark:text-red-500 dark:bg-red-950/40 dark:border-red-500/20",
        label: "Rejected",
    },
};

// Application Card Component for better organization
const ApplicationCard = ({
    application,
    isHovered,
    handleDeleteClick,
    router,
}: {
    application: any;
    isHovered: boolean;
    handleDeleteClick: (e: React.MouseEvent, id: string, name: string) => void;
    router: any;
}) => {
    const statusKey =
        (application.status as ApplicationStatus) || "not_applied";
    const StatusIcon = statusConfig[statusKey]?.icon || Calendar;

    // Format date nicely
    const formattedDate = format(
        new Date(application.createdAt || new Date()),
        "MMM d, yyyy"
    );

    // Extract company initials for the avatar
    const getInitials = (name: string) => {
        return name
            .split(" ")
            .map((word) => word[0])
            .join("")
            .toUpperCase()
            .substring(0, 2);
    };

    const companyName = application.company || "Unnamed Company";
    const companyInitials = getInitials(companyName);

    // Generate a consistent pseudo-random color for the company avatar
    const getCompanyColor = (name: string) => {
        const colors = [
            "bg-blue-100 text-blue-700 border-blue-200 dark:bg-primary/10 dark:text-primary dark:border-primary/20",
            "bg-purple-100 text-purple-700 border-purple-200 dark:bg-primary/10 dark:text-primary dark:border-primary/20",
            "bg-pink-100 text-pink-700 border-pink-200 dark:bg-primary/10 dark:text-primary dark:border-primary/20",
            "bg-indigo-100 text-indigo-700 border-indigo-200 dark:bg-primary/10 dark:text-primary dark:border-primary/20",
            "bg-teal-100 text-teal-700 border-teal-200 dark:bg-primary/10 dark:text-primary dark:border-primary/20",
            "bg-cyan-100 text-cyan-700 border-cyan-200 dark:bg-primary/10 dark:text-primary dark:border-primary/20",
        ];

        // Simple hash function to get consistent color based on name
        let hash = 0;
        for (let i = 0; i < name.length; i++) {
            hash = name.charCodeAt(i) + ((hash << 5) - hash);
        }

        const index = Math.abs(hash) % colors.length;
        return colors[index];
    };

    const avatarColor = getCompanyColor(companyName);

    return (
        <div
            className="relative group"
            onMouseEnter={() => {}}
            onMouseLeave={() => {}}
        >
            <Link
                href={`/dashboard/applications/${application.id}`}
                className="block"
            >
                <Card
                    className={cn(
                        "cursor-pointer transition-all duration-300 overflow-hidden",
                        "border border-border/30 bg-background",
                        "hover:bg-accent/5 hover:border-border/50",
                        "hover:shadow-[0_4px_20px_-4px_rgba(0,0,0,0.1)]",
                        "dark:bg-background/70 dark:backdrop-blur-sm",
                        "dark:hover:bg-accent/10 dark:hover:shadow-lg",
                        "transform hover:-translate-y-1",
                        "active:scale-[0.99] sm:active:scale-100" // Touch feedback on mobile
                    )}
                >
                    <CardContent className="p-3 sm:p-4 md:p-6">
                        <div className="flex items-start gap-3 sm:gap-4">
                            {/* Company Avatar */}
                            <div
                                className={cn(
                                    "flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-md flex items-center justify-center text-base sm:text-lg font-semibold",
                                    "border transition-transform duration-300 group-hover:scale-110 shadow-sm",
                                    avatarColor
                                )}
                            >
                                {companyInitials}
                            </div>

                            <div className="flex-1 min-w-0 pr-8 sm:pr-4">
                                {" "}
                                {/* Space for the action button on mobile */}
                                <div className="flex flex-col gap-1 sm:gap-2 mb-1 sm:mb-2">
                                    <h3 className="font-semibold text-base sm:text-lg truncate text-foreground">
                                        {companyName}
                                    </h3>
                                    <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                        {application.position || "No Position"}
                                        {application.location &&
                                            ` • ${application.location}`}
                                    </p>
                                </div>
                                <div className="flex flex-wrap items-center gap-2 mt-2 sm:mt-3">
                                    <div
                                        className={cn(
                                            "px-2 py-0.5 sm:px-2.5 sm:py-1 rounded-full text-xs font-medium flex items-center gap-1 sm:gap-1.5 shadow-sm",
                                            statusConfig[statusKey]?.color ||
                                                "",
                                            "transition-transform duration-200 hover:scale-105"
                                        )}
                                    >
                                        <StatusIcon className="h-3 w-3" />
                                        <span className="text-[10px] sm:text-xs">
                                            {statusConfig[statusKey]?.label ||
                                                statusKey}
                                        </span>
                                    </div>

                                    <div className="text-[10px] sm:text-xs text-muted-foreground flex items-center">
                                        <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1 opacity-70" />
                                        {formattedDate}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>

                    {/* Visual cue for interaction */}
                    <div
                        className={cn(
                            "absolute inset-0 opacity-0",
                            "bg-gradient-to-r from-transparent to-primary/5 dark:bg-gradient-to-tr dark:from-transparent dark:via-primary/5 dark:to-primary/10",
                            "transition-opacity duration-300",
                            isHovered && "opacity-100"
                        )}
                    />

                    {/* Subtle accent line at the top */}
                    <div
                        className={cn(
                            "absolute top-0 left-0 w-full h-0.5",
                            "bg-gradient-to-r from-transparent via-primary/30 to-transparent",
                            "opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                        )}
                    />
                </Card>
            </Link>

            {/* Actions Menu - Always visible on mobile, hover on desktop */}
            <div
                className={cn(
                    "absolute top-2 sm:top-3 right-2 sm:right-3 z-10",
                    "transition-opacity duration-200",
                    "opacity-100 sm:opacity-70", // Always visible on mobile
                    isHovered && "sm:opacity-100"
                )}
                onClick={(e) => e.stopPropagation()}
            >
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="secondary"
                            size="sm"
                            className={cn(
                                "h-7 w-7 sm:h-8 sm:w-8 p-0 sm:p-2 rounded-full",
                                "shadow-sm border border-border/30",
                                "bg-background/90 backdrop-blur-sm",
                                "hover:bg-accent/10 hover:shadow-md",
                                "text-muted-foreground"
                            )}
                            onClick={(e) => e.stopPropagation()}
                            aria-label="Options"
                        >
                            <MoreVertical className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                            <span className="sr-only">Options</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-44 sm:w-48">
                        <DropdownMenuItem
                            className="flex items-center cursor-pointer text-xs sm:text-sm"
                            onClick={(e) => {
                                e.preventDefault();
                                router.push(
                                    `/dashboard/applications/${application.id}`
                                );
                            }}
                        >
                            <ExternalLink className="mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                            View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            className="flex items-center cursor-pointer text-xs sm:text-sm"
                            onClick={(e) => {
                                e.preventDefault();
                                router.push(
                                    `/dashboard/applications/${application.id}/content`
                                );
                            }}
                        >
                            <Edit className="mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                            Edit Content
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                            className="text-destructive flex items-center cursor-pointer text-xs sm:text-sm"
                            onClick={(e) =>
                                handleDeleteClick(
                                    e,
                                    application.id.toString(),
                                    application.company || "this application"
                                )
                            }
                        >
                            <Trash2 className="mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                            Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>
    );
};

export function ApplicationsClient() {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [applicationToDelete, setApplicationToDelete] = useState<
        string | null
    >(null);
    const [applicationToDeleteName, setApplicationToDeleteName] =
        useState<string>("");
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [hoveredId, setHoveredId] = useState<string | null>(null);

    const { mutate: deleteApplication, isPending: isDeleting } =
        useDeleteApplication();

    // Safely get status from URL
    const getStatusFromUrl = (): ApplicationStatus | "all" => {
        try {
            const statusParam = searchParams?.get("status");
            if (!statusParam) return "all";

            // Validate that it's a valid status
            if (
                statusParam === "all" ||
                VALID_STATUSES.includes(statusParam as ApplicationStatus)
            ) {
                return statusParam as ApplicationStatus | "all";
            }
            return "all";
        } catch (e) {
            console.error("Error parsing status from URL", e);
            return "all";
        }
    };

    // Get status filter from URL with safe fallback
    const [status, setStatus] = useState<ApplicationStatus | "all">(
        getStatusFromUrl()
    );

    // Update local state when URL params change - with safe error handling
    useEffect(() => {
        try {
            if (searchParams) {
                const currentStatus = getStatusFromUrl();
                setStatus(currentStatus);
            }
        } catch (e) {
            console.error("Error updating from URL params", e);
        }
    }, [searchParams]);

    // Fetch filtered applications using React Query
    const {
        data: applications,
        isLoading,
        error,
    } = useFilteredApplications({
        status: status !== "all" ? status : undefined,
    });

    // Handle opening the delete confirmation dialog
    const handleDeleteClick = (
        e: React.MouseEvent,
        id: string,
        name: string
    ) => {
        e.preventDefault();
        e.stopPropagation();
        setApplicationToDelete(id);
        setApplicationToDeleteName(name);
        setIsDeleteDialogOpen(true);
    };

    // Confirm deletion
    const confirmDelete = () => {
        if (applicationToDelete) {
            deleteApplication(applicationToDelete);
            setIsDeleteDialogOpen(false);
            setApplicationToDelete(null);
            setApplicationToDeleteName("");
        }
    };

    return (
        <div className="space-y-4 sm:space-y-6">
            {/* Applications List */}
            {isLoading ? (
                <div className="flex justify-center items-center py-8 sm:py-12">
                    <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin text-primary" />
                </div>
            ) : error ? (
                <div className="text-center py-8 sm:py-12 text-destructive text-sm sm:text-base">
                    <p>Error loading applications. Please try again.</p>
                </div>
            ) : !applications || applications.length === 0 ? (
                <Card className="border-border/40 shadow-sm">
                    <CardContent className="py-10 sm:py-16 text-center px-4 sm:px-6">
                        <div className="max-w-sm mx-auto">
                            <div className="rounded-full bg-slate-100 dark:bg-muted w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-sm">
                                <Calendar className="h-6 w-6 sm:h-8 sm:w-8 text-slate-400 dark:text-muted-foreground" />
                            </div>
                            <h3 className="text-lg sm:text-xl font-medium mb-1 sm:mb-2 text-foreground">
                                No applications found
                            </h3>
                            <p className="text-xs sm:text-sm text-muted-foreground mb-4 sm:mb-6 px-2 sm:px-0">
                                Start tracking your job applications to organize
                                your job search.
                            </p>
                            <Link href="/dashboard/applications/new">
                                <Button
                                    size="sm"
                                    className="shadow-sm sm:text-sm h-8 sm:h-9"
                                >
                                    <PlusCircle className="mr-1.5 h-3.5 w-3.5" />
                                    Create New Application
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            ) : (
                <div className="grid gap-3 sm:gap-4">
                    {applications.map((application: any) => {
                        if (!application) return null;
                        const isHovered =
                            hoveredId === application.id.toString();

                        return (
                            <div
                                key={application.id}
                                onMouseEnter={() =>
                                    setHoveredId(application.id.toString())
                                }
                                onMouseLeave={() => setHoveredId(null)}
                            >
                                <ApplicationCard
                                    application={application}
                                    isHovered={isHovered}
                                    handleDeleteClick={handleDeleteClick}
                                    router={router}
                                />
                            </div>
                        );
                    })}
                </div>
            )}

            {/* Delete Confirmation Dialog */}
            <AlertDialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
            >
                <AlertDialogContent className="max-w-[90vw] sm:max-w-md border-destructive/20 shadow-lg p-4 sm:p-6">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="text-center text-destructive text-lg sm:text-xl">
                            Delete Application
                        </AlertDialogTitle>
                        <AlertDialogDescription className="text-center text-sm sm:text-base">
                            Are you sure you want to delete the application for{" "}
                            <span className="font-semibold text-foreground">
                                {applicationToDeleteName}
                            </span>
                            ?
                            <div className="mt-2 text-destructive font-medium text-xs sm:text-sm">
                                This action cannot be undone.
                            </div>
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0 mt-4 sm:mt-0">
                        <AlertDialogCancel className="mt-0 sm:mt-0 border-border/40 shadow-sm hover:bg-background/80 dark:hover:bg-accent/10 text-sm">
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmDelete}
                            disabled={isDeleting}
                            className={cn(
                                "bg-destructive text-destructive-foreground hover:bg-destructive/90",
                                "border border-destructive/30 shadow-sm",
                                "transition-all duration-200",
                                "text-sm"
                            )}
                        >
                            {isDeleting ? (
                                <>
                                    <Loader2 className="mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4 animate-spin" />
                                    Deleting...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                                    Delete Application
                                </>
                            )}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}
