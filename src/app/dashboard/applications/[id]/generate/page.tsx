"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Download, Copy, MessageSquare, FileText, Send } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { LoadingAnimation } from "@/components/ui/loading-animation";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";

export default function GenerateContentPage() {
    const params = useParams();
    const router = useRouter();
    const [application, setApplication] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [generating, setGenerating] = useState(false);
    const [activeTab, setActiveTab] = useState("cover-letter");
    const [resume, setResume] = useState<any>(null);
    const [generatedContent, setGeneratedContent] = useState({
        coverLetter: "",
        linkedinMessage: "",
        coldEmail: "",
    });
    const [displayMode, setDisplayMode] = useState<"edit" | "generate">("edit");
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchApplicationData = async () => {
            try {
                setLoading(true);
                const response = await fetch(`/api/applications/${params.id}`, {
                    credentials: "include",
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch application");
                }

                const data = await response.json();
                setApplication(data);

                // Fetch resume if available
                if (data.resumeId) {
                    const resumeResponse = await fetch(
                        `/api/resumes/${data.resumeId}`,
                        {
                            credentials: "include",
                        }
                    );
                    if (resumeResponse.ok) {
                        const resumeData = await resumeResponse.json();
                        setResume(resumeData);
                    }
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                toast.error("Failed to load application data");
            } finally {
                setLoading(false);
            }
        };

        fetchApplicationData();
    }, [params.id]);

    const handleGenerate = async (type: string) => {
        if (!application) return;

        const promptMap: { [key: string]: string } = {
            "cover-letter": "cover letter",
            "linkedin-message": "LinkedIn message",
            "cold-email": "cold email",
        };

        try {
            setError(null);
            setGenerating(true);
            setDisplayMode("generate");

            const response = await fetch("/api/ai/generate-content", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    type: promptMap[type],
                    application: {
                        company: application.company,
                        position: application.position,
                        description: application.description,
                    },
                    resume: {
                        text: resume?.text || "",
                    },
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(
                    errorData.error || "Failed to generate content"
                );
            }

            const data = await response.json();

            if (type === "cover-letter") {
                setGeneratedContent({
                    ...generatedContent,
                    coverLetter: data.content,
                });
            } else if (type === "linkedin-message") {
                setGeneratedContent({
                    ...generatedContent,
                    linkedinMessage: data.content,
                });
            } else if (type === "cold-email") {
                setGeneratedContent({
                    ...generatedContent,
                    coldEmail: data.content,
                });
            }

            toast.success(`${promptMap[type]} generated successfully!`);
        } catch (err: any) {
            setError(err.message || "Failed to generate content");
            toast.error(err.message || "Failed to generate content");
        } finally {
            setGenerating(false);
        }
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        toast.success("Copied to clipboard");
    };

    const downloadAsDocx = (content: string, filename: string) => {
        // This is a simplified version - in a real app, you'd use a library to create a proper DOCX
        const blob = new Blob([content], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    if (loading) {
        return (
            <div className="container py-8 flex items-center justify-center min-h-[60vh]">
                <LoadingAnimation
                    variant="ripple"
                    size="lg"
                    text="Loading application data..."
                    subText="Retrieving your application details..."
                    className="py-12"
                />
            </div>
        );
    }

    if (!application) {
        return (
            <div className="container py-8">
                <div className="text-center">
                    <p className="text-lg text-muted-foreground">
                        Application not found
                    </p>
                    <Link href="/dashboard/applications">
                        <Button className="mt-4">Back to Applications</Button>
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className="container py-6">
            <div className="mb-8">
                <div className="flex items-center justify-between">
                    <div>
                        <Link
                            href={`/dashboard/applications/${params.id}`}
                            className="text-sm text-primary"
                        >
                            ← Back to Application
                        </Link>
                        <h1 className="mt-2 text-3xl font-bold">
                            Generate Content
                        </h1>
                        <p className="mt-2 text-muted-foreground">
                            Create personalized content for your application to{" "}
                            {application.company} for the {application.position}{" "}
                            position.
                        </p>
                    </div>
                    <Button
                        variant="outline"
                        onClick={() =>
                            router.push(`/dashboard/applications/${params.id}`)
                        }
                        className="h-10"
                    >
                        View Application Details
                    </Button>
                </div>
            </div>

            <Tabs
                defaultValue="cover-letter"
                className="w-full"
                onValueChange={setActiveTab}
            >
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="cover-letter">Cover Letter</TabsTrigger>
                    <TabsTrigger value="linkedin-message">
                        LinkedIn Message
                    </TabsTrigger>
                    <TabsTrigger value="cold-email">Cold Email</TabsTrigger>
                </TabsList>

                <TabsContent value="cover-letter" className="mt-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Cover Letter</CardTitle>
                            <CardDescription>
                                Generate a personalized cover letter for your
                                application.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {displayMode === "generate" &&
                            generating &&
                            activeTab === "cover-letter" ? (
                                <div className="min-h-[400px] bg-muted/10 p-4 rounded-md border">
                                    <TextGenerateEffect
                                        words="Generating your personalized cover letter..."
                                        className="text-muted-foreground"
                                    />
                                </div>
                            ) : displayMode === "generate" &&
                              !generating &&
                              generatedContent.coverLetter ? (
                                <div className="min-h-[400px] p-4 rounded-md border relative">
                                    <TextGenerateEffect
                                        words={generatedContent.coverLetter}
                                        className="text-sm"
                                    />
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        className="absolute top-2 right-2"
                                        onClick={() => setDisplayMode("edit")}
                                    >
                                        Edit
                                    </Button>
                                </div>
                            ) : (
                                <Textarea
                                    value={generatedContent.coverLetter}
                                    onChange={(e) =>
                                        setGeneratedContent({
                                            ...generatedContent,
                                            coverLetter: e.target.value,
                                        })
                                    }
                                    placeholder="Your generated cover letter will appear here..."
                                    className="min-h-[400px]"
                                />
                            )}
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button
                                onClick={() => handleGenerate("cover-letter")}
                                disabled={generating}
                            >
                                {generating && activeTab === "cover-letter" ? (
                                    <>
                                        <LoadingAnimation
                                            variant="ripple"
                                            size="sm"
                                            text="Generating..."
                                            className="mr-2 inline-block"
                                        />
                                        Generating...
                                    </>
                                ) : (
                                    <>
                                        <FileText className="mr-2 h-4 w-4" />
                                        Generate Cover Letter
                                    </>
                                )}
                            </Button>
                            <div className="space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        copyToClipboard(
                                            generatedContent.coverLetter
                                        )
                                    }
                                    disabled={!generatedContent.coverLetter}
                                >
                                    <Copy className="mr-2 h-4 w-4" />
                                    Copy
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        downloadAsDocx(
                                            generatedContent.coverLetter,
                                            `Cover Letter - ${application.company}.docx`
                                        )
                                    }
                                    disabled={!generatedContent.coverLetter}
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    Download
                                </Button>
                            </div>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="linkedin-message" className="mt-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>LinkedIn Message</CardTitle>
                            <CardDescription>
                                Generate a personalized LinkedIn message for
                                networking.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {displayMode === "generate" &&
                            generating &&
                            activeTab === "linkedin-message" ? (
                                <div className="min-h-[400px] bg-muted/10 p-4 rounded-md border">
                                    <TextGenerateEffect
                                        words="Generating your personalized LinkedIn message..."
                                        className="text-muted-foreground"
                                    />
                                </div>
                            ) : displayMode === "generate" &&
                              !generating &&
                              generatedContent.linkedinMessage ? (
                                <div className="min-h-[400px] p-4 rounded-md border relative">
                                    <TextGenerateEffect
                                        words={generatedContent.linkedinMessage}
                                        className="text-sm"
                                    />
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        className="absolute top-2 right-2"
                                        onClick={() => setDisplayMode("edit")}
                                    >
                                        Edit
                                    </Button>
                                </div>
                            ) : (
                                <Textarea
                                    value={generatedContent.linkedinMessage}
                                    onChange={(e) =>
                                        setGeneratedContent({
                                            ...generatedContent,
                                            linkedinMessage: e.target.value,
                                        })
                                    }
                                    placeholder="Your generated LinkedIn message will appear here..."
                                    className="min-h-[400px]"
                                />
                            )}
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button
                                onClick={() =>
                                    handleGenerate("linkedin-message")
                                }
                                disabled={generating}
                            >
                                {generating &&
                                activeTab === "linkedin-message" ? (
                                    <>
                                        <LoadingAnimation
                                            variant="ripple"
                                            size="sm"
                                            text="Generating..."
                                            className="mr-2 inline-block"
                                        />
                                        Generating...
                                    </>
                                ) : (
                                    <>
                                        <MessageSquare className="mr-2 h-4 w-4" />
                                        Generate LinkedIn Message
                                    </>
                                )}
                            </Button>
                            <div className="space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        copyToClipboard(
                                            generatedContent.linkedinMessage
                                        )
                                    }
                                    disabled={!generatedContent.linkedinMessage}
                                >
                                    <Copy className="mr-2 h-4 w-4" />
                                    Copy
                                </Button>
                            </div>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="cold-email" className="mt-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Cold Email</CardTitle>
                            <CardDescription>
                                Generate a cold email for outreach.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {displayMode === "generate" &&
                            generating &&
                            activeTab === "cold-email" ? (
                                <div className="min-h-[400px] bg-muted/10 p-4 rounded-md border">
                                    <TextGenerateEffect
                                        words="Generating your personalized cold email..."
                                        className="text-muted-foreground"
                                    />
                                </div>
                            ) : displayMode === "generate" &&
                              !generating &&
                              generatedContent.coldEmail ? (
                                <div className="min-h-[400px] p-4 rounded-md border relative">
                                    <TextGenerateEffect
                                        words={generatedContent.coldEmail}
                                        className="text-sm"
                                    />
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        className="absolute top-2 right-2"
                                        onClick={() => setDisplayMode("edit")}
                                    >
                                        Edit
                                    </Button>
                                </div>
                            ) : (
                                <Textarea
                                    value={generatedContent.coldEmail}
                                    onChange={(e) =>
                                        setGeneratedContent({
                                            ...generatedContent,
                                            coldEmail: e.target.value,
                                        })
                                    }
                                    placeholder="Your generated cold email will appear here..."
                                    className="min-h-[400px]"
                                />
                            )}
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button
                                onClick={() => handleGenerate("cold-email")}
                                disabled={generating}
                            >
                                {generating && activeTab === "cold-email" ? (
                                    <>
                                        <LoadingAnimation
                                            variant="ripple"
                                            size="sm"
                                            text="Generating..."
                                            className="mr-2 inline-block"
                                        />
                                        Generating...
                                    </>
                                ) : (
                                    <>
                                        <Send className="mr-2 h-4 w-4" />
                                        Generate Cold Email
                                    </>
                                )}
                            </Button>
                            <div className="space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        copyToClipboard(
                                            generatedContent.coldEmail
                                        )
                                    }
                                    disabled={!generatedContent.coldEmail}
                                >
                                    <Copy className="mr-2 h-4 w-4" />
                                    Copy
                                </Button>
                            </div>
                        </CardFooter>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}
