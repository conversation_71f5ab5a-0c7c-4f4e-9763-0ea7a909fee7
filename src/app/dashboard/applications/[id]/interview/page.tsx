"use client";

import { useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { <PERSON>, Sparkles, ArrowRight } from "lucide-react";
import Link from "next/link";

export default function InterviewPrepPage() {
    const params = useParams();

    return (
        <div className="container max-w-6xl space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold">Interview Practice</h1>
                    <p className="text-muted-foreground">
                        Prepare for your upcoming interviews with AI assistance
                    </p>
                </div>
                <Link href={`/dashboard/applications/${params.id}`}>
                    <Button variant="outline">
                        <ArrowRight className="mr-2 h-4 w-4 rotate-180" />
                        Back to Application
                    </Button>
                </Link>
            </div>

            <Card className="border-2 border-dashed border-primary/20">
                <CardHeader className="text-center pb-6">
                    <div className="mx-auto h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                        <Lock className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle className="text-2xl">
                        Interview Practice is a Pro Feature
                    </CardTitle>
                    <CardDescription className="text-base mt-2">
                        Upgrade to HireRizz Pro to unlock AI-powered interview
                        preparation
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 pb-8">
                    <div className="grid gap-4 md:grid-cols-3">
                        <div className="bg-card p-4 rounded-lg border border-border">
                            <div className="flex items-start gap-2 mb-2">
                                <Sparkles className="h-5 w-5 text-primary mt-0.5" />
                                <h3 className="font-medium">
                                    Company-Specific Questions
                                </h3>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                Get tailored interview questions based on the
                                company and role
                            </p>
                        </div>
                        <div className="bg-card p-4 rounded-lg border border-border">
                            <div className="flex items-start gap-2 mb-2">
                                <Sparkles className="h-5 w-5 text-primary mt-0.5" />
                                <h3 className="font-medium">
                                    AI Interview Coach
                                </h3>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                Receive feedback on your answers and suggestions
                                for improvement
                            </p>
                        </div>
                        <div className="bg-card p-4 rounded-lg border border-border">
                            <div className="flex items-start gap-2 mb-2">
                                <Sparkles className="h-5 w-5 text-primary mt-0.5" />
                                <h3 className="font-medium">Mock Interviews</h3>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                Practice with realistic interview scenarios and
                                real-time feedback
                            </p>
                        </div>
                    </div>

                    <div className="flex justify-center pt-4">
                        <Button
                            size="lg"
                            className="px-8 gap-2 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90"
                        >
                            <Sparkles className="h-4 w-4" />
                            Upgrade to Pro
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
