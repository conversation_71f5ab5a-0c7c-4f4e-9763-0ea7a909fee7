// src/app/dashboard/applications/[id]/content/page.tsx
"use client";

import React, { useState, Suspense } from "react";
import { useParams } from "next/navigation";
import { motion } from "framer-motion";
import { useContentManagement } from "@/hooks/use-content-management";
import {
    ContentErrorBoundary,
    ContentErrorFallback,
} from "@/components/content/content-error-boundary";
import { ContentLoadingSkeleton } from "@/components/content/content-loading-skeleton";
import ContentTabs from "@/components/content/content-tabs";
import ApplicationDetailsSidebar from "@/components/content/application-details-sidebar";
import { useResume } from "@/lib/hooks/api-hooks";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    ArrowLeft,
    RefreshCw,
    Building2,
    MapPin,
    FileText,
    MessageSquare,
    Mail,
    HelpCircle,
    CheckCircle2,
    Home,
    ChevronRight,
} from "lucide-react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

function ContentPageContent() {
    const params = useParams();
    const applicationId = params?.id as string;
    const [activeTab, setActiveTab] = useState("coverLetter");

    const {
        combinedData,
        isLoading,
        isGeneratingContent,
        error,
        handleGenerateContent,
        handleContentUpdate,
        handleFieldUpdate,
        handleRefresh,
        copyToClipboard,
        downloadContent,
    } = useContentManagement({ applicationId });

    const { data: resume } = useResume(
        combinedData?.resumeId ? String(combinedData.resumeId) : ""
    );

    if (error) {
        return (
            <div className="min-h-screen bg-background">
                <ContentErrorFallback
                    error={error}
                    applicationId={applicationId}
                />
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="min-h-screen bg-background">
                <ContentLoadingSkeleton />
            </div>
        );
    }

    if (!combinedData) {
        return (
            <div className="min-h-screen bg-background">
                <ContentErrorFallback
                    error={new Error("Application not found")}
                    applicationId={applicationId}
                />
            </div>
        );
    }

    const statusConfig = {
        not_applied: { color: "secondary", label: "Not Applied" },
        applied: { color: "default", label: "Applied" },
        interviewing: { color: "default", label: "Interviewing" },
        offered: { color: "default", label: "Offered" },
        rejected: { color: "destructive", label: "Rejected" },
    };

    const currentStatus =
        statusConfig[combinedData.status as keyof typeof statusConfig] ||
        statusConfig.not_applied;

    const tabs = [
        { id: "coverLetter", label: "Cover Letter", icon: FileText },
        { id: "linkedinMessage", label: "LinkedIn", icon: MessageSquare },
        { id: "coldEmail", label: "Cold Email", icon: Mail },
        { id: "appQuestions", label: "Questions", icon: HelpCircle },
    ];

    const getContentStatus = (tabId: string) => {
        const hasContent = (combinedData as any)?.[tabId]?.length > 0;
        const isGenerating =
            isGeneratingContent[tabId as keyof typeof isGeneratingContent];

        if (isGenerating) return "generating";
        if (hasContent) return "complete";
        return "pending";
    };

    return (
        <div className="min-h-screen bg-background">
            <div className="container max-w-7xl mx-auto px-4 py-6 space-y-6">
                {/* Breadcrumb Navigation */}
                <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Link
                        href="/dashboard"
                        className="flex items-center hover:text-foreground transition-colors"
                    >
                        <Home className="h-4 w-4" />
                        <span className="ml-1">Home</span>
                    </Link>
                    <ChevronRight className="h-4 w-4" />
                    <Link
                        href="/dashboard/applications"
                        className="hover:text-foreground transition-colors"
                    >
                        Applications
                    </Link>
                    <ChevronRight className="h-4 w-4" />
                    <Link
                        href={`/dashboard/applications/${applicationId}`}
                        className="hover:text-foreground transition-colors"
                    >
                        {combinedData.company}
                    </Link>
                    <ChevronRight className="h-4 w-4" />
                    <span className="text-foreground font-medium">
                        Generated Content
                    </span>
                </nav>

                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href="/dashboard/applications">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-2xl font-semibold text-foreground">
                                Generated Content
                            </h1>
                            <p className="text-sm text-muted-foreground">
                                {combinedData.position} at{" "}
                                {combinedData.company}
                            </p>
                        </div>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw
                            className={`h-4 w-4 mr-2 ${
                                isLoading ? "animate-spin" : ""
                            }`}
                        />
                        Refresh
                    </Button>
                </div>

                {/* Application Overview */}
                <Card className="border-border bg-card">
                    <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-muted rounded-lg">
                                    <Building2 className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div>
                                    <h2 className="font-medium text-card-foreground">
                                        {combinedData.company}
                                    </h2>
                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                        <span>{combinedData.position}</span>
                                        {combinedData.location && (
                                            <>
                                                <span>•</span>
                                                <div className="flex items-center gap-1">
                                                    <MapPin className="h-3 w-3" />
                                                    {combinedData.location}
                                                </div>
                                            </>
                                        )}
                                        <span>•</span>
                                        <span>
                                            {formatDistanceToNow(
                                                new Date(
                                                    combinedData.createdAt
                                                ),
                                                { addSuffix: true }
                                            )}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <Badge variant={currentStatus.color as any}>
                                {currentStatus.label}
                            </Badge>
                        </div>
                    </CardContent>
                </Card>

                {/* Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Application Details Sidebar */}
                    <div className="lg:col-span-1">
                        <ApplicationDetailsSidebar
                            application={combinedData}
                            onFieldUpdate={handleFieldUpdate}
                        />
                    </div>

                    {/* Content Tabs */}
                    <div className="lg:col-span-3">
                        <Tabs
                            value={activeTab}
                            onValueChange={setActiveTab}
                            className="space-y-4"
                        >
                            <TabsList className="grid w-full grid-cols-4 bg-muted">
                                {tabs.map((tab) => {
                                    const Icon = tab.icon;
                                    const status = getContentStatus(tab.id);

                                    return (
                                        <TabsTrigger
                                            key={tab.id}
                                            value={tab.id}
                                            className="relative data-[state=active]:bg-background data-[state=active]:text-foreground"
                                        >
                                            <Icon className="h-4 w-4 mr-2" />
                                            {tab.label}
                                            {status === "complete" && (
                                                <CheckCircle2 className="h-3 w-3 ml-2 text-green-600 dark:text-green-400" />
                                            )}
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>

                            <Card className="border-border bg-card">
                                <CardContent className="p-6">
                                    <motion.div
                                        key={activeTab}
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <ContentTabs
                                            activeTab={activeTab}
                                            onTabChange={setActiveTab}
                                            combinedData={combinedData}
                                            isGeneratingContent={
                                                isGeneratingContent
                                            }
                                            onGenerateContent={
                                                handleGenerateContent
                                            }
                                            onContentUpdate={
                                                handleContentUpdate
                                            }
                                            onCopy={copyToClipboard}
                                            onDownload={downloadContent}
                                            resume={resume}
                                        />
                                    </motion.div>
                                </CardContent>
                            </Card>
                        </Tabs>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default function ContentPage() {
    return (
        <ContentErrorBoundary applicationId={useParams()?.id as string}>
            <Suspense
                fallback={
                    <div className="min-h-screen bg-background">
                        <ContentLoadingSkeleton />
                    </div>
                }
            >
                <ContentPageContent />
            </Suspense>
        </ContentErrorBoundary>
    );
}
