"use client";

import { useParams } from "next/navigation";
import <PERSON> from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Loader2,
    CheckCircle,
    XCircle,
    Clock,
    Calendar,
    PhoneCall,
    Award,
    Lock,
    Eye,
    EyeOff,
    ExternalLink,
    ChevronDown,
    ChevronUp,
} from "lucide-react";
import { Breadcrumb } from "@/components/breadcrumb";
import {
    useApplication,
    useUpdateApplicationStatus,
    type ApplicationStatus,
} from "@/lib/hooks/api-hooks";
import { useState } from "react";
import { ResumeSelector } from "@/components/resume-selector";

// Status display configuration
const statusConfig = {
    not_applied: {
        icon: Calendar,
        color: "text-blue-500",
        label: "Not Applied",
    },
    applied: {
        icon: Clock,
        color: "text-yellow-500",
        label: "Applied",
    },
    interviewing: {
        icon: PhoneCall,
        color: "text-purple-500",
        label: "Interviewing",
    },
    offer: {
        icon: Award,
        color: "text-orange-500",
        label: "Offer",
    },
    accepted: {
        icon: CheckCircle,
        color: "text-green-500",
        label: "Accepted",
    },
    rejected: {
        icon: XCircle,
        color: "text-red-500",
        label: "Rejected",
    },
};

export default function ReviewPage() {
    const params = useParams();
    const applicationId = params?.id as string;
    const [showRawJD, setShowRawJD] = useState(false);
    const [expandedSection, setExpandedSection] = useState<string | null>(null);

    const {
        data: application,
        isLoading,
        error,
    } = useApplication(applicationId);
    const updateStatus = useUpdateApplicationStatus();

    // Helper function for formatting values
    const formatValue = (value: any): string => {
        if (Array.isArray(value)) {
            return value.join(", ");
        }
        return value?.toString() || "Not specified";
    };

    // Helper function for rendering links
    const renderLink = (url: string) => {
        if (!url) return null;
        return (
            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 inline-flex items-center gap-1"
            >
                {url} <ExternalLink className="h-3 w-3" />
            </a>
        );
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[60vh]">
                <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                    <p className="text-lg text-muted-foreground">
                        Loading application...
                    </p>
                </div>
            </div>
        );
    }

    if (error || !application) {
        return (
            <div className="text-center">
                <p className="text-lg text-muted-foreground">
                    {error?.message || "Application not found"}
                </p>
                <Link href="/dashboard/applications">
                    <Button className="mt-4">Back to Applications</Button>
                </Link>
            </div>
        );
    }

    const handleStatusUpdate = (status: ApplicationStatus) => {
        updateStatus.mutate({
            id: applicationId,
            status,
            application,
        });
    };

    // Get the status info for display
    const status = application.status || "not_applied";
    const statusInfo = statusConfig[status as keyof typeof statusConfig] || {
        icon: Calendar,
        color: "text-gray-500",
        label: status,
    };
    const StatusIcon = statusInfo.icon;

    const parsedJD = application.parsedJobDescription;

    return (
        <div className="space-y-6">
            <div>
                <Breadcrumb
                    items={[
                        {
                            label: "Applications",
                            href: "/dashboard/applications",
                            active: false,
                        },
                        {
                            label: application.company,
                            href: `/dashboard/applications/${applicationId}`,
                            active: true,
                        },
                    ]}
                />
                <h1 className="text-3xl font-bold tracking-tight">
                    {application.company}
                </h1>
                <p className="mt-2 text-muted-foreground">
                    {application.position}{" "}
                    {application.location && `• ${application.location}`}
                </p>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
                <div className="md:col-span-2 space-y-6">
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Application Details</CardTitle>
                                    <CardDescription>
                                        Review the application information
                                    </CardDescription>
                                </div>
                                {parsedJD && (
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setShowRawJD(!showRawJD)}
                                        className="text-sm"
                                    >
                                        {showRawJD ? (
                                            <>
                                                <EyeOff className="mr-2 h-4 w-4" />
                                                Hide Raw JD
                                            </>
                                        ) : (
                                            <>
                                                <Eye className="mr-2 h-4 w-4" />
                                                Show Raw JD
                                            </>
                                        )}
                                    </Button>
                                )}
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div>
                                <h3 className="font-medium mb-2">Company</h3>
                                <p className="text-2xl font-semibold">
                                    {application.company}
                                </p>
                            </div>
                            <div>
                                <h3 className="font-medium mb-2">Position</h3>
                                <p className="text-xl">
                                    {application.position}
                                </p>
                            </div>
                            {application.location && (
                                <div>
                                    <h3 className="font-medium mb-2">
                                        Location
                                    </h3>
                                    <p>{application.location}</p>
                                </div>
                            )}
                            <div>
                                <h3 className="font-medium mb-2">
                                    Job Description
                                </h3>

                                {/* Parsed JD toggle and display */}
                                {parsedJD && (
                                    <div className="mt-4">
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                                setShowRawJD(!showRawJD)
                                            }
                                            className="text-sm mb-3"
                                        >
                                            {showRawJD ? (
                                                <>
                                                    <EyeOff className="mr-2 h-4 w-4" />
                                                    Hide Parsed Analysis
                                                </>
                                            ) : (
                                                <>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    Show Parsed Analysis
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                )}

                                {!showRawJD && parsedJD && (
                                    <div className="rounded-lg border bg-muted/20 divide-y">
                                        {/* Company & Position Section */}
                                        <div className="p-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <h4 className="font-semibold mb-1">
                                                        Company
                                                    </h4>
                                                    <p>
                                                        {parsedJD.company ||
                                                            "Not specified"}
                                                    </p>
                                                </div>
                                                <div>
                                                    <h4 className="font-semibold mb-1">
                                                        Position
                                                    </h4>
                                                    <p>
                                                        {parsedJD.position ||
                                                            "Not specified"}
                                                    </p>
                                                </div>
                                            </div>
                                            {parsedJD.applicationUrl && (
                                                <div className="mt-2">
                                                    <h4 className="font-semibold mb-1">
                                                        Application URL
                                                    </h4>
                                                    {renderLink(
                                                        parsedJD.applicationUrl
                                                    )}
                                                </div>
                                            )}
                                        </div>

                                        {/* Skills Section */}
                                        <div className="p-4">
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    setExpandedSection(
                                                        expandedSection ===
                                                            "skills"
                                                            ? null
                                                            : "skills"
                                                    )
                                                }
                                                className="flex justify-between items-center w-full text-left"
                                            >
                                                <h4 className="font-semibold">
                                                    Required Skills
                                                </h4>
                                                <span>
                                                    {expandedSection ===
                                                    "skills" ? (
                                                        <ChevronUp className="h-4 w-4" />
                                                    ) : (
                                                        <ChevronDown className="h-4 w-4" />
                                                    )}
                                                </span>
                                            </button>
                                            {expandedSection === "skills" && (
                                                <div className="mt-2 grid grid-cols-2 gap-2">
                                                    {parsedJD.requiredSkills?.map(
                                                        (
                                                            skill: string,
                                                            i: number
                                                        ) => (
                                                            <div
                                                                key={i}
                                                                className="bg-muted/30 px-2 py-1 rounded text-sm"
                                                            >
                                                                {skill}
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            )}
                                        </div>

                                        {/* Experience Section */}
                                        <div className="p-4">
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    setExpandedSection(
                                                        expandedSection ===
                                                            "experience"
                                                            ? null
                                                            : "experience"
                                                    )
                                                }
                                                className="flex justify-between items-center w-full text-left"
                                            >
                                                <h4 className="font-semibold">
                                                    Experience & Qualifications
                                                </h4>
                                                <span>
                                                    {expandedSection ===
                                                    "experience" ? (
                                                        <ChevronUp className="h-4 w-4" />
                                                    ) : (
                                                        <ChevronDown className="h-4 w-4" />
                                                    )}
                                                </span>
                                            </button>
                                            {expandedSection ===
                                                "experience" && (
                                                <div className="mt-2 space-y-2">
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Experience Required:
                                                        </p>
                                                        <p className="text-sm">
                                                            {parsedJD.requiredExperience ||
                                                                "Not specified"}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Key Qualifications:
                                                        </p>
                                                        <ul className="list-disc list-inside text-sm">
                                                            {parsedJD.keyQualifications?.map(
                                                                (
                                                                    qual: string,
                                                                    i: number
                                                                ) => (
                                                                    <li key={i}>
                                                                        {qual}
                                                                    </li>
                                                                )
                                                            )}
                                                        </ul>
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Responsibilities Section */}
                                        <div className="p-4">
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    setExpandedSection(
                                                        expandedSection ===
                                                            "responsibilities"
                                                            ? null
                                                            : "responsibilities"
                                                    )
                                                }
                                                className="flex justify-between items-center w-full text-left"
                                            >
                                                <h4 className="font-semibold">
                                                    Job Responsibilities
                                                </h4>
                                                <span>
                                                    {expandedSection ===
                                                    "responsibilities" ? (
                                                        <ChevronUp className="h-4 w-4" />
                                                    ) : (
                                                        <ChevronDown className="h-4 w-4" />
                                                    )}
                                                </span>
                                            </button>
                                            {expandedSection ===
                                                "responsibilities" && (
                                                <ul className="mt-2 list-disc list-inside text-sm">
                                                    {parsedJD.jobResponsibilities?.map(
                                                        (
                                                            resp: string,
                                                            i: number
                                                        ) => (
                                                            <li key={i}>
                                                                {resp}
                                                            </li>
                                                        )
                                                    )}
                                                </ul>
                                            )}
                                        </div>

                                        {/* Additional Info Section */}
                                        <div className="p-4">
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    setExpandedSection(
                                                        expandedSection ===
                                                            "additional"
                                                            ? null
                                                            : "additional"
                                                    )
                                                }
                                                className="flex justify-between items-center w-full text-left"
                                            >
                                                <h4 className="font-semibold">
                                                    Additional Information
                                                </h4>
                                                <span>
                                                    {expandedSection ===
                                                    "additional" ? (
                                                        <ChevronUp className="h-4 w-4" />
                                                    ) : (
                                                        <ChevronDown className="h-4 w-4" />
                                                    )}
                                                </span>
                                            </button>
                                            {expandedSection ===
                                                "additional" && (
                                                <div className="mt-2 space-y-3">
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Salary Range:
                                                        </p>
                                                        <p className="text-sm">
                                                            {parsedJD.estimatedSalaryRange ||
                                                                "Not specified"}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Company Values:
                                                        </p>
                                                        <p className="text-sm">
                                                            {formatValue(
                                                                parsedJD.companyValues
                                                            )}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-sm">
                                                            Potential
                                                            Challenges:
                                                        </p>
                                                        <ul className="list-disc list-inside text-sm">
                                                            {parsedJD.potentialChallenges?.map(
                                                                (
                                                                    challenge: string,
                                                                    i: number
                                                                ) => (
                                                                    <li key={i}>
                                                                        {
                                                                            challenge
                                                                        }
                                                                    </li>
                                                                )
                                                            )}
                                                        </ul>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div>
                                <h3 className="font-medium mb-2">Notes</h3>
                                <p className="text-muted-foreground">
                                    {application.notes || "No notes added"}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Application Status</CardTitle>
                            <CardDescription>
                                Update the current status
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between p-4 rounded-lg bg-muted">
                                <div className="flex items-center space-x-2">
                                    <StatusIcon
                                        className={`h-5 w-5 ${statusInfo.color}`}
                                    />
                                    <span>{statusInfo.label}</span>
                                </div>
                                <span className="text-sm text-muted-foreground">
                                    {new Date(
                                        application.createdAt
                                    ).toLocaleDateString()}
                                </span>
                            </div>

                            <div className="space-y-2">
                                <Button
                                    variant="outline"
                                    className="w-full border-yellow-500 hover:bg-yellow-500/10"
                                    onClick={() =>
                                        handleStatusUpdate("applied")
                                    }
                                    disabled={
                                        updateStatus.isPending ||
                                        application.status === "applied"
                                    }
                                >
                                    <Clock className="mr-2 h-4 w-4" />
                                    Mark as Applied
                                </Button>
                                <Button
                                    variant="outline"
                                    className="w-full border-purple-500 hover:bg-purple-500/10"
                                    onClick={() =>
                                        handleStatusUpdate("interviewing")
                                    }
                                    disabled={
                                        updateStatus.isPending ||
                                        application.status === "interviewing"
                                    }
                                >
                                    <PhoneCall className="mr-2 h-4 w-4" />
                                    Mark as Interviewing
                                </Button>
                                <Button
                                    variant="outline"
                                    className="w-full border-orange-500 hover:bg-orange-500/10"
                                    onClick={() => handleStatusUpdate("offer")}
                                    disabled={
                                        updateStatus.isPending ||
                                        application.status === "offer"
                                    }
                                >
                                    <Award className="mr-2 h-4 w-4" />
                                    Mark as Offer Received
                                </Button>
                                <Button
                                    variant="outline"
                                    className="w-full border-green-500 hover:bg-green-500/10"
                                    onClick={() =>
                                        handleStatusUpdate("accepted")
                                    }
                                    disabled={
                                        updateStatus.isPending ||
                                        application.status === "accepted"
                                    }
                                >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Mark as Accepted
                                </Button>
                                <Button
                                    variant="outline"
                                    className="w-full border-red-500 hover:bg-red-500/10"
                                    onClick={() =>
                                        handleStatusUpdate("rejected")
                                    }
                                    disabled={
                                        updateStatus.isPending ||
                                        application.status === "rejected"
                                    }
                                >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Mark as Rejected
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Generated Content</CardTitle>
                            <CardDescription>
                                View AI-generated content for this application
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button
                                className="w-full"
                                variant="outline"
                                asChild
                            >
                                <Link
                                    href={`/dashboard/applications/${applicationId}/content`}
                                >
                                    View Cover Letter & Messages
                                </Link>
                            </Button>
                            <div className="relative">
                                <Button
                                    className="w-full bg-muted/50 text-muted-foreground border-dashed"
                                    variant="outline"
                                    disabled
                                >
                                    <Lock className="mr-2 h-4 w-4" />
                                    Practice Interview
                                    <span className="absolute -top-2 -right-2 bg-secondary text-secondary-foreground text-xs px-1.5 py-0.5 rounded-full">
                                        PRO
                                    </span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    <ResumeSelector
                        applicationId={applicationId}
                        resumeId={application.resumeId}
                    />
                </div>
            </div>
        </div>
    );
}
