"use client";

import dynamic from "next/dynamic";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";

// Dynamically import our client component with no SSR
const ApplicationsClient = dynamic(
    () => import("./applications-client").then((mod) => mod.ApplicationsClient),
    {
        ssr: false,
        loading: () => <ApplicationsListSkeleton />,
    }
);

export function ApplicationsList() {
    return <ApplicationsClient />;
}

function ApplicationsListSkeleton() {
    return (
        <div className="space-y-4">
            <LoadingSkeleton variant="list" size="lg" className="w-full" />
        </div>
    );
}
