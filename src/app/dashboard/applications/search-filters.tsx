"use client";

import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { useCallback } from "react";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { VALID_STATUSES } from "@/lib/hooks/api-hooks";

const statusLabels: Record<string, string> = {
    not_applied: "Not Applied",
    applied: "Applied",
    interviewing: "Interviewing",
    offer: "Offer",
    rejected: "Rejected",
    accepted: "Accepted",
};

export function SearchFilters() {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Get current status filter from URL with safe handling
    const currentStatus = searchParams ? searchParams.get("status") : null;

    // Create a new URLSearchParams instance with error handling
    const createQueryString = useCallback(
        (name: string, value: string | null) => {
            try {
                if (!searchParams) return "";

                const params = new URLSearchParams(searchParams.toString());

                if (value === null) {
                    params.delete(name);
                } else {
                    params.set(name, value);
                }

                return params.toString();
            } catch (e) {
                console.error("Error creating query string", e);
                return "";
            }
        },
        [searchParams]
    );

    // Handle status filter click with error handling
    const handleStatusClick = (status: string) => {
        try {
            router.push(
                `${pathname}?${createQueryString("status", status || null)}`
            );
        } catch (e) {
            console.error("Error updating status", e);
        }
    };

    // Clear filters with error handling
    const clearFilters = () => {
        try {
            router.push(pathname);
        } catch (e) {
            console.error("Error clearing filters", e);
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center overflow-x-auto pb-2">
                <div className="flex gap-2">
                    <Badge
                        variant={!currentStatus ? "default" : "outline"}
                        className={`cursor-pointer py-1.5 px-4 rounded-full text-sm font-medium transition-all ${
                            !currentStatus
                                ? "bg-primary text-primary-foreground shadow-sm shadow-primary/25"
                                : "border-border/40 hover:bg-accent/30"
                        }`}
                        onClick={() => handleStatusClick("")}
                    >
                        All
                    </Badge>

                    {VALID_STATUSES.map((status) => (
                        <Badge
                            key={status}
                            variant={
                                currentStatus === status ? "default" : "outline"
                            }
                            className={`cursor-pointer py-1.5 px-4 rounded-full text-sm font-medium transition-all ${
                                currentStatus === status
                                    ? "bg-primary text-primary-foreground shadow-sm shadow-primary/25"
                                    : "border-border/40 hover:bg-accent/30"
                            }`}
                            onClick={() => handleStatusClick(status)}
                        >
                            {statusLabels[status] || status}
                        </Badge>
                    ))}
                </div>

                {currentStatus && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearFilters}
                        className="text-muted-foreground hover:text-foreground"
                    >
                        <X className="h-4 w-4 mr-1" />
                        Clear Filter
                    </Button>
                )}
            </div>
        </div>
    );
}
