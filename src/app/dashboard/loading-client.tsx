"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import PreloaderParticles from "@/components/preloader-particles";

export default function DashboardLoadingClient() {
    const [progress, setProgress] = useState(60); // Start at 60% for dashboard

    useEffect(() => {
        let currentProgress = 60;

        // Handle progress animation
        const interval = setInterval(() => {
            if (currentProgress < 100) {
                currentProgress += 5;
                if (currentProgress > 100) currentProgress = 100;
                setProgress(currentProgress);

                if (currentProgress >= 100) {
                    clearInterval(interval);
                }
            }
        }, 100);

        return () => clearInterval(interval);
    }, []);

    return (
        <motion.div
            className="fixed inset-0 z-[999] bg-black flex flex-col items-center justify-center overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0, transition: { duration: 0.3 } }}
        >
            {/* Interactive Particles Background */}
            <PreloaderParticles isActive={true} />

            {/* Animated Brand Logo */}
            <motion.div
                className="absolute top-8 left-8 text-3xl font-bold tracking-tighter"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, ease: "easeOut" }}
            >
                <span className="text-gradient-purple-blue">Hire</span>
                <span className="text-gradient-pink-cyan">Rizz</span>
            </motion.div>

            {/* Animated Loading Indicator */}
            <div className="relative w-64 h-64 flex items-center justify-center">
                <motion.div
                    className="absolute w-full h-full border-[1px] border-white/10 rounded-full"
                    initial={{ rotate: 0 }}
                    animate={{ rotate: 360 }}
                    transition={{
                        duration: 8,
                        ease: "linear",
                        repeat: Infinity,
                    }}
                />

                <motion.div
                    className="absolute w-full h-full rounded-full flex items-center justify-center"
                    style={{
                        background: `conic-gradient(from 0deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) ${progress}%, transparent ${progress}%, transparent 100%)`,
                    }}
                    initial={{ opacity: 0.4, scale: 0.9 }}
                    animate={{
                        opacity: [0.4, 0.8, 0.4],
                        scale: [0.9, 0.92, 0.9],
                    }}
                    transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                    }}
                >
                    <div className="w-[98%] h-[98%] rounded-full bg-black" />
                </motion.div>

                {/* Progress glow effect */}
                <motion.div
                    className="absolute w-full h-full rounded-full"
                    style={{
                        boxShadow: `0 0 30px ${
                            progress / 3
                        }px hsla(var(--primary), ${progress / 200})`,
                        background: "transparent",
                    }}
                />

                <motion.div
                    className="relative text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary z-10"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                >
                    {Math.floor(progress)}%
                </motion.div>
            </div>

            {/* Animated Loading Message */}
            <motion.div
                className="mt-16 overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.4 }}
            >
                <div className="flex flex-col items-center text-center">
                    <motion.p
                        className="text-xl font-light text-white/80 uppercase tracking-widest"
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                        }}
                    >
                        Preparing Dashboard
                    </motion.p>
                    <div className="mt-3 flex space-x-2">
                        {[0, 1, 2].map((i) => (
                            <motion.div
                                key={i}
                                className="w-2 h-2 bg-secondary rounded-full"
                                animate={{
                                    y: [0, -8, 0],
                                    opacity: [0.5, 1, 0.5],
                                }}
                                transition={{
                                    duration: 0.6,
                                    repeat: Infinity,
                                    delay: i * 0.15,
                                    ease: "easeInOut",
                                }}
                            />
                        ))}
                    </div>
                </div>
            </motion.div>

            {/* Animated Background Lines */}
            <div
                className="absolute inset-0 flex justify-between z-[-1]"
                style={{ perspective: "1000px" }}
            >
                {Array.from({ length: 10 }).map((_, i) => (
                    <motion.div
                        key={i}
                        className="h-full w-[1px] bg-gradient-to-b from-transparent via-primary/20 to-transparent"
                        initial={{
                            opacity: 0,
                            rotateX: 90,
                            translateZ: -100,
                        }}
                        animate={{
                            opacity: [0, 0.4, 0],
                            rotateX: [90, 0, 90],
                            translateZ: [-100, 0, -100],
                        }}
                        transition={{
                            duration: 2 + i / 3,
                            repeat: Infinity,
                            delay: i * 0.2,
                            ease: "easeInOut",
                        }}
                    />
                ))}
            </div>
        </motion.div>
    );
}
