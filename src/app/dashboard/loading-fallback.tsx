export default function LoadingFallback() {
    return (
        <div className="fixed inset-0 z-[999] bg-black flex flex-col items-center justify-center">
            {/* Static Brand Logo */}
            <div className="absolute top-8 left-8 text-3xl font-bold tracking-tighter">
                <span className="text-gradient-purple-blue">Hire</span>
                <span className="text-gradient-pink-cyan">Rizz</span>
            </div>

            {/* Static Loading Indicator */}
            <div className="relative w-64 h-64 flex items-center justify-center">
                <div
                    className="absolute w-full h-full border-[1px] border-white/10 rounded-full animate-spin"
                    style={{ animationDuration: "8s" }}
                />
                <div
                    className="absolute w-full h-full rounded-full flex items-center justify-center"
                    style={{
                        background:
                            "conic-gradient(from 0deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 60%, transparent 60%, transparent 100%)",
                    }}
                >
                    <div className="w-[98%] h-[98%] rounded-full bg-black" />
                </div>
                <div className="text-3xl font-bold text-white">60%</div>
            </div>

            {/* Static Loading Message */}
            <div className="mt-16">
                <div className="flex flex-col items-center text-center">
                    <p className="text-xl font-light text-white/80 uppercase tracking-widest">
                        Preparing Dashboard
                    </p>
                    <div className="mt-3 flex space-x-2">
                        {[0, 1, 2].map((i) => (
                            <div
                                key={i}
                                className="w-2 h-2 bg-secondary rounded-full"
                            />
                        ))}
                    </div>
                </div>
            </div>

            {/* Static Background Lines */}
            <div className="absolute inset-0 flex justify-between z-[-1]">
                {Array.from({ length: 10 }).map((_, i) => (
                    <div
                        key={i}
                        className="h-full w-[1px] bg-gradient-to-b from-transparent via-primary/20 to-transparent opacity-30"
                    />
                ))}
            </div>
        </div>
    );
}
