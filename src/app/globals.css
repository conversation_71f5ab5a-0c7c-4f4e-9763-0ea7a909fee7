@import "tailwindcss";
@import "uploadthing/tw/v4";
@source "../../node_modules/@uploadthing/react/dist/**";

@custom-variant dark (&:is(.dark *));

@theme {
    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));
    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));

    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));

    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));

    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));

    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));

    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));

    --color-popover: hsl(var(--popover));
    --color-popover-foreground: hsl(var(--popover-foreground));

    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));

    --color-chart-1: hsl(var(--chart-1));
    --color-chart-2: hsl(var(--chart-2));
    --color-chart-3: hsl(var(--chart-3));
    --color-chart-4: hsl(var(--chart-4));
    --color-chart-5: hsl(var(--chart-5));

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --animate-accordion-down: accordion-down 0.2s ease-out;
    --animate-accordion-up: accordion-up 0.2s ease-out;

    @keyframes accordion-down {
        from {
            height: 0;
        }
        to {
            height: var(--radix-accordion-content-height);
        }
    }
    @keyframes accordion-up {
        from {
            height: var(--radix-accordion-content-height);
        }
        to {
            height: 0;
        }
    }
}

@utility container {
    margin-inline: auto;
    padding-inline: 2rem;
    @media (width >= --theme(--breakpoint-sm)) {
        max-width: none;
    }
    @media (width >= 1400px) {
        max-width: 1400px;
    }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@layer base {
    :root {
        /* Vibrant modern palette - Light mode */
        --background: 0 0% 100%; /* Pure white: #ffffff */
        --foreground: 240 10% 3.9%; /* Almost black: #09090b */

        --card: 0 0% 100%; /* White: #ffffff */
        --card-foreground: 240 10% 3.9%; /* Almost black: #09090b */

        --popover: 0 0% 100%; /* White: #ffffff */
        --popover-foreground: 240 10% 3.9%; /* Almost black: #09090b */

        /* Vibrant purple as primary */
        --primary: 267 75% 53%; /* Vibrant purple: #7c3aed */
        --primary-foreground: 0 0% 100%; /* White: #ffffff */

        /* Bright magenta as secondary */
        --secondary: 330 80% 60%; /* Bright magenta: #e11d74 */
        --secondary-foreground: 0 0% 100%; /* White: #ffffff */

        --muted: 260 20% 96%; /* Light purple-gray: #f5f4fa */
        --muted-foreground: 240 3.8% 46.1%; /* Medium gray: #757575 */

        /* Bright blue as accent */
        --accent: 217 91% 60%; /* Bright blue: #3b82f6 */
        --accent-foreground: 0 0% 100%; /* White: #ffffff */

        /* Bright red as destructive */
        --destructive: 0 84% 60%; /* Bright red: #ef4444 */
        --destructive-foreground: 0 0% 100%; /* White: #ffffff */

        --border: 240 5.9% 90%; /* Light gray: #e5e5e5 */
        --input: 240 5.9% 90%; /* Light gray: #e5e5e5 */
        --ring: 267 75% 53%; /* Vibrant purple: #7c3aed */

        --radius: 0.75rem; /* Slightly larger radius for modern look */

        /* Chart colors using the new vibrant palette */
        --chart-1: 267 75% 53%; /* Vibrant purple: #7c3aed */
        --chart-2: 330 80% 60%; /* Bright magenta: #e11d74 */
        --chart-3: 217 91% 60%; /* Bright blue: #3b82f6 */
        --chart-4: 47 95% 55%; /* Bright yellow: #eab308 */
        --chart-5: 142 71% 45%; /* Bright green: #10b981 */

        /* Custom theme colors using the new palette */
        --pink: 330 80% 60%; /* Bright magenta: #e11d74 */
        --cyan: 190 95% 50%; /* Bright cyan: #06b6d4 */
        --purple: 267 75% 53%; /* Vibrant purple: #7c3aed */
        --coral: 12 89% 59%; /* Bright coral: #f43f5e */
        --amber: 45 93% 58%; /* Bright amber: #f59e0b */
        --grid-color: 260 20% 92%; /* Light purple-gray for grid: #e9e8f0 */
    }

    .dark {
        /* Vibrant modern palette - Dark mode */
        --background: 240 10% 3.9%; /* Almost black: #09090b */
        --foreground: 0 0% 100%; /* White: #ffffff */

        --card: 240 10% 7%; /* Dark gray: #111113 */
        --card-foreground: 0 0% 100%; /* White: #ffffff */

        --popover: 240 10% 7%; /* Dark gray: #111113 */
        --popover-foreground: 0 0% 100%; /* White: #ffffff */

        /* Lighter purple as primary in dark mode */
        --primary: 267 75% 65%; /* Lighter purple: #a78bfa */
        --primary-foreground: 240 10% 3.9%; /* Almost black: #09090b */

        /* Lighter magenta as secondary */
        --secondary: 330 80% 70%; /* Lighter magenta: #f472b6 */
        --secondary-foreground: 240 10% 3.9%; /* Almost black: #09090b */

        --muted: 240 10% 12%; /* Dark gray for muted areas: #1e1e24 */
        --muted-foreground: 240 5% 64.9%; /* Light gray: #a1a1aa */

        /* Lighter blue as accent */
        --accent: 217 91% 70%; /* Lighter blue: #60a5fa */
        --accent-foreground: 240 10% 3.9%; /* Almost black: #09090b */

        /* Lighter red as destructive */
        --destructive: 0 84% 70%; /* Lighter red: #f87171 */
        --destructive-foreground: 240 10% 3.9%; /* Almost black: #09090b */

        --border: 240 3.7% 15.9%; /* Dark gray for borders: #27272a */
        --input: 240 3.7% 15.9%; /* Dark gray for inputs: #27272a */
        --ring: 267 75% 65%; /* Lighter purple: #a78bfa */

        /* Chart colors using the new vibrant palette for dark mode */
        --chart-1: 267 75% 65%; /* Lighter purple: #a78bfa */
        --chart-2: 330 80% 70%; /* Lighter magenta: #f472b6 */
        --chart-3: 217 91% 70%; /* Lighter blue: #60a5fa */
        --chart-4: 47 95% 65%; /* Lighter yellow: #facc15 */
        --chart-5: 142 71% 55%; /* Lighter green: #34d399 */

        /* Custom theme colors using the new palette */
        --pink: 330 80% 70%; /* Lighter magenta: #f472b6 */
        --cyan: 190 95% 60%; /* Lighter cyan: #22d3ee */
        --purple: 267 75% 65%; /* Lighter purple: #a78bfa */
        --coral: 12 89% 69%; /* Lighter coral: #fb7185 */
        --amber: 45 93% 68%; /* Lighter amber: #fbbf24 */
        --grid-color: 240 3.7% 25%; /* Dark gray grid color: #3f3f46 */
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

/* Cyberpunk specific animations - optimized for performance */
@keyframes float {
    0%,
    100% {
        transform: translateY(0);
        will-change: transform;
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes float-delayed {
    0%,
    100% {
        transform: translateY(0);
        will-change: transform;
    }
    50% {
        transform: translateY(-15px);
    }
}

@keyframes ping-slow {
    0% {
        transform: scale(1);
        opacity: 0.3;
        will-change: transform, opacity;
    }
    50% {
        opacity: 0.1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

@keyframes glow {
    0%,
    100% {
        opacity: 0.5;
        filter: blur(10px);
        will-change: opacity, filter;
    }
    50% {
        opacity: 0.8;
        filter: blur(15px);
    }
}

@keyframes pulse-border {
    0%,
    100% {
        border-color: hsla(var(--primary), 0.3);
        will-change: border-color;
    }
    50% {
        border-color: hsla(var(--primary), 0.8);
    }
}

@keyframes scanner {
    0% {
        transform: translateY(-100%);
        opacity: 0;
        will-change: transform, opacity;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        transform: translateY(100%);
        opacity: 0;
    }
}

@keyframes glitch {
    0% {
        clip-path: inset(40% 0 61% 0);
        transform: translate(-2px, 2px);
        will-change: clip-path, transform;
    }
    20% {
        clip-path: inset(92% 0 1% 0);
        transform: translate(1px, 1px);
    }
    40% {
        clip-path: inset(43% 0 1% 0);
        transform: translate(-1px, -3px);
    }
    60% {
        clip-path: inset(25% 0 58% 0);
        transform: translate(3px, 2px);
    }
    80% {
        clip-path: inset(54% 0 7% 0);
        transform: translate(-2px, -2px);
    }
    100% {
        clip-path: inset(58% 0 43% 0);
        transform: translate(2px, -1px);
    }
}

@keyframes loading-pulse {
    0%,
    100% {
        opacity: 0.3;
        transform: translateY(0);
        will-change: transform, opacity;
    }
    50% {
        opacity: 1;
        transform: translateY(-2px);
    }
}

/* Text reveal animation */
.reveal-text {
    animation: reveal-text 0.8s ease-out forwards;
    clip-path: inset(0 100% 0 0);
}

@keyframes reveal-text {
    0% {
        clip-path: inset(0 100% 0 0);
    }
    100% {
        clip-path: inset(0 0 0 0);
    }
}

/* Reveal effect for elements */
.reveal-element {
    position: relative;
    display: inline-block;
}

.reveal-element::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: hsl(267, 75%, 53%); /* Vibrant purple */
    transform-origin: right;
    animation: reveal 1.5s ease forwards;
}

@keyframes reveal {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

/* Scanner line element optimized for performance */
.scanner-line {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
        90deg,
        transparent 0%,
        hsl(var(--secondary)) 20%,
        hsl(var(--secondary)) 50%,
        hsl(var(--secondary)) 80%,
        transparent 100%
    );
    box-shadow: 0 0 12px hsl(var(--secondary));
}

/* Text gradient classes with vibrant color palette */
.text-gradient-pink-cyan {
    @apply bg-gradient-to-r from-[hsl(330,80%,60%)] to-[hsl(190,95%,50%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

.text-gradient-cyan-purple {
    @apply bg-gradient-to-r from-[hsl(190,95%,50%)] to-[hsl(267,75%,53%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

.text-gradient-pink-purple {
    @apply bg-gradient-to-r from-[hsl(330,80%,60%)] to-[hsl(267,75%,53%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

.text-gradient-coral-amber {
    @apply bg-gradient-to-r from-[hsl(12,89%,59%)] to-[hsl(45,93%,58%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

/* Add more vibrant gradients */
.text-gradient-purple-blue {
    @apply bg-gradient-to-r from-[hsl(267,75%,53%)] to-[hsl(217,91%,60%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

.text-gradient-green-blue {
    @apply bg-gradient-to-r from-[hsl(142,71%,45%)] to-[hsl(217,91%,60%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

.text-gradient-yellow-red {
    @apply bg-gradient-to-r from-[hsl(47,95%,55%)] to-[hsl(0,84%,60%)] bg-clip-text text-transparent;
    transform: translateZ(0);
}

.bg-grid {
    background-image: url("/grid.svg");
    background-repeat: repeat;
    @apply opacity-20;
    transform: translateZ(0);
}

.border-glow {
    box-shadow: 0 0 5px hsl(var(--primary)), 0 0 20px hsla(var(--primary), 0.3);
}

.cyberpunk-card {
    @apply border border-[hsl(var(--border))] bg-[hsl(var(--card))] 
           hover:border-[hsl(var(--primary)/0.5)] transition-all;
}

/* Custom cursor styles */
.cursor-container {
    pointer-events: none;
    transform: translateZ(0);
    will-change: transform;
}

.custom-cursor {
    cursor: none;
}

.custom-cursor a,
.custom-cursor button,
.custom-cursor [role="button"],
.custom-cursor input,
.custom-cursor label,
.custom-cursor select,
.custom-cursor textarea {
    cursor: none;
}

/* Optimize loading indicators */
.loading-indicator {
    transform: translateZ(0);
    will-change: transform, opacity;
}

/* Page transition optimizations */
.page-transition-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9998;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(30, 41, 59, 0.8); /* Dark blue-gray background */
    backdrop-filter: blur(5px);
    transform: translateZ(0);
}

/* Optimize image rendering */
.optimize-image {
    image-rendering: -webkit-optimize-contrast;
}

/* Hardware acceleration for critical elements */
.hw-accelerate {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
}

.loading-text {
    font-size: 1.5rem;
    letter-spacing: 0.2em;
    font-weight: bold;
    text-transform: uppercase;
}

.loading-dots {
    display: flex;
    gap: 0.5rem;
    margin-left: 0.5rem;
}

.loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* Performance-optimized animations by using composited properties */
.animate-float {
    animation: float 6s ease-in-out infinite;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.animate-float-delayed {
    animation: float-delayed 7s ease-in-out 2s infinite;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.animate-ping-slow {
    animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.animate-glow {
    animation: glow 4s ease-in-out infinite;
    transform: translateZ(0);
}

.animate-pulse-border {
    animation: pulse-border 2s ease-in-out infinite;
}

.animate-scanner {
    animation: scanner 1.5s ease-in-out infinite;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.animate-glitch {
    animation: glitch 500ms infinite linear alternate-reverse;
    transform: translateZ(0);
}

.animate-loading-pulse {
    animation: loading-pulse 1.5s infinite ease-in-out;
    transform: translateZ(0);
}
