"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { Label } from "@/components/ui/label";
import { z } from "zod";

// Client-side validation schema
const formSchema = z.object({
    jobUrl: z
        .string()
        .trim()
        .url("Please enter a valid URL with http:// or https://")
        .max(2048, "URL is too long"),
});

export default function AnalyzeJobTest() {
    const [jobUrl, setJobUrl] = useState("");
    const [result, setResult] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [validationError, setValidationError] = useState<string | null>(null);

    // Handle URL input change with validation
    const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setJobUrl(value);
        setValidationError(null);

        if (value.trim()) {
            try {
                formSchema.parse({ jobUrl: value });
            } catch (err) {
                if (err instanceof z.ZodError) {
                    setValidationError(err.errors[0].message);
                }
            }
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            formSchema.parse({ jobUrl });
        } catch (err) {
            if (err instanceof z.ZodError) {
                setValidationError(err.errors[0].message);
                return;
            }
        }

        setLoading(true);
        setError(null);
        setResult(null);

        try {
            const response = await fetch("/api/ai/analyze-job-link", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ jobUrl: jobUrl.trim() }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(
                    data.message || data.error || "Failed to analyze job link"
                );
            }

            setResult(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : "An error occurred");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="container mx-auto py-10 max-w-2xl">
            <Card>
                <CardHeader>
                    <CardTitle>Job Link Analysis Test</CardTitle>
                    <CardDescription>
                        Enter a job URL to analyze the posting
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="jobUrl">Job URL</Label>
                            <Input
                                id="jobUrl"
                                type="url"
                                placeholder="https://example.com/jobs/software-engineer"
                                value={jobUrl}
                                onChange={handleUrlChange}
                                required
                                className={
                                    validationError ? "border-red-500" : ""
                                }
                            />
                            {validationError && (
                                <p className="text-sm text-red-500 mt-1">
                                    {validationError}
                                </p>
                            )}
                        </div>

                        <Button
                            type="submit"
                            disabled={loading || !!validationError}
                            className="mt-4"
                        >
                            {loading ? "Analyzing..." : "Analyze Job"}
                        </Button>

                        {error && (
                            <Alert variant="destructive">
                                <ExclamationTriangleIcon className="h-4 w-4" />
                                <AlertTitle>Error</AlertTitle>
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}

                        {result && (
                            <div className="mt-4">
                                <h3 className="font-medium mb-2">
                                    Analysis Results:
                                </h3>
                                <pre className="bg-slate-100 p-4 rounded-lg overflow-auto">
                                    {JSON.stringify(result, null, 2)}
                                </pre>
                            </div>
                        )}
                    </form>
                </CardContent>
            </Card>
        </div>
    );
}
