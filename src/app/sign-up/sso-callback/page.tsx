"use client";

import { AuthenticateWithRedirectCallback } from "@clerk/nextjs";
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";

export default function SignUpSSOCallbackPage() {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    return (
        <div className="flex min-h-screen items-center justify-center">
            {isMounted ? (
                <AuthenticateWithRedirectCallback />
            ) : (
                <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="mt-4 text-muted-foreground">
                        Authenticating...
                    </p>
                </div>
            )}
        </div>
    );
}
