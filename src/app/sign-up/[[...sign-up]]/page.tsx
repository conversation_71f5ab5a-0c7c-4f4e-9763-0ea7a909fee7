"use client";

import { SignUp, useAuth } from "@clerk/nextjs";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { CyberpunkBackground } from "@/components/ui/cyberpunk-background";
import { CyberpunkText } from "@/components/ui/cyberpunk-text";
import { useEffect, useState } from "react";

export default function SignUpPage() {
    // Add client-side only rendering flag
    const [isMounted, setIsMounted] = useState(false);
    // Get the redirect URL from query parameters
    const searchParams = useSearchParams();
    const redirectUrl = searchParams?.get("redirect_url") || "/dashboard";
    const router = useRouter();
    const { isLoaded, userId } = useAuth();

    // Set mounted flag on client-side
    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Force redirect to dashboard if already signed in
    useEffect(() => {
        // Only run on client-side
        if (!isMounted) return;

        // Check for already authenticated via Clerk
        if (isLoaded && userId) {
            console.log("User already authenticated, redirecting to dashboard");
            router.replace("/dashboard");
            return;
        }

        // Check for redirect loop
        const visitCount = parseInt(
            sessionStorage.getItem("signUpVisits") || "0"
        );
        sessionStorage.setItem("signUpVisits", (visitCount + 1).toString());

        // If visited sign-up page more than 3 times in this session,
        // force redirect to dashboard to break potential loop
        if (visitCount > 2) {
            console.log(
                "Detected potential redirect loop, forcing navigation to dashboard"
            );
            sessionStorage.removeItem("signUpVisits");
            router.replace("/dashboard");
        }
    }, [isLoaded, userId, router, isMounted]);

    return (
        <div className="flex min-h-screen flex-col items-center justify-center bg-background text-foreground relative">
            <CyberpunkBackground />

            <div className="mb-8 relative z-10">
                <Link href="/" className="flex items-center gap-2">
                    <CyberpunkText
                        as="span"
                        gradient="accent"
                        className="text-3xl"
                    >
                        <span className="text-[#5e4b8b]">Hire</span>
                        <span className="text-[#ff6e61]">Rizz</span>
                    </CyberpunkText>
                    <div className="absolute -top-1 -right-3 w-2 h-2 bg-[#5e4b8b] rounded-full animate-ping" />
                </Link>
            </div>

            <div className="relative z-10">
                {/* Only render SignUp on client-side */}
                {isMounted ? (
                    <SignUp
                        appearance={{
                            elements: {
                                rootBox: "mx-auto",
                                card: "shadow-lg rounded-lg border border-border bg-card",
                                formButtonPrimary:
                                    "bg-[#5e4b8b] hover:bg-[#5e4b8b]/90",
                                formFieldInput: "bg-background border-border",
                                formFieldLabel: "text-foreground",
                                footerActionText: "text-muted-foreground",
                                footerActionLink: "text-[#6d9dc5]",
                                identityPreviewText: "text-foreground",
                                identityPreviewEditButtonText: "text-[#6d9dc5]",
                            },
                        }}
                        redirectUrl={redirectUrl}
                        afterSignUpUrl={redirectUrl}
                        path="/sign-up"
                        routing="path"
                        signInUrl="/sign-in"
                    />
                ) : (
                    <div className="shadow-lg rounded-lg border border-border bg-card p-8 w-[400px] h-[480px] flex items-center justify-center">
                        <div className="animate-pulse flex flex-col items-center gap-4">
                            <div className="h-8 bg-muted rounded-md w-3/4"></div>
                            <div className="h-10 bg-muted rounded-md w-full"></div>
                            <div className="h-10 bg-muted rounded-md w-full"></div>
                            <div className="h-10 bg-muted rounded-md w-full"></div>
                            <div className="h-10 bg-muted rounded-md w-1/2 mt-2"></div>
                        </div>
                    </div>
                )}
            </div>

            <div className="absolute bottom-1/3 right-[15%] w-20 h-20 rounded-full bg-gradient-to-br from-[#da1b61]/30 to-[#5e4b8b]/30 blur-xl"></div>
            <div className="absolute top-1/4 left-[15%] w-16 h-16 rounded-full bg-gradient-to-br from-[#6d9dc5]/30 to-[#5e4b8b]/30 blur-xl"></div>
        </div>
    );
}
