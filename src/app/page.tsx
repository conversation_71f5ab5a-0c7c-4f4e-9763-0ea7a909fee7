"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { useAuth } from "@clerk/nextjs";
import { ArrowRight } from "lucide-react";
import { useState, useEffect } from "react";
import { CyberpunkCursor } from "@/components/ui/cursor";
import { HeroSection } from "@/components/landing/hero-section";
import { FloatingNotification } from "@/components/ui/floating-notification";
import dynamic from "next/dynamic";

// Lazy load heavy components
const FeatureCard = dynamic(
    () => import("@/components/ui/feature-card").then((mod) => mod.FeatureCard),
    { ssr: true }
);
const TestimonialCard = dynamic(
    () =>
        import("@/components/ui/testimonial-card").then(
            (mod) => mod.TestimonialCard
        ),
    { ssr: true }
);

export default function Home() {
    const [isMounted, setIsMounted] = useState(false);
    const { isLoaded, userId } = useAuth();
    const isAuthenticated = isLoaded && userId && isMounted;

    useEffect(() => setIsMounted(true), []);

    return (
        <div className="flex min-h-screen flex-col bg-background">
            {!isAuthenticated && <FloatingNotification />}

            <div className="pointer-events-none fixed inset-0 z-30">
                <CyberpunkCursor />
            </div>

            <header className="sticky top-0 z-40 border-b border-border bg-background/90 backdrop-blur-md">
                <div className="container flex h-16 items-center justify-between py-4">
                    <div className="flex items-center gap-2">
                        <span className="text-xl font-bold relative">
                            <span className="text-primary">Job</span>
                            <span className="text-accent">Craft</span>
                            <div className="absolute -top-1 -right-3 w-2 h-2 bg-primary rounded-full animate-ping" />
                        </span>
                    </div>
                    <nav className="flex items-center gap-4">
                        <ThemeToggle />
                        {isAuthenticated ? (
                            <Link href="/dashboard">
                                <Button className="h-12 inline-flex animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-400 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50">
                                    Go to Dashboard{" "}
                                    <ArrowRight className="ml-2 h-4 w-4" />
                                </Button>
                            </Link>
                        ) : (
                            <>
                                <Link href="/sign-in">
                                    <Button variant="outline" className="h-12">
                                        Sign In
                                    </Button>
                                </Link>
                                <Link href="/sign-up">
                                    <Button className="inline-flex h-12 animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-400 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50">
                                        Get Started
                                    </Button>
                                </Link>
                            </>
                        )}
                    </nav>
                </div>
            </header>

            <main className="flex-1 text-white">
                <HeroSection isAuthenticated={!!isAuthenticated} />

                {/* How it Works Section with Enhanced Visuals */}
                <section className="py-24 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent"></div>
                    <div className="container relative">
                        <div className="text-center mb-16">
                            <span className="inline-block text-sm font-bold text-gradient-pink-cyan uppercase tracking-widest mb-3">
                                Process
                            </span>
                            <h2 className="text-4xl font-bold mb-4">
                                How{" "}
                                <span className="text-primary">It Works</span>
                            </h2>
                            <p className="text-muted-foreground max-w-xl mx-auto">
                                Your journey to better job applications in three
                                simple steps
                            </p>
                        </div>
                        <div className="grid md:grid-cols-3 gap-8 relative">
                            {steps.map((step, index) => (
                                <div key={index} className="relative group">
                                    <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-accent/5 to-transparent rounded-2xl -rotate-2 group-hover:rotate-0 transition-transform duration-300"></div>
                                    <div className="relative bg-card/50 p-8 rounded-xl border border-border hover:border-primary/50 transition-all duration-300">
                                        <div className="absolute -top-4 -left-4 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-bold">
                                            {index + 1}
                                        </div>
                                        <div className="mb-4">{step.icon}</div>
                                        <h3 className="text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                                            {step.title}
                                        </h3>
                                        <p className="text-muted-foreground">
                                            {step.description}
                                        </p>
                                    </div>
                                </div>
                            ))}
                            <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-primary/20 to-transparent -z-10 hidden md:block"></div>
                        </div>
                    </div>
                </section>

                {/* Features Section with Enhanced Cards */}
                <section id="features" className="relative py-24 lg:py-32">
                    <div className="absolute inset-0 bg-muted/50 skew-y-3 origin-bottom-left"></div>
                    <div className="container relative">
                        <div className="text-center mb-16">
                            <span className="inline-block text-sm font-bold text-gradient-pink-cyan uppercase tracking-widest mb-3">
                                Features
                            </span>
                            <h2 className="text-4xl font-bold mb-4">
                                Not Just Another{" "}
                                <span className="text-accent">Job App</span>
                            </h2>
                            <p className="text-muted-foreground max-w-xl mx-auto">
                                Reinvent how you apply for jobs with our
                                cutting-edge AI tools
                            </p>
                        </div>
                        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-3">
                            {featureCards.map((feature, index) => (
                                <div
                                    key={index}
                                    className="group relative overflow-hidden rounded-xl bg-card p-8 hover:shadow-xl transition-all duration-300 border border-border hover:border-primary/50"
                                >
                                    <div className="absolute top-0 left-0 h-1 w-full bg-gradient-to-r from-primary to-accent transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                                    <div className="absolute -right-20 -bottom-20 w-40 h-40 bg-primary/5 rounded-full blur-3xl group-hover:bg-accent/5 transition-colors duration-300"></div>
                                    <div className="mb-5 inline-block rounded-lg bg-primary/10 p-3 text-primary relative z-10">
                                        {feature.icon}
                                    </div>
                                    <h3 className="mb-3 text-xl font-semibold relative z-10">
                                        {feature.title}
                                    </h3>
                                    <p className="text-muted-foreground relative z-10">
                                        {feature.description}
                                    </p>
                                    <div className="mt-6 flex items-center gap-2 text-sm text-primary/80">
                                        <span>Learn more</span>
                                        <ArrowRight className="h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section with Enhanced Design */}
                <section className="py-24 relative">
                    <div className="absolute inset-0 bg-muted/70">
                        <div className="absolute inset-0 opacity-30">
                            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary to-transparent"></div>
                            <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent"></div>
                        </div>
                    </div>
                    <div className="container relative">
                        <div className="max-w-3xl mx-auto text-center">
                            <span className="inline-block text-sm font-bold text-gradient-pink-cyan uppercase tracking-widest mb-3">
                                {isAuthenticated
                                    ? "Ready to Continue?"
                                    : "Ready to Start?"}
                            </span>
                            <h2 className="text-5xl font-bold mb-6">
                                {isAuthenticated ? (
                                    <>
                                        Continue Your <br />
                                        <span className="text-gradient-pink-cyan">
                                            Job Search Journey
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        Launch Your <br />
                                        <span className="text-gradient-pink-cyan">
                                            Dream Career
                                        </span>
                                    </>
                                )}
                            </h2>
                            <p className="text-xl text-muted-foreground mb-10 max-w-2xl mx-auto">
                                {isAuthenticated
                                    ? "Continue working on your applications and improving your career prospects."
                                    : "Transform your job search with AI-powered tools that help you stand out from the crowd."}
                            </p>
                            <Link
                                href={
                                    isAuthenticated
                                        ? "/dashboard/resumes"
                                        : "/sign-up"
                                }
                            >
                                <Button
                                    size="lg"
                                    className="inline-flex animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-10 py-6 font-medium text-slate-400 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50"
                                >
                                    {isAuthenticated
                                        ? "Manage Your Resumes"
                                        : "Get Started For Free"}
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Button>
                            </Link>
                            {!isAuthenticated && (
                                <p className="text-muted-foreground mt-4 text-sm">
                                    No credit card required • Free plan
                                    available
                                </p>
                            )}
                        </div>
                    </div>
                </section>
            </main>

            <footer className="border-t border-border py-8 bg-card/80 text-muted-foreground">
                <div className="container">
                    <div className="flex flex-col items-center justify-center text-center gap-4">
                        <div className="flex items-center gap-1">
                            <span className="text-xl font-bold">
                                <span className="text-primary">Job</span>
                                <span className="text-accent">Craft</span>
                            </span>
                        </div>
                        <p className="text-sm max-w-md mx-auto">
                            AI-powered job application assistant that helps you
                            create personalized application materials.
                        </p>
                        <div className="text-xs text-muted-foreground/70 mt-4">
                            &copy; {new Date().getFullYear()} JobCraft. All
                            rights reserved.
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
}

// Feature cards data
const featureCards = [
    {
        title: "Resume Management",
        description:
            "Upload, parse, and manage multiple versions of your resume for different job applications.",
        icon: (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <polyline points="14,2 14,8 20,8" />
                <line x1="16" y1="13" x2="8" y2="13" />
                <line x1="16" y1="17" x2="8" y2="17" />
                <polyline points="10,9 9,9 8,9" />
            </svg>
        ),
    },
    {
        title: "AI-Powered Cover Letters",
        description:
            "Generate personalized cover letters that highlight your relevant experience for each position.",
        icon: (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                <path d="M8 10h.01" />
                <path d="M12 10h.01" />
                <path d="M16 10h.01" />
            </svg>
        ),
    },
    {
        title: "Application Tracking",
        description:
            "Keep track of all your applications, interviews, and follow-ups in one organized dashboard.",
        icon: (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4" />
                <rect width="6" height="7" x="9" y="9" rx="2" />
            </svg>
        ),
    },
];

// Updated steps data with icons
const steps = [
    {
        title: "Upload Your Resume",
        description:
            "Start by uploading your existing resume or create a new one from scratch using our AI-powered tools.",
        icon: (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <polyline points="14 2 14 8 20 8" />
                <path d="M12 12v6" />
                <path d="M9 15l3-3 3 3" />
            </svg>
        ),
    },
    {
        title: "Customize & Enhance",
        description:
            "Our AI analyzes job descriptions and helps you tailor your application materials for maximum impact.",
        icon: (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
            </svg>
        ),
    },
    {
        title: "Apply with Confidence",
        description:
            "Submit your applications knowing they're optimized for both human recruiters and ATS systems.",
        icon: (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                <polyline points="22 4 12 14.01 9 11.01" />
            </svg>
        ),
    },
];
