import { db } from "@/lib/db";

export default async function Instruments() {
    // Create a type for the instruments
    type Instrument = {
        id: number;
        name: string;
    };

    // This is a placeholder - in a real app, you would fetch from your database
    // For example, using Drizzle ORM with your database
    const instruments: Instrument[] = [
        { id: 1, name: "violin" },
        { id: 2, name: "viola" },
        { id: 3, name: "cello" },
        { id: 4, name: "double bass" },
        { id: 5, name: "flute" },
        { id: 6, name: "clarinet" },
        { id: 7, name: "oboe" },
        { id: 8, name: "bassoon" },
        { id: 9, name: "trumpet" },
        { id: 10, name: "trombone" },
        { id: 11, name: "french horn" },
        { id: 12, name: "tuba" },
    ];

    return (
        <div className="container py-6">
            <h1 className="text-3xl font-bold mb-6">Musical Instruments</h1>

            {instruments && instruments.length > 0 ? (
                <div className="grid gap-4">
                    {instruments.map((instrument: Instrument) => (
                        <div
                            key={instrument.id}
                            className="p-4 border rounded-lg bg-card"
                        >
                            <h2 className="text-xl font-medium">
                                {instrument.name}
                            </h2>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="p-4 border rounded-lg bg-card">
                    <p>No instruments found.</p>
                </div>
            )}
        </div>
    );
}
